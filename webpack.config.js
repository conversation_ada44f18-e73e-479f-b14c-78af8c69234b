let path = require('path')
let webpack = require('webpack')
const NODE_ENV = process.env.NODE_ENV
const UglifyJsPlugin = require("uglifyjs-webpack-plugin")
const VueLoaderPlugin = require('vue-loader/lib/plugin')
let HTMLWebpackPlugin = require("html-webpack-plugin");

module.exports = {
    entry: {
      index: "./src/index.js"
    },
    output: {
      // path:  __dirname + "/CourseLearning",
        path: path.join(__dirname,"./CourseLearning"),
      // 输出文件的目标路径
      filename: "app.js",
      publicPath: "./",
      libraryTarget: "umd",
      library: "CourseLearning",
    },
    plugins: [
        new VueLoaderPlugin(),
          new HTMLWebpackPlugin({
            title: "VICDemo",
            filename: "index.html"
          })
    ],
    mode:"development",
    module: {
        rules: [{
                test: /\.css$/,
                use: [
                    'vue-style-loader',
                    'css-loader'
                ],
            },
            {
                test: /\.(ttf|woff|eot)$/,
                use: [{
                    loader: "file-loader",
                    options: {
                        outputPath: 'fonts'
                    }
                }]
            }, {
                test: /\.scss$/,
                use: [
                    'vue-style-loader',
                    'css-loader',
                    'sass-loader'
                ],
            },
            {
                test: /\.sass$/,
                use: [
                    'vue-style-loader',
                    'css-loader',
                    'sass-loader?indentedSyntax'
                ],
            },
            {
                test: /\.vue$/,
                loader: 'vue-loader',
                options: {
                    loaders: {
                        // Since sass-loader (weirdly) has SCSS as its default parse mode, we map
                        // the "scss" and "sass" values for the lang attribute to the right configs here.
                        // other preprocessors should work out of the box, no loader config like this necessary.
                        'scss': [
                            'vue-style-loader',
                            'css-loader',
                            'sass-loader'
                        ],
                        'sass': [
                            'vue-style-loader',
                            'css-loader',
                            'sass-loader?indentedSyntax'
                        ]
                    }
                    // other vue-loader options go here
                }
            },
            {
                test: /\.js$/,
                // loader: 'babel-loader',
                exclude: /node_modules/,
                use: {
                    loader: 'babel-loader',
                    options: {
                        plugins: ["@babel/plugin-transform-runtime"]
                    }
                }
            },
            {
                test: /\.(png|jpg|gif|svg)$/,
                loader: 'file-loader',
                options: {
                    name: '[name].[ext]?[hash]',
                    outputPath:"images"
                }
            }
        ]
    },
    resolve: {
        alias: {
            'vue$': 'vue/dist/vue.esm.js'
        },
        extensions: ['*', '.js', '.vue', '.json']
    },
    devServer: {
        historyApiFallback: true,
        noInfo: true,
        overlay: true
    },
    performance: {
        hints: false
    },
  devtool:'source-map',
  optimization:{
    minimizer:[
      new UglifyJsPlugin({
        sourceMap:true,
        uglifyOptions: {
          output: {
            comments: false
          },
          warnings: false,
          compress: {
            drop_debugger: true,
            drop_console: false
          }
        }
      })
    ]
  },
}
