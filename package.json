{"name": "demo", "description": "A Vue.js project", "version": "1.0.10", "author": "wchbrad <<EMAIL>>", "license": "MIT", "private": false, "main": "index.js", "scripts": {"dev": "cross-env NODE_ENV=development webpack-dev-server --hot --port 3000", "build": "cross-env NODE_ENV=production webpack --progress --hide-modules"}, "dependencies": {"@babel/runtime": "^7.21.0", "@wchbrad/vue-easy-tree": "^1.0.10", "axios": "0.19.2", "color": "0.11.0", "element-ui": "2.13.2", "gl-matrix-double": "^2.3.1", "lodash": "4.17.4", "vic": "6.6.0", "vue": "2.6.11", "vue-color": "^2.8.1", "vue-virtual-scroller": "^1.0.10"}, "devDependencies": {"@babel/core": "^7.19.3", "@babel/plugin-transform-runtime": "^7.21.0", "@babel/preset-env": "^7.19.4", "babel-loader": "^8.2.5", "core-js": "^3.6.5", "cross-env": "^3.1.3", "css-loader": "^2.1.0", "file-loader": "^1.1.11", "html-webpack-plugin": "^3.2.0", "sass": "^1.55.0", "sass-loader": "^7.1.0", "uglifyjs-webpack-plugin": "^2.1.1", "vue-loader": "^15.7.0", "vue-template-compiler": "2.6.11", "vue-template-es2015-compiler": "^1.6.0", "webpack": "^4.14.0", "webpack-cli": "^3.0.8", "webpack-dev-server": "^3.1.11"}}