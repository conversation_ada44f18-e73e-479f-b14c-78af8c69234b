import MATRIX from "gl-matrix-double";
import color from "color";
import _ from "lodash";

/**
 * @class osgb新模型向render3DView模型的适配器
 */
export default class OSGBModelsAdapter {
	constructor() {
		this.nodeArr = [];
		this.node = null;
		this.view3d = null;
		this.labelInited = false;
		this.allNodeArr = [];
		this.transformationNode = null;
		this.rootNode = null;
		this.currentMatrix = MATRIX.mat4.create();
		// 保存基础变换矩阵（初始位置、旋转、缩放等）
		this.baseMatrix = MATRIX.mat4.create();
		// 保存用户变换矩阵
		this.userMatrix = MATRIX.mat4.create();
	}

	// 统一的变换更新方法
	updateTransformation(userMatrix) {
		if (!this.transformationNode || !this.view3d) {
			console.warn('Transformation node or view3d not initialized');
			return;
		}

		// 保存用户变换矩阵
		MATRIX.mat4.copy(this.userMatrix, userMatrix);

		// 组合变换：最终矩阵 = 基础变换 × 用户变换
		const finalMatrix = MATRIX.mat4.create();
		MATRIX.mat4.multiply(finalMatrix, this.baseMatrix, this.userMatrix);

		// 更新当前矩阵
		MATRIX.mat4.copy(this.currentMatrix, finalMatrix);

		console.log('基础变换矩阵:', this.baseMatrix);
		console.log('用户变换矩阵:', this.userMatrix);
		console.log('最终变换矩阵:', this.currentMatrix);

		// 移除旧节点
		this.view3d.removeNode(this.transformationNode, this.rootNode);
		this.view3d.NodeFactory.releaseNode([this.transformationNode]);

		// 创建新的变换节点
		this.transformationNode = this.view3d.NodeFactory.createTransformation(
			this.currentMatrix,
			this.allNodeArr,
			"OSGBModel_Transform_" + Date.now()
		);

		// 添加到场景
		this.view3d.addNode(this.transformationNode, this.rootNode);
	}

	// 简化的变换节点创建 - 整合所有初始变换
	async createTransformationNode(nodeArray) {
		const initialMatrix = MATRIX.mat4.create();

		// 基础变换
		MATRIX.mat4.translate(
			initialMatrix,
			initialMatrix,
			MATRIX.vec3.fromValues(3187965.25, -1288959.6875, 92253.724609375)
		);
		MATRIX.mat4.rotateZ(initialMatrix, initialMatrix, 1.16667);
		MATRIX.mat4.scale(initialMatrix, initialMatrix, MATRIX.vec3.fromValues(1000, 1000, 1000));

		// 整合 OGF 模型的坐标转换
		const OSGBModelPoint = [3141838, -1258974, 735306];
		const OGFModelPoint = [505338219, 4948248351, 536987];
		const pointVec = [
			Number(OGFModelPoint[0] - OSGBModelPoint[0] + 1010),
			Number(OGFModelPoint[1] - OSGBModelPoint[1] + 1000),
			Number(OGFModelPoint[2] - OSGBModelPoint[2] - 100)
		];
		const pointVec3 = MATRIX.vec3.fromValues(...pointVec);

		// 应用 OGF 变换
		const ogfMatrix = MATRIX.mat4.create();
		MATRIX.mat4.fromTranslation(ogfMatrix, pointVec3);

		// 围绕点旋转
		const pivotPoint = OGFModelPoint;
		const angleDegrees = -67;
		const radians = this.degreesToRadians(angleDegrees);

		const transToOrigin = MATRIX.mat4.create();
		MATRIX.mat4.fromTranslation(transToOrigin, [-pivotPoint[0], -pivotPoint[1], -pivotPoint[2]]);

		const rotZ = MATRIX.mat4.create();
		MATRIX.mat4.rotateZ(rotZ, rotZ, radians);

		const transBack = MATRIX.mat4.create();
		MATRIX.mat4.fromTranslation(transBack, pivotPoint);

		// 组合所有变换：基础变换 * OGF变换 * 旋转变换
		const rotationMatrix = MATRIX.mat4.create();
		MATRIX.mat4.multiply(rotationMatrix, rotZ, transToOrigin);
		MATRIX.mat4.multiply(rotationMatrix, transBack, rotationMatrix);

		MATRIX.mat4.multiply(ogfMatrix, rotationMatrix, ogfMatrix);
		MATRIX.mat4.multiply(initialMatrix, ogfMatrix, initialMatrix);

		// 保存基础变换矩阵
		MATRIX.mat4.copy(this.baseMatrix, initialMatrix);
		// 初始化用户变换矩阵为单位矩阵
		MATRIX.mat4.identity(this.userMatrix);
		// 当前矩阵等于基础变换矩阵
		MATRIX.mat4.copy(this.currentMatrix, initialMatrix);

		console.log('初始化基础变换矩阵:', this.baseMatrix);

		this.transformationNode = this.view3d.NodeFactory.createTransformation(
			this.currentMatrix,
			nodeArray,
			"OSGBModel_Root"
		);

		return this.transformationNode;
	}

	// 重置用户变换到初始状态
	resetUserTransformation() {
		if (!this.transformationNode || !this.view3d) {
			console.warn('Transformation node or view3d not initialized');
			return;
		}

		// 重置用户变换矩阵为单位矩阵
		MATRIX.mat4.identity(this.userMatrix);

		// 当前矩阵等于基础变换矩阵
		MATRIX.mat4.copy(this.currentMatrix, this.baseMatrix);

		console.log('重置用户变换，恢复到基础变换状态');

		// 移除旧节点
		this.view3d.removeNode(this.transformationNode, this.rootNode);
		this.view3d.NodeFactory.releaseNode([this.transformationNode]);

		// 创建新的变换节点
		this.transformationNode = this.view3d.NodeFactory.createTransformation(
			this.currentMatrix,
			this.allNodeArr,
			"OSGBModel_Transform_" + Date.now()
		);

		// 添加到场景
		this.view3d.addNode(this.transformationNode, this.rootNode);
	}

	// 移除多余的transFormationNode方法，使用updateTransformation替代

	/**
	 * 模型添加之后的视角强制定位
	 * @returns {Promise<unknown>}
	 */
	async focusViewPoint() {
			this.view3d.calcAABB([this.node], (err, aabb) => {
				if (err) {
					console.log(err);
				} else {
					const center = aabb.min;
					this.view3d.acquireViewpoint((err, viewPoint) => {
						viewPoint.center = center;
						const eye = [...center];
						viewPoint.eye = MATRIX.vec3.fromValues(
							eye[0] - 100,
							eye[1] - 100,
							eye[2] + 100
						);
						this.view3d.translateViewpoint(viewPoint);
						this.viewPoint = viewPoint;
					});
				}
			});
	}

	/**
	 * @desc 模型添加
	 * @param filePath {String} 文件在渲染服务器上的绝对路径
	 * @returns {Promise<unknown>}
	 */
	async loadModel(filePath) {
		return new Promise((resolve, reject) => {
			this.view3d.NodeFactory.loadModel({
				modelUrl : filePath,
				callback : (err, node) => {
					if (err) {
						reject(err);
					} else {
						this.node = node;
						resolve(node);
					}
				}
			});
		});
	}

	/**
	 * @desc 将OSGBModel转换为OSGBViewModel
	 * @param originModel {OSGBModel}
	 * @param options
	 * @returns {Promise<null>}
	 */
	async adapt(osgbModelList, view3D, rootNode, center) {
		this.center = center
		this.view3d = view3D;
		this.rootNode = rootNode;
		this.nodeArr = [];
		const pathArr = [];
		osgbModelList.forEach(modelName => {
			pathArr.push("file:///D://BrsDemo//0622OSGB//Production_1//Data//" + modelName);
		})
		for (let i = 0; i < pathArr.length; i++) {
			this.nodeArr.push(this.loadModel(pathArr[i]));
		}
		const nodeArr = await Promise.all(this.nodeArr);

		this.allNodeArr = nodeArr;
		const node = await this.createTransformationNode(nodeArr);

		this.node = node;

		// 注释掉这行，避免创建额外的变换层级
		// this.fromTranslationOGFModel();

		this.view3d.addNode(node, rootNode);
		await this.focusViewPoint();
	}

	fromTranslationOGFModel() {
		// ① 目标点（OSGB）和源点（OGF）
		const OSGBModelPoint = [3141838, -1258974, 735306];
		const OGFModelPoint = [505338219, 4948248351, 536987];

		const pointVec = [
			Number(OGFModelPoint[0] - OSGBModelPoint[0] + 1010)
			, Number(OGFModelPoint[1] - OSGBModelPoint[1] + 1000)
			, Number(OGFModelPoint[2] - OSGBModelPoint[2] - 100)
		];
		const pointVec3 = MATRIX.vec3.fromValues(...pointVec);

		/* -------------------------------------------------
         * 2. 构造 4×4 变换矩阵：先平移，再（可选）旋转
         * ------------------------------------------  ------- */
		let mat4 = MATRIX.mat4.create();
		MATRIX.mat4.fromTranslation(mat4, pointVec3);   // 先做平移

		// 如果两套坐标系还存在朝向差，需要保留旋转
		// 没有的话可以直接删掉下面两句
		const rotZ = MATRIX.mat4.create();
		MATRIX.mat4.rotateZ(rotZ, rotZ, this.degreesToRadians(0));
		MATRIX.mat4.multiply(mat4, rotZ, mat4);         // 旋转 * 平移

		const transformationNode = this.view3d.NodeFactory.createTransformation(
			mat4,
			[this.node],
			"Transformation_OSGBNode"
		);
		// this.view3d.addNode(transformationNode, renderer3DView.vicWrapper.modelRootNode);

		// --------------------
		// const pivotPoint = [504542464,4947171387,568011];  // 你要围绕的点（世界坐标）
		const pivotPoint = OGFModelPoint;

		const rotateNode = this.rotateNodeAroundPointY(transformationNode, pivotPoint, -67);

	}

	rotateNodeAroundPointY(targetNode, pivotPoint, angleDegrees, nodeName = "Rotate_Z_Around_Point") {
		// pivotPoint 是你想围绕旋转的固定点，例如 [505338219, 4948248351, 536987]
		const radians = this.degreesToRadians(angleDegrees);

		// T(-P): 将模型平移到以 pivotPoint 为中心的局部坐标系
		const transToOrigin = MATRIX.mat4.create();
		MATRIX.mat4.fromTranslation(transToOrigin, [-pivotPoint[0], -pivotPoint[1], -pivotPoint[2]]);

		// R: 绕 Y 轴旋转（局部）
		const rotY = MATRIX.mat4.create();
		MATRIX.mat4.rotateZ(rotY, rotY, radians);

		// T(P): 再把模型从局部坐标系平移回世界坐标
		const transBack = MATRIX.mat4.create();
		MATRIX.mat4.fromTranslation(transBack, pivotPoint);

		// 最终组合矩阵：T(P) * R * T(-P)
		const resultMat = MATRIX.mat4.create();
		MATRIX.mat4.multiply(resultMat, rotY, transToOrigin);       // R * T(-P)
		MATRIX.mat4.multiply(resultMat, transBack, resultMat);      // T(P) * (...)

		// 创建 transformation node 并加入场景
		const rotNode = this.view3d.NodeFactory.createTransformation(
			resultMat,
			[targetNode],
			nodeName
		);

        this.transNode = rotNode;
		this.view3d.addNode(rotNode, this.rootNode);
		console.log(`✅ 节点 ${nodeName} 已绕点 ${pivotPoint} 的 Z 轴旋转 ${angleDegrees} 度`);

		return rotNode;
	}

	// 角度转弧度
	degreesToRadians(angle) {
		return angle * Math.PI / 180.0;
	}



	/**
     * 创建线段节点
     *
     * @param startPoint 起始点坐标
     * @param endPoint 终止点坐标
     * @param lineWidth 线宽
     * @param lineColor 线的颜色
     *
     * @return 线段模型节点
     */
    createLine(startPoint, endPoint, lineWidth, lineColor) {
        const distance = this.distance(startPoint, endPoint);
        console.log(distance);
        this.distances = this.distances + distance;
        console.log(this.distances);
        // 线条效果
        const lineStyle = {
            width : 2,
            color : color("rgb(14, 175, 82)").alpha(1),
            type : "solid"
        };

        const lineNode = this.view3d.NodeFactory.createLine(
            MATRIX.vec3.fromValues(startPoint[0], startPoint[1], startPoint[2]),
            MATRIX.vec3.fromValues(endPoint[0], endPoint[1], endPoint[2]), lineStyle);
        this.view3d.addNode(lineNode, this.view3d.rootNode);
        return lineNode;
    }


	/**
     * 计算两点距离
     * @startPoint  起始点
     * @endPoint  终点
     */
    distance(startPoint, endPoint) {
        startPoint = MATRIX.vec3.fromValues(startPoint[0], startPoint[1], startPoint[2]);
        endPoint = MATRIX.vec3.fromValues(endPoint[0], endPoint[1], endPoint[2]);
        var distance = MATRIX.vec3.distance(startPoint, endPoint);
        distance = distance / 1000;
        distance = distance.toFixed(2);
        // distance = parseFloat(distance.toString()); // 当前线段距离
        return Number(distance);
    }
}



// WEBPACK FOOTER //
// ./src/app/adapter/OSGBModelsAdapter.js
