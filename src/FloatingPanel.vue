<template>
    <div style="position: absolute">
        <slot></slot>
    </div>
</template>

<script>

import MATRIX from "gl-matrix-double";
import $ from "jquery";

export default {
    name: "FloatingPanel",
    props: ["nodes", "view3D", "position", "scene3dId"],
    data() {
        return {
            isInit: false,
            state: false,
            currentView3D: this.view3D
        };
    },
    // watch: {
    //     view3D: {
    //         async handler(newVal, oldPlayer){
    //             if (newVal && !this.currentView3D){
    //                 this.currentView3D = newVal;
    //                 this.init(null, this.position)
    //             }
    //         },
    //         deep: true,
    //         immediate:true
    //     },
    //     nodes: {
    //         async handler(newVal, oldPlayer){
    //             console.log(newVal);
    //             if (newVal && !this.isInit){
    //                 this.init(newVal);
    //             }
    //         },
    //         deep: true,
    //         immediate:true
    //     }
    // },
    mounted() {
        $(this.$el).hide();
        if (!this.view3D){
            throw new Error("[View3D] 对象不能为空")
        }
        if (!this.position){
            throw new Error("[View3D] 坐标不能为空")
        }

        this.init(null, this.position)
    },
    methods: {
        /**
         * 在三维空间固定一个悬浮的标签或者窗口，随着模型的缩放移动而变化
         * @param nodes 在某个Node上悬浮窗口
         * @param point 窗口悬浮在某个点
         */
        async init(nodes, point){
            const this_ = this;
            let center = null;
            if(this.state){
                return;
            }
            if (nodes){
                center = await new Promise((resolve, reject) => {
                    this_.currentView3D.calcAABB(nodes, (err, aabb) => {
                        const min = aabb.min;
                        const max = aabb.max;
                        resolve(MATRIX.vec3.fromValues((min[0]+max[0])/2, (min[1]+max[1])/2, max[2]));
                    });
                })
            }else {
                center = point;
            }
            if (!this_.currentView3D){
                return;
            }
            this_.createListener(center, (pos) => {
                if (!this_.state){
                    $(this_.$el).show();
                    this_.state = true;
                }
                const domWidth = $(this_.$el).width();
                const domHeight = $(this_.$el).height();

                const scene3dDivId = this_.scene3dId ? this_.scene3dId : "bcd";

                // 获取三维区域的宽高和偏移量
                const scene3dWidth = $("#" + scene3dDivId).width();
                const scene3dHeight = $("#" + scene3dDivId).height();
                const scene3dOffetLeft = $("#" + scene3dDivId).offset().left;
                const scene3dOffetTop = $("#" + scene3dDivId).offset().top;

                // 如果坐标超出屏幕宽度，则隐藏
                const positionLeft = pos[0] - (domWidth / 2);
                const positionTop = scene3dHeight - pos[1] - domHeight + scene3dOffetTop;

                if (positionLeft > scene3dOffetLeft
                    && positionLeft < scene3dOffetLeft + scene3dWidth - domWidth
                    && positionTop > scene3dOffetTop
                    && positionTop < scene3dHeight + scene3dOffetTop - domHeight){
                    if (pos[2] < 1){
                        // 将DOM元素定位到对应位置
                        $(this_.$el).offset({
                            "left" : positionLeft,
                            "top" : positionTop
                        });
                        $(this_.$el).show();
                    } else {
                        $(this_.$el).hide();
                    }
                }else {
                    $(this_.$el).hide();
                }
            });
        },
        createListener(center, callback){
            this.currentView3D.registerVpMatrixChangedCallback((err, viewMatrix, projectionMatrix, windowsMatrix) => {
                const pos = this.currentView3D.worldCoordToScreenCoord(center, viewMatrix, projectionMatrix, windowsMatrix);
                callback(pos);
            })
        }
    }
}
</script>

<style scoped>

</style>
