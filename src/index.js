import $ from "jquery";
import Vue from "vue";
import "./css/index.css";
import ElementUI from "element-ui";
import "element-ui/lib/theme-chalk/index.css";
import Render3D from "./Render3D";
import Title from "./Title";
Vue.use(ElementUI);
import osgbJson from "./osgb.json";

var Matrix = require("gl-matrix-double");
$(function () {
    const demoView = new DemoView();
    demoView.init();
});
export default class DemoView{
    constructor() {
        this.render3D = new Render3D(this.pickHandler);
    }
    async init(){
        $("body").append("<div id='demo_body'><div id='title'></div><div id='render_body'></div></div>");
        const name = "2121C3F0-E57E-48E5-B318-E08D2523DDA7.pgf";
        await this.render3D.init("render_body");
        const this_ = this;
        this.render3D.loadModel(name, function (center) {
            // 加载OSGB模型
            this_.render3D.loadOSGBModel(osgbJson.rootFileName, center);
            this_.render3D.locationNode("fbab0d83-4bbc-4f3b-a859-e71b5d1bd6db");
        });

        new Vue({
            el : "#title",
            components : {
                Title
            },
            data() {
                return {
                    render3D: this_.render3D
                };
            },
            template : "<Title :render3D='render3D'  ref='Title'/>",
            mounted() {
                this.$el.oncontextmenu = e => false;
            },
            methods:{
                startLearingScript(){

                }
            }
        });

    }

    pickHandler(msg){

    }
}
