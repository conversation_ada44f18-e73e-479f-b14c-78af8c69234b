
import { vec3 } from 'gl-matrix-double';
//     ```
//
// 其中，`vec3` 是 gl-matrix-double 库中的一种三维向量类型，代表三维空间中的一个点或向量。
// `vec3.create()` 用于创建一个新的三维向量，
// `vec3.sub(v1, v2, v3)` 用于计算向量 v2 - v3，
// `vec3.normalize(out, a)` 用于将向量 a 转化为单位向量，
// `vec3.scaleAndAdd(out, a, b, scale)` 用于计算 a + b * scale，
// `vec3.cross(out, a, b)` 用于计算向量 a 和 b 的叉积，
// `vec3.negate(out, a)` 用于计算向量 a 的负向量，
// `vec3.add(out, a, b)` 用于计算向量 a 和 b 的和。
//
// 函数 `calculateRectanglePoints` 根据矩形中心点 D 和 AB 的垂直向量 ABPerp 计算出矩形四个顶点的坐标，并将它们以顺时针的顺序返回。函数的输入参数为三点 A、B、C，矩形长宽 width 和 height。在函数体内，首先计算出 AB 和 AC 的单位向量，然后通过向量运算求出矩形中心点 D，再利用 D、AB 的垂直向量 ABPerp 和矩形长宽 width、height 计算出矩形四个顶点的坐标。
//
// 如果矩形的长度和宽度为0，则返回一个空数组。如果AB和AC存在线性相关的情况，即三点A、B、C共线，则返回矩形顶点 A、B、C 以及 D（即使矩形没有定义），此时函数所求的结果并不唯一。

//
//
// 其中，vec3是gl-matrix-double库中的向量类型，normalize函数用于将向量归一化，subtract函数用于计算两个向量的差，
// cross函数用于计算两个向量的叉积，add函数用于计算两个向量的和，scale函数用于将向量缩放。最后，我们需要处理向量为0的异常情况，避免出现-0的情况。

//```

//其中，`vec3.create()`用于创建三维向量，`vec3.subtract(a, b, c)`用于计算向量b-c得到向量a，`vec3.cross(a, b, c)`用于计算叉积，`vec3.scaleAndAdd(a, b, c, d)`用于计算向量b+c×d得到向量a，`vec3.exactEquals(a, b)`用于判断两个向量是否完全相等。需要注意，在这个函数中，我们假设矩形长宽都是正数。


// 首先需要确定矩形在AB平面上的方向向量，可以计算向量AB和向量AC的叉积得到。然后把矩形宽向量与方向向量叉积得到矩形高向量，再把宽向量乘上矩形宽的一半，高向量乘上矩形高的一半，得到矩形两个顶点的坐标，再分别沿着方向向量和高向量方向移动得到另外两个顶点的坐标。
//
// 如果矩形在AB平面上的方向向量和高向量有任意一个为零向量，则说明无法构造出符合要求的矩形，我们需要返回一个错误信息。
//
// 以下是完整的js代码实现：
//
// ```javascript

/**
 * 已知三维空间的三点ABC，ABC所在平面上有直角矩形p1,p2,p3,p4
 * p1和A点重合，
 * p1p2与AB同方向且共线，
 * p1p4垂直于AB，
 * p1p2 p4p3 为矩形宽，width，
 * p1 p4, p2p3为矩形长，length，
 * 求矩形 p1 p2 p3 p4坐标，坐标顺序和ABC的顺序一致
 * TODO 此方法没有做参数校验和特殊情况处理，请方法调用方根据实际情况处理
 *
 * 一些方法的解释
 * `vec3` 是 gl-matrix-double 库中的一种三维向量类型，代表三维空间中的一个点或向量
 * `vec3.create()` 用于创建一个新的三维向量
 * `vec3.sub(v1, v2, v3)` 用于计算向量 v2 - v3
 * `vec3.normalize(out, a)` 用于将向量 a 转化为单位向量
 * `vec3.scaleAndAdd(out, a, b, scale)` 用于计算 a + b * scale
 * `vec3.cross(out, a, b)` 用于计算向量 a 和 b 的叉积
 * `vec3.negate(out, a)` 用于计算向量 a 的负向量
 * `vec3.add(out, a, b)` 用于计算向量 a 和 b 的和
 * @param A
 * @param B
 * @param C
 * @param width
 * @param length
 * @returns {(*|vec3)[]}
 */
export function computeRectPoints(A, B, C, width, length) {
    // 计算向量 AB
    const AB = vec3.create();
    vec3.sub(AB, B, A);
    // 计算向量 AC
    const AC = vec3.create();
    vec3.sub(AC, C, A);
    // 计算平面法向量
    const normal = vec3.create();
    vec3.cross(normal, AB, AC);
    // 将矩形宽方向向量归一化
    const unitWidthVector = vec3.create();
    vec3.normalize(unitWidthVector, AB);

    // 计算矩形宽向量
    const widthVector = vec3.create();
    vec3.scale(widthVector, unitWidthVector, width);

    // 将矩形长方向向量归一化
    const unitLengthVector = vec3.create();
    vec3.cross(unitLengthVector, normal, unitWidthVector);
    vec3.normalize(unitLengthVector, unitLengthVector);
    // 计算矩形长向量
    const lengthVector = vec3.create();
    vec3.scale(lengthVector, unitLengthVector, length);
    // 计算矩形四个顶点坐标
    const p1 = A;
    const p2 = vec3.create();
    vec3.add(p2, p1, widthVector);
    const p3 = vec3.create();
    vec3.add(p3, p2, lengthVector);
    const p4 = vec3.create();
    vec3.subtract(p4, p3, widthVector);
    // 最终结果即为p1、p2、p3、p4的坐标
    return [A, p2, p3, p4];
}
