import COLOR from "color";
import MATRIX from "gl-matrix-double";

/**
 * 圆柱
 * 这个类用于绘制，构建一个圆柱
 * 除公有方法外，其他方法皆用Symbol修饰为私有方法
 *
 * *********************示例******************************
 * 如果要同步调用可以把下面的回调封装成Promise
 * const cylinderFactory = new CylinderFactory(view3D);
 * cylinderFactory.draw(async function (points) {
 *		console.log(points);
 *		const param = await cylinderFactory.createCylinderParam({
 *				startPt : points[0],
 *				endPt : points[1],
 *				pointNum : 30
 *		});
 *		console.log(param);
 *		cylinderFactory.createCylinderBox(param);
 *	});
 */

const planeVector = Symbol("a private function planeVector");
const calcCylinderParam = Symbol("a private function calcCylinderParam");
const createCircularInteraction = Symbol("a private function createCircularInteraction");


export default class CylinderFactory{
	constructor(view3D) {
		this.view3D = view3D;
		this.defaultInteraction = null;
	}

	/**
	 * 打开一个圆的绘制交互来绘制两个点
	 * @param {Function} callback 绘制完成回调
	 */
	draw(callback){
		const interation = this[createCircularInteraction]([], callback);
		this.defaultInteraction = this.view3D.swapInteraction(interation);
	}

	/**
	 * 中途退出绘制
	 */
	closeDraw(){
		if (this.defaultInteraction){
			this.view3D.swapInteraction(this.defaultInteraction);
		}
	}

	/**
	 * 通过画出圆的，起始位置和结束位置，和精密度，计算：圆的各个点
	 * @param {Object} options 参数
	 * @param {vec3} options.startPt 构建圆的起始位置
	 * @param {vec3} options.endPt 构建圆的起始位置
	 * @param {Number} options.pointNum 圆的精度，参照切圆法 默认值 30
	 * @param {Color} options.color 可选参 包围盒颜色， 默认 rgb(98,255,211)
	 * @param {Number} options.top 可选参 构建包围盒的向上超出值 默认值 40000
	 * @param {Number} options.bottom 可选参 构建包围盒的向下超出值 默认值 40000
	 * @returns {{color: (*|Color), bottomCenter: vec3, topRadius: number, topNormal, positions, bottomNormal, bottomRadius: number, topCenter: vec3}} 圆的构建参数
	 */
	async createCylinderParam(options) {
		return await new Promise((resolve, reject) => {
			this.view3D.calculateCircularPoints(options, (error, circularPointsArray) => {
				if (error) {
					console.error("计算圆，各个点，失败");
					return;
				}
				const bottomsPoints = [];
				const topPonits = [];
				for (const obj of circularPointsArray) {
					bottomsPoints.push(MATRIX.vec3.fromValues(obj[0], obj[1], obj[2] - 40000));
					topPonits.push(MATRIX.vec3.fromValues(obj[0], obj[1], obj[2] + 40000));
				}
				const param = this[calcCylinderParam]([options.startPt, options.endPt],
					topPonits, bottomsPoints, options);
				resolve(param);
			});
		});
	};


	/**
	 * 创建一个圆柱体
	 * @param object 根据 createCylinderParam 获取的参数
	 * @returns {{node: *, effect: *}}
	 */
	createCylinderBox(object) {
		const node = this.view3D.NodeFactory.createCylinder({
			bottomCenter : object.bottomCenter,
			bottomRadius : object.bottomRadius,
			bottomNormal : object.bottomNormal,
			topCenter : object.topCenter,
			topRadius : object.topRadius,
			topNormal : object.topNormal,
			color : object.color
		});
		let mat = MATRIX.mat4.create();
		const newNode = this.view3D.NodeFactory.createTransformation(mat, [node], "box");
		this.view3D.addNode(newNode, this.view3D.rootNode);
		const effect = this.view3D.EffectFactory.createTransparencyEffect({
			destNodes : [newNode],
			transparencyValue : 0.3
		});
		this.view3D.addEffect(effect);
		return{
			node: newNode,
			effect: effect
		}
	}


	/**
	 * 创建绘制圆形交互
	 * @param {Array<vec3>>} points 已经绘制的点
	 * @param {Function} callback 绘制完成回调
	 */
	[createCircularInteraction](points, callback) {
		const view3D = this.view3D;
		const this_ = this;
		const intOption = function (){
			const filterArray = [];
			filterArray.push(view3D.InteractionFactory.createNamedNodeFilter());
			const chainf = view3D.InteractionFactory.createChainFilter(filterArray);
			// 圆圈Tracker样式
			const blue = COLOR("rgb(0, 0, 255)").alpha(1);
			const lineStyle = {
				width : 4,
				color : blue,
				type : "solid"
			};
			const black = COLOR("rgb(0, 0, 0)").alpha(1);
			const white = COLOR("rgb(255, 255, 255)");
			const textFrame = {
				border : {
					color : black,
					width : 3
				},
				text : {
					size : 15,
					color : black,
					alignment : "left"
				},
				fillColor : white,
				padding : 2
			};
			const ptNum = points.length;
			const trackerArray = [];
			const pointNum = 30;
			// 点数为1时添加，圆圈Tracker
			if (ptNum === 1) {
				const startPoint = points[0];
				trackerArray.push(
					view3D.InteractionFactory.createCircularTracker(startPoint, {
						lineStyle : lineStyle,
						textFrameStyle : textFrame,
						pointNum : pointNum
					})
				); // tracker圆的精细度
			}
			trackerArray.push(view3D.InteractionFactory.createFollowRectTracker());
			const trackers = view3D.InteractionFactory.createCompositeTracker(trackerArray);
			return {
				pickFilter : chainf,
				tracker : trackers
			};
		};
		return view3D.InteractionFactory.createPointPickInteraction(async (e, msg) => {
			var pickPos;
			if (msg.button === 0 && msg.picked.length > 0) {
				for (let picked of msg.picked) {
					if (picked.nodePath.length >= 1) {
						// 获取选中的模型的node
						pickPos = msg.picked[0].position;
						points.push(pickPos);
                        if (points.length === 2) {
                            this_.view3D.swapInteraction(this_.defaultInteraction);
                            callback(points);
                            return;
                        }
						break;
					}
				}
			}
			const interation = this_[createCircularInteraction](points, callback);
			this_.view3D.swapInteraction(interation);
		}, intOption());
	}



	/**
	 * 根据圆的上下地面点集计算创建圆柱需要的信息
	 * @param positions 构建圆的两个点
	 * @param topPonits 圆柱顶面原点集合
	 * @param bottomsPoints 圆柱底面原点集合
	 * @param options 参数
	 * @param {Color} options.color 可选参 包围盒颜色， 默认 rgb(98,255,211)
	 * @param {Number} options.top 可选参 构建包围盒的向上超出值 默认值 40000
	 * @param {Number} options.bottom 可选参 构建包围盒的向下超出值 默认值 40000
	 * @returns {{color: (*|Color), bottomCenter: vec3, topRadius: number, topNormal, positions, bottomNormal, bottomRadius: number, topCenter: vec3}} 圆的构建参数
	 */
	[calcCylinderParam](positions, topPonits, bottomsPoints, options){
		const this_ = this;
		const centerPoint = MATRIX.vec3.fromValues((positions[0][0] + positions[1][0]) / 2,
			(positions[0][1] + positions[1][1]) / 2,
			(positions[0][2] + positions[1][2]) / 2);
		const radius = Math.sqrt(
			Math.pow(positions[0][0] - positions[1][0], 2) + Math.pow(positions[0][1] - positions[1][1], 2));

		// 圆柱体底面中心
		const bottom = options && options.bottom ? Number(options.bottom) : 40000;
		const bottomCenter = MATRIX.vec3.fromValues(centerPoint[0], centerPoint[1], centerPoint[2] - bottom);
		// 圆柱体地面半径
		const bottomRadius = radius / 2;
		// 圆柱体底面法向
		const bottomNormal = this_[planeVector](bottomsPoints[0], bottomsPoints[1], bottomsPoints[2]);
		// 圆柱体顶面中心
		const top = options && options.top ? Number(options.top) : 40000;
		const topCenter = MATRIX.vec3.fromValues(centerPoint[0], centerPoint[1], centerPoint[2] + top);
		// 圆柱体顶面半径
		const topRadius = radius / 2;
		// 圆柱体顶面法向
		const topNormal = this_[planeVector](topPonits[0], topPonits[1], topPonits[2]);
		// 圆柱体颜色(颜色中的alpha值无效)
		const color = options && options.color ? COLOR(options.color) : COLOR("rgb(98,255,211)");
		return {
			bottomCenter : bottomCenter,
			bottomRadius : bottomRadius,
			bottomNormal : bottomNormal,
			topCenter : topCenter,
			topRadius : topRadius,
			topNormal : topNormal,
			color : color,
			positions
		};
	}


	/**
	 * 通过三维坐标的三个点计算该三点构成的平面的法向量
	 * @param point1 坐标点1
	 * @param point2 坐标点2
	 * @param point3 坐标点3
	 * @returns {vec3}
	 */
	[planeVector](point1, point2, point3) {
		const A = point1[1] * (point2[2]
			- point3[2]) + point2[1] * (point3[2] - point1[2]) + point3[1] * (point1[2] - point2[2]);
		const B = point1[2] * (point2[0]
			- point3[0]) + point2[2] * (point3[0] - point1[0]) + point3[2] * (point1[0] - point2[0]);
		const C = point1[0] * (point2[1]
			- point3[1]) + point2[0] * (point3[1] - point1[1]) + point3[0] * (point1[1] - point2[1]);
		return MATRIX.vec3.fromValues(A, B, C);
	};

}
