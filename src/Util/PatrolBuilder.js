/* eslint-disable no-console */
/* eslint-disable no-magic-numbers */
import color from "color";
import _ from "lodash";
import MATRIX from "gl-matrix-double";

export default class PatrolBuilder{
    constructor(view3D) {
        this.view3D = view3D;
        this.distances = 0;
        this.points = [];
        this.pointNodes = [];
        this.pipeLineNodes = [];
        this.pipLineMap = new Map();
        this.currentPipName = "";
        this.defaultInteration = null;
    }
    draw(callback, startPoint, pipeName){
        const this_ = this;
        this_.points = [];
        this.pipeLineNodes = [];
        this.currentPipName = pipeName;
        if (startPoint){
            this.points.push(MATRIX.vec3.fromValues(startPoint[0], startPoint[1], startPoint[2]));
            this_.createFollowRectTrackerInteraction(2, "rgb(14, 175, 82)", callback,
                MATRIX.vec3.fromValues(startPoint[0], startPoint[1], startPoint[2]));
        }else {
            this_.createFollowRectTrackerInteraction(2, "rgb(14, 175, 82)", callback, null);
        }
    }

    /**
     * 展示站点轨迹
     * @param pipName
     * @param points
     */
    show(pipName, points){
        const nodes = [];
        for (let i = 0; i < points.length - 1; i++) {
            this.createPoint(points[i], 4, "rgb(14, 175, 82)");
            const node = this.createLine(points[i], points[i + 1], 4, "rgb(255, 0, 0)");
            nodes.push(node);
        }
        this.pipLineMap.set(pipName, nodes);
    }

    /**
     * 移除站点轨迹
     * @param pipNames
     */
    remove(pipNames){
        console.log(this.pipLineMap);
        if (pipNames){
            for (const pipName of pipNames) {
                try{
                    const nodes = this.pipLineMap.get(pipName);
                    for (const node of nodes) {
                        this.view3D.removeNode(node, this.view3D.rootNode);
                    }
                }catch (e){
                    console.log();
                }
            }
        }else {
            for (const key of this.pipLineMap.keys()) {
                try {
                    const nodes = this.pipLineMap.get(key);
                    for (const node of nodes) {
                        this.view3D.removeNode(node, this.view3D.rootNode);
                    }
                }catch (e){
                    console.log();
                }
            }
        }
        for (const pointNode of this.pointNodes) {
            try{
                this.view3D.removeNode(pointNode, this.view3D.rootNode);
            }catch (e){
                console.log();
            }
        }
    }


    /**
     * 创建并切换到跟随鼠标移动的小方框交互
     *   顺便保存好默认交互
     *
     * @param sideWidth 线段宽度(用于 createNewPointPickInteraction 函数)
     * @param sideColor 线段颜色(用于 createNewPointPickInteraction 函数)
     * @param callback
     */
    createFollowRectTrackerInteraction(sideWidth, sideColor, callback, pickPoint) {
        const view3D = this.view3D;
        const this_ = this;
        // 点选交互的回调函数
        const pickCallback = (err, msg) => {
            if (err) {
                throw Error(err.message);
            }
            if (msg.button === 2){
                this_.pipLineMap.set(this_.currentPipName, this_.pipeLineNodes);
                this_.swapInteraction(this_.defaultInteration);
                callback(this_.points,this_.distances);
                this_.distances = 0;
                return;
            }
            if(msg.picked.length > 0 && msg.button === 0) {
                pickPoint = msg.picked[0].position;
                console.log("==================绘制路线点", this.points.length - 1, "====================");
                console.log([...this_.points]);
                this.createPoint(pickPoint, 4, sideColor);
                this_.points.push(pickPoint);
                if (this.points.length >= 2){
                    this.createLine(pickPoint, this_.points[this.points.length - 2], 4, sideColor);
                }
                // callback(pickPoint);
                const pointPickInteraction
                    = this_.createNewPointPickInteraction(sideWidth, sideColor, pickPoint, pickCallback);
                this_.swapInteraction(pointPickInteraction);
            }
        };
        // 创建有跟随鼠标移动的小方框的点选交互
        const followRectTracker = this.createNewPointPickInteraction(sideWidth, sideColor, pickPoint, pickCallback);
        // 将默认的交互保存下来，将来还原交互使用
        this.defaultInteration = view3D.swapInteraction(followRectTracker);
    }


    /**
     * 创建新的点选交互
     *
     * @param sideWidth 线段宽度(用于 createTracker 函数)
     * @param sideColor 线段颜色(用于 createTracker 函数)
     * @param pickPoint 点坐标(x、y、z坐标集合)
     * @param callback
     *
     * @returns interaction 对象
     */
    createNewPointPickInteraction(sideWidth, sideColor, pickPoint, callback) {
        const this_ = this;
        // 构建参数 createPointPickInteraction 的 option 参数
        const option = {
            pickFilter : this_.createPickFilter(),
            tracker : this_.createTracker(sideWidth, sideColor, pickPoint)
        };

        // 创建并返回新的点选交互
        return this_.view3D.InteractionFactory.createPointPickInteraction(callback, option);
    }

    /**
     * 切换交互
     *
     * @param newInteraction 新的交互
     */
    swapInteraction(newInteraction) {
        if(newInteraction) {
            const oldInteraction = this.view3D.swapInteraction(newInteraction);
            // 释放掉旧的交互资源
            this.view3D.InteractionFactory.releaseInteraction([oldInteraction]);
        }
    }

    createPickFilter(){
        const this_ = this;
        // 过滤掉没有命名的过滤器
        const namedNodeFilter = this_.view3D.InteractionFactory.createNamedNodeFilter();
        // 过滤器数组
        const filters = [namedNodeFilter];

        // 返回链式过滤器
        return this_.view3D.InteractionFactory.createChainFilter(filters);
    }

    createTracker(sideWidth, sideColor, startPoint) {
        const this_ = this;
        // 创建跟随鼠标移动的小方框 Tracker
        const followRectTracker = this_.view3D.InteractionFactory.createFollowRectTracker();

        // 创建皮筋效果
        const black = color("rgb(0, 0, 0)").alpha(1);
        const white = color("rgb(255, 255, 255)");
        const lineStyle = {
            lineStyle : {
                width : sideWidth,
                color : color(sideColor).alpha(1),
                type : "solid"
            },
            textFrameStyle : {
                border : {color : black, width : 3},
                text : {size : 15, color : black, alignment : "left"},
                fillColor : white,
                padding : 2
            }
        };
        const pointTracker = startPoint === null
            ? null : this_.view3D.InteractionFactory.createDistanceToPointTracker(startPoint, lineStyle);

        // Tracker数组
        const trackers = _.compact([followRectTracker, pointTracker]);

        // 创建并返回复合 Tracker
        return this_.view3D.InteractionFactory.createCompositeTracker(trackers);
    }


    /**
     * 创建线段节点
     *
     * @param startPoint 起始点坐标
     * @param endPoint 终止点坐标
     * @param lineWidth 线宽
     * @param lineColor 线的颜色
     *
     * @return 线段模型节点
     */
    createLine(startPoint, endPoint, lineWidth, lineColor) {
        const distance = this.distance(startPoint, endPoint);
        console.log(distance);
        this.distances = this.distances + distance;
        console.log(this.distances);
        // 线条效果
        const lineStyle = {
            width : 2,
            color : color("rgb(14, 175, 82)").alpha(1),
            type : "solid"
        };

        const lineNode = this.view3D.NodeFactory.createLine(
            MATRIX.vec3.fromValues(startPoint[0], startPoint[1], startPoint[2]),
            MATRIX.vec3.fromValues(endPoint[0], endPoint[1], endPoint[2]), lineStyle);
        this.view3D.addNode(lineNode, this.view3D.rootNode);
        this.pipeLineNodes.push(lineNode);
        this.pipLineMap.set(this.currentPipName, this.pipeLineNodes);

        return lineNode;
    }

    /**
     * 添加点模型节点
     *
     * @param point 点坐标
     * @param pointSize 点的大小
     * @param pointColor 点的颜色
     *
     * @return 点模型节点
     */
    createPoint(point, pointSize, pointColor) {
        const pointStyle = {
            color : color("rgb(255, 189, 0)").alpha(0.8),
            size : 16
        };
        const pointNode = this.view3D.NodeFactory.createPoint(point, pointStyle);
        this.pointNodes.push(pointNode);
        this.view3D.addNode(pointNode, this.view3D.rootNode);
        return pointNode;
    }

    destory(){
        for (const pointNode of this.pointNodes) {
            this.view3D.removeNode(pointNode, this.view3D.rootNode);
        }
        this.remove();
        this.pointNodes = [];
        this.pipLineMap.clear();
        this.view3D = null;
        this.render3dView = null;
    }

    /**
     * 计算两点距离
     * @startPoint  起始点
     * @endPoint  终点
     */
    distance(startPoint, endPoint) {
        startPoint = MATRIX.vec3.fromValues(startPoint[0], startPoint[1], startPoint[2]);
        endPoint = MATRIX.vec3.fromValues(endPoint[0], endPoint[1], endPoint[2]);
        var distance = MATRIX.vec3.distance(startPoint, endPoint);
        distance = distance / 1000;
        distance = distance.toFixed(2);
        // distance = parseFloat(distance.toString()); // 当前线段距离
        return Number(distance);
    }
}
