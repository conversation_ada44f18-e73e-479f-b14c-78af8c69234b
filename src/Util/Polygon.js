import COLOR from "color";
import MATRIX from "gl-matrix-double";
const createRectangleInteraction = Symbol("a private function createRectangleInteraction");
export default class Polygon{
    constructor(view3D) {
        this.view3D = view3D;
        this.defaultInteraction = null;
    }

    /**
     * 开始绘制一个包围盒
     * @param {Function} callback 绘制完成三个点的回调
     * @param {String} direction horizontal: 水平  perpendicular 垂直
     * @returns {Interaction} 返回包围盒的八个点坐标
     */
    async draw(callback, direction){
        const interation = this[createRectangleInteraction]([], callback, direction);
        this.defaultInteraction = this.view3D.swapInteraction(interation);
    }

    create(points){
        const this_ = this;
        this.view3D.NodeFactory.createPolygon({
            points: points,
            url: "http://10.1.1.18:8083/CourseLearning/1.jpg",
            callback: function (err, node) {
                if(err){
                    console.log(err);
                }
                this_.view3D.addNode(node, this_.view3D.rootNode);

                // 定位贴图
                // this_.view3D.calcNodesFocusedViewpoint({
                //     nodes: [node],
                //     callback : function (err, viewpoint) {
                //         if (!err){
                //             this_.view3D.translateViewpoint(viewpoint, 1000);
                //         }
                //     }
                // })

            },
            pixelScale: 4
        })
    }

    /**
     * 绘制包围盒的交互，返回值是一个Interaction
     * @param points 当前通过鼠标交互绘制的点
     * @param {Function} callback 绘制完成三个点的回调
     * @param top 构建包围盒的向上超出值
     * @param bottom 构建包围盒的向下超出值
     * @returns {Interaction} 绘制包围盒的鼠标交互
     */
    [createRectangleInteraction](points, callback, direction) {
        const view3D = this.view3D;
        const this_ = this;
        const trackerArray = [];
        const blue = COLOR("rgb(0, 0, 255)").alpha(1);
        const lineStyle = {
            width : 4,
            color : blue,
            type : "solid"
        };
        const black = COLOR("rgb(0, 0, 0)").alpha(1);
        const white = COLOR("rgb(255, 255, 255)");
        const textFrame = {
            border : {
                color : black,
                width : 3
            },
            text : {
                size : 15,
                color : black,
                alignment : "left"
            },
            fillColor : white,
            padding : 2
        };
        let startPoint;
        if (points.length > 0){
            startPoint = points[0];
        }
        if (points.length === 1){
            trackerArray.push(
                view3D.InteractionFactory.createDistanceToPointTracker(startPoint, {
                    lineStyle : lineStyle,
                    textFrameStyle : textFrame
                })
            );
        }else if (points.length === 2){
            let endPoint = points[1];
            trackerArray.push(
                view3D.InteractionFactory.createLineBasedRectTracker(
                    {
                        startPt : startPoint,
                        endPt : endPoint
                    },
                    {
                        lineStyle : lineStyle,
                        textFrameStyle : textFrame
                    }
                )
            );
        }
        trackerArray.push(view3D.InteractionFactory.createFollowRectTracker());
        const lineTracker = view3D.InteractionFactory.createCompositeTracker(trackerArray);
        return view3D.InteractionFactory.createPointPickInteraction(async function (err, msg){
            points.push(msg.picked[0].position);
            if (points.length === 3) {
                view3D.swapInteraction(this_.defaultInteraction);
                const points_ = this_.computeRectPoints(points[0], points[1], points[2], 991 * 4, 475 * 4);
                console.log("==================");
                console.log(points_)
                callback(points_);
                return;
            }else {
                view3D.swapInteraction(this_[createRectangleInteraction](points, callback));
            }
        }, {
            tracker : lineTracker
        });
    }


    /**
     * 已知三维空间的三点ABC，ABC所在平面上有直角矩形p1,p2,p3,p4
     * p1和A点重合，
     * p1p2与AB同方向且共线，
     * p1p4垂直于AB，
     * p1p2 p4p3 为矩形宽，width，
     * p1 p4, p2p3为矩形长，length，
     * 求矩形 p1 p2 p3 p4坐标，坐标顺序和ABC的顺序一致
     * TODO 此方法没有做参数校验和特殊情况处理，请方法调用方根据实际情况处理
     *
     * 一些方法的解释
     * `vec3` 是 gl-matrix-double 库中的一种三维向量类型，代表三维空间中的一个点或向量
     * `vec3.create()` 用于创建一个新的三维向量
     * `vec3.sub(v1, v2, v3)` 用于计算向量 v2 - v3
     * `vec3.normalize(out, a)` 用于将向量 a 转化为单位向量
     * `vec3.scaleAndAdd(out, a, b, scale)` 用于计算 a + b * scale
     * `vec3.cross(out, a, b)` 用于计算向量 a 和 b 的叉积
     * `vec3.negate(out, a)` 用于计算向量 a 的负向量
     * `vec3.add(out, a, b)` 用于计算向量 a 和 b 的和
     * @param A
     * @param B
     * @param C
     * @param width
     * @param length
     * @returns {(*|vec3)[]}
     */
    computeRectPoints(A, B, C, width, length) {
        const vec3 = MATRIX.vec3;
        // 计算向量 AB
        const AB = vec3.create();
        vec3.sub(AB, B, A);
        // 计算向量 AC
        const AC = vec3.create();
        vec3.sub(AC, C, A);
        // 计算平面法向量
        const normal = vec3.create();
        vec3.cross(normal, AB, AC);
        // 将矩形宽方向向量归一化
        const unitWidthVector = vec3.create();
        vec3.normalize(unitWidthVector, AB);
        // 计算矩形宽向量
        const widthVector = vec3.create();
        vec3.scale(widthVector, unitWidthVector, width);
        // 将矩形长方向向量归一化
        const unitLengthVector = vec3.create();
        vec3.cross(unitLengthVector, normal, unitWidthVector);
        vec3.normalize(unitLengthVector, unitLengthVector);
        // 计算矩形长向量
        const lengthVector = vec3.create();
        vec3.scale(lengthVector, unitLengthVector, length);
        // 计算矩形四个顶点坐标
        const p1 = A;
        const p2 = vec3.create();
        vec3.add(p2, p1, widthVector);
        const p3 = vec3.create();
        vec3.add(p3, p2, lengthVector);
        const p4 = vec3.create();
        vec3.subtract(p4, p3, widthVector);
        // 最终结果即为p1、p2、p3、p4的坐标
        console.log(A, p2, p3, p4)

        // 将这个平面沿着法向量的方向平移
        // 因为如果直接以鼠标点选的地方绘制，则会出现屏幕闪烁的情况
        const heightNormal = vec3.normalize(vec3.create(), normal);
        const p1_ = vec3.scaleAndAdd(vec3.create(), p1, heightNormal, 10);
        const p2_ = vec3.scaleAndAdd(vec3.create(), p2, heightNormal, 10);
        const p3_ = vec3.scaleAndAdd(vec3.create(), p3, heightNormal, 10);
        const p4_ = vec3.scaleAndAdd(vec3.create(), p4, heightNormal, 10);

        return [p1_, p2_, p3_, p4_];
    }
}
