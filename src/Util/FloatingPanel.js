import MATRIX from "gl-matrix-double";
import $ from "jquery";
var COLOR = require("color");
export default class FloatingPanel{
    constructor(view3D) {
        this.view3D = view3D;
        this.UUID = genUUID();
    }

    /**
     * 在三维空间固定一个悬浮的标签或者窗口，随着模型的缩放移动而变化
     * @param nodes 在某个Node上悬浮窗口
     * @param point 窗口悬浮在某个点
     * @param Dom 窗口内要展示的内容
     * @param options 扩展参数
     */
    async init(nodes, point, Dom ,options){
        const this_ = this;
        let center = null;
        if (nodes){
            center = await new Promise((resolve, reject) => {
                this_.view3D.calcAABB(nodes, (err, aabb) => {
                    const min = aabb.min;
                    const max = aabb.max;
                    resolve(MATRIX.vec3.fromValues((min[0]+max[0])/2, (min[1]+max[1])/2, max[2]));
                });
            })
        }else {
            center = point;
        }

        const pointNode = this_.view3D.NodeFactory.createPoint(center, {
            size: 2,
            color: COLOR("rgb(232,22,22)")
        });
        this_.view3D.addNode(pointNode, this_.view3D.rootNode);

        $("#bcd").append("<div id='floatingPanel_" + this_.UUID + "' style='width: 200px; height: 200px; position: absolute; background-color: beige'>" +
            "测试一下展示的标签11111111111111111111</div>");

        this_.createListener(center, (pos) => {
            if (pos[2] < 0.6){
                this_.show();
                // 将DOM元素定位到对应位置
                $("#floatingPanel_" + this_.UUID).css({
                    "left" : pos[0] - 100 + "px",
                    "bottom" : pos[1] + 100 + "px"
                });
            }else {
                this_.hide();
            }
        });

        return {
            DOM: $("#floatingPanel_" + this_.UUID),
            divId: "floatingPanel_" + this_.UUID
        }
    }


    show(){
        $("#floatingPanel_" + this.UUID).show();
    }


    hide(){
        $("#floatingPanel_" + this.UUID).hide();
    }

    /**
     * 销毁这个实例
     */
    destory(){

    }

    createListener(center, callback){
        this.view3D.registerVpMatrixChangedCallback((err, viewMatrix, projectionMatrix, windowsMatrix) => {
            const pos = this.view3D.worldCoordToScreenCoord(center, viewMatrix, projectionMatrix, windowsMatrix);
            callback(pos);
        })
    }
}


const genUUID = function () {
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
        var r = Math.random() * 16 | 0, v = c === "x" ? r : r & 0x3 | 0x8;
        return v.toString(16);
    });
};
