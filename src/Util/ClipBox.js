import Matrix from "gl-matrix-double";

export default class ClipBox{
    constructor(view3D) {
        this.view3D = view3D;
        this.tempEffect = [];
    }

    /**
     * 创建一个立方体的剖切效果
     * @param points   立方体八个顶点，点位顺序必须为顺时针或者逆时针
     * @param state  立方体上面是否可拖拽
     * @param drawingRootNodes 要剖切的点
     */
    create(points, state, drawingRootNodes){
        const dir1 = this.getNormal(points[2], points[1], points[0]);
        const vec3 = Matrix.vec3;
        const point5_0 = vec3.create();
        vec3.sub(point5_0, points[0], points[5]);
        const angle = vec3.angle(dir1, point5_0);
        console.log(angle / Math.PI * 180);
        // 逆时针排列
        if (angle / Math.PI * 180 > 90){
            let tem = points[1];
            points[1] = points[3];
            points[3] = tem;

            tem = points[5];
            points[5] = points[7];
            points[7] = tem;
        }else if (angle / Math.PI * 180 > 90){
            throw new Error("请在水平位置绘制区域");
        }
        this.createClip(points[0], points[3], points[2], points[1], 0, state, drawingRootNodes);
        this.createClip(points[7], points[6], points[2], points[3], 1, state, drawingRootNodes);
        this.createClip(points[4], points[5], points[6], points[7], 2, state, drawingRootNodes);
        this.createClip(points[5], points[1], points[6], points[2], 3, state, drawingRootNodes);
        this.createClip(points[4], points[0], points[5], points[1], 4, state, drawingRootNodes);
        this.createClip(points[4], points[7], points[3], points[0], 5, state, drawingRootNodes);
    }

    createClip(point1, point2, point3, point4, no, state, drawingRootNodes){
        // 剖切器位置
        let centerPosition = Matrix.vec3.fromValues((point1[0] + point3[0])/2, (point1[1] + point3[1])/2, (point1[2] + point3[2])/2);
        // 要剖切的节点数组（数组中的节点必须存在于场景中）
        let destNodes = drawingRootNodes;
        const ctrlParam = {
            scale: 1,
            showClipCtrl: no === 2 && state ? true : false,
            pickHandler: function (err, position, direction , angle ) {}
        }

        const dir = this.getNormal(point1, point2, point3)
        const effect = this.view3D.EffectFactory.createClipEffect(no, centerPosition, destNodes, ctrlParam, dir);
        this.view3D.addEffect(effect);
        this.tempEffect.push(effect);
    }

    getNormal(point1, point2, point3){
        // 剖切面方向
        const vec3 = Matrix.vec3;
        const A = point1;
        const B = point2;
        const C = point3;
        // 计算向量 AB
        const AB = vec3.create();
        vec3.sub(AB, B, A);
        // 计算向量 AC
        const AC = vec3.create();
        vec3.sub(AC, C, A);
        // 计算平面法向量
        const dir = vec3.create();
        vec3.cross(dir, AB, AC);
        return dir;
    }

    clear(){
        this.tempEffect.forEach(effect => {
            this.view3D.removeEffect(effect);
        });
        this.tempEffect = [];
    }
}
