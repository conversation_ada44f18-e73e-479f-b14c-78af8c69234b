import MATRIX from "gl-matrix-double";
var PI = 3.1415926;
import $ from "jquery";
import _ from "lodash";
import vic from "vic";

export default class Person{
    constructor(view3D, style, nodeName) {
        this.interval = null;
        this.nodeName = nodeName;
        this.animation = null;
        this.view3D = view3D;
        this.startDirection = null;
        // 这里是小人奔跑的参数
        this.style = {
            speed : 10,
            angleSpeed : 0.5,
            viewPointHeight : 200,
            viewPointDaRatio : 0.8,
            viewPointGoDownFollow : "true",
            isViewPointLinster : false,
        };
        if (style){
            this.style = $.extend({}, this.style, style);
        }
        // 小人的状态
        this.state = "run";
        this.playAnimationCommand = null;
        this.stopAnimationCommand = null;
        this.playModeCommand = null;
        // 小人模型当前的方向
        this.currentDirection = this.startDirection;
    }


    async create(path, startPoint, modelSize, xAngle, modelType){
        // 获取节点Node对象
        const modelUrl = path;
        console.log(modelUrl);
        // 加载动画模型并获取对应Node
        this.animation = await new Promise((resolve, reject)=>{
            const loadModuleOption = {
                modelUrl : modelUrl,
                callback : (err, node)=>{
                    if (err){
                        console.log(err);
                    }
                    var animationNode = this.personStandUp(node,
                        MATRIX.vec3.fromValues(startPoint[0], startPoint[1], startPoint[2]),
                        this.nodeName,
                        modelSize,
                        Math.PI * (xAngle/180),
                        this.view3D);
                    resolve({
                        loadAnimationNode : node,
                        animationNode : animationNode,
                        modelType: modelType === "fbx" || modelType === "FBX" ? "animation": ""
                    });
                }
            };

            const fileType = ["osg","ive", "3ds", "osgb","FBX","OSG","IVE","3DS","OSGB"];
            if (modelType === "fbx" || modelType === "FBX") {
                this.view3D.NodeFactory.loadAnimationModel(loadModuleOption);
            } else {
                this.view3D.NodeFactory.loadModel(loadModuleOption);
            }
        });

        this.view3D.addNode(this.animation.animationNode, this.view3D.rootNode);
        this.view3D.addNode(this.animation.loadAnimationNode, this.view3D.rootNode);
        this.setAnimationCommand();
        this.changePersonState();
    }

    // 设置动画命令
    setAnimationCommand(){
        // 创建一个动画播放命令
        const anName = "Take 001";
        const playOp = {
            priority : 7,
            weight : 0.1
        };
        if (this.animation.modelType === "animation") {
            this.playAnimationCommand = this.animation.loadAnimationNode.createPlayAnimationCommand(anName, playOp);
            this.view3D.executeCommand(this.playAnimationCommand);
            // 设置播放模式，默认下为loop循环模式。
            const mode = "loop";
            this.playModeCommand = this.animation.loadAnimationNode.createSetPlayModeCommand(anName, mode);
            this.view3D.executeCommand(this.playModeCommand);
            this.stopAnimationCommand = this.animation.loadAnimationNode.createStopAnimationCommand(anName);
        }

        // 动画小人的移动轨迹点
        this.positions = [];
    }

    /**
     * 动画播放
     * @param positions 坐标点
     * @param {AnimationName} node 动画节点
     */
    async move(positions, callback, isRevert) {
        console.log("--------开始移动-----");
        const this_ = this;
        // 如果当前路径的开始节点不是小人当前所处节点
        if (this.currentPoint && !(positions[0][0] === this.currentPoint[0]
            && positions[0][1] === this.currentPoint[1]
            && positions[0][2] === this.currentPoint[2])){
            positions.unshift(this.currentPoint);
        }

        if (isRevert && positions.length >= 3){
            this.positions = [positions[0], positions[positions.length - 2], positions[positions.length - 1]];
        }else {
            this.positions = positions;
        }


        if (this_.state === "cacse"){
            this_.changePersonState();
        }
        this.interval = null;

        if (this.style.isViewPointLinster){
            this.interval = this_.viewPointLinster();
        }
        for (let i = 0; i < this.positions.length - 1; i++){
            if (this_.animation && this_.animation.animationNode) {
                this_.currentDirection = await this.personRun(this_.animation.animationNode,
                    this.positions[i], this.positions[i + 1],
                    1000, this_.currentDirection, null, isRevert);
            }
        }
        clearInterval(this.interval);
        this_.currentPoint = positions[positions.length - 1];
        // 存储模型的方向

        if (callback){
            if (this_.state === "run" && this_.animation.modelType === "animation"){
                this_.changePersonState();
            }
            callback();
        }
    }

    changePersonState(){
        if (this.state === "cacse"){
            this.view3D.executeCommand(this.playAnimationCommand);
            this.view3D.executeCommand(this.playModeCommand);
            this.state = "run";
        }else if (this.state === "run"){
            this.view3D.executeCommand(this.stopAnimationCommand);
            this.state = "cacse";
        }
    }

    viewPointLinster(){
        const this_ = this;
        let eyeZ = null;
        let centerZ = null;
        const view3D = this.view3D;
        const interval = setInterval(function () {
            const direction = MATRIX.vec3.fromValues(-this_.modelCurrentDirection[0],
                -this_.modelCurrentDirection[1],
                this_.modelCurrentDirection[2] > 100 && this_.style.viewPointGoDownFollow === "true"
                    ? -this_.modelCurrentDirection[2] : 0);
            view3D.calcNodesFocusedViewpoint({
                nodes : [this_.animation.animationNode],
                direction : direction,
                daRatio : Number(this_.style.viewPointDaRatio),
                callback : function (err, viewpoint) {
                    eyeZ = eyeZ ? eyeZ : viewpoint.eye[2] + this_.style.viewPointHeight;
                    const eye = MATRIX.vec3.fromValues(
                        viewpoint.eye[0],
                        viewpoint.eye[1],
                        eyeZ);
                    // centerZ = centerZ ? centerZ : viewpoint.center[2] + this_.style.viewPointHeight;
                    const center = MATRIX.vec3.fromValues(
                        viewpoint.center[0],
                        viewpoint.center[1],
                        viewpoint.center[2]);
                    const up = MATRIX.vec3.fromValues(viewpoint.up[0],
                        viewpoint.up[1], viewpoint.up[2]);
                    let newViewPoint = new vic.Viewpoint(eye, center, up);
                    view3D.translateViewpoint(newViewPoint, 0);
                }
            });
        }, 50);
        return interval;
    }

    /**
     * 小人跑起来:首先转向之后移动
     * view3D
     * startPosition 开始点
     * endPosition  结束点
     * duration  移动速度
     * startAngle  初始向量
     * endDirection  结束向量
     */
    personRun(animationNode, startPosition, endPosition, duration, startAngle, endDirection, isRevert){
        var that = this;
        return new Promise(async (resolve, reject)=>{
            var lastDirection = await that.rotateDirection(that.view3D, animationNode, startPosition,
                endPosition, startAngle, endDirection, null, isRevert);
            await that.movePerson(animationNode, startPosition, endPosition, null, isRevert);
            resolve(lastDirection);
        });
    };


    /**
     * 旋转方向
     */
    rotateDirection(view3D, animationNode, startPosition, endPosition, startAngle, endDirection, duration, isRevert,isDestory){
        var that = this;
        if (isRevert){
            duration = 0.01;
        }
        startAngle ? startAngle : startAngle = MATRIX.vec3.fromValues(0, 1, 0);
        // endDirection ? endDirection = MATRIX.vec3.fromValues(0, 1, 0)
        //     : endDirection = MATRIX.vec3.fromValues(startPosition[0] - endPosition[0],
        //     startPosition[1] - endPosition[1], startPosition[2] - endPosition[2]);
        !endDirection && (endDirection = MATRIX.vec3.fromValues(startPosition[0] - endPosition[0],
            startPosition[1] - endPosition[1], startPosition[2] - endPosition[2]));
        that.modelCurrentDirection = [...endDirection];
        var angle = that.calcAngleFromTwoVec(startAngle, endDirection);
        return new Promise((resolve, reject)=>{
            const option = {
                transformations : [{
                    node : animationNode,
                    rotation : {
                        position : MATRIX.vec3.fromValues(startPosition[0], startPosition[1], startPosition[2]),
                        direction : MATRIX.vec3.fromValues(0, 0, angle.dir[2]),
                        angle : isDestory ? 360 - angle.angle : angle.angle
                    }
                }],
                duration : duration ? duration : angle.angle / 180 * that.style.angleSpeed * 1000,
                callback : function (error) {
                    if(error){
                        console.error("小人移动出错");
                        reject();
                    }
                    resolve(endDirection);
                }
            };
            view3D.transformNodes(option);
        });
    }

    /**
     * 移动小人
     * @animationNode  小人node
     * @startPosition  小人起点位置
     * @endPosition  小人终点位置
     */
    movePerson(animationNode, startPosition, endPosition, duration, isRevert){
        var that = this;
        const dLength = Math.sqrt((startPosition[0] - endPosition[0]) * (startPosition[0] - endPosition[0])
            + (startPosition[1] - endPosition[1]) * (startPosition[1] - endPosition[1])
            + (startPosition[2] - endPosition[2]) * (startPosition[2] - endPosition[2])) / 1000;
        const speed = this.style.speed;
        const t = dLength / speed * 1000;
        if (isRevert){
            duration = 0.01;
        }
        return new Promise((resolve, reject)=>{
            that.view3D.transformNodes({
                transformations : [{
                    node : animationNode,
                    translation : MATRIX.vec3.fromValues(endPosition[0] - startPosition[0],
                        endPosition[1] - startPosition[1], endPosition[2] - startPosition[2])
                }],
                duration : duration ? duration : t,
                callback : function (error) {
                    if(error){
                        console.error("小人移动出错");
                        reject();
                    }
                    resolve();
                }
            });
        });
    }

    /**
     *
     * @param {vec3} from 起始向量
     * @param {vec3} to 偏转的向量
     * @returns {Object} result 输出结果
     * 					result.radian 旋转弧度
     * 					result.angle 旋转角度
     * 					result.dir 旋转轴
     */
    calcAngleFromTwoVec(from, to) {
        // 让Z轴为0，只计算水平方向的旋转角度
        from[2] = 0;
        to[2] = 0;
        var angle = MATRIX.vec3.angle(from, to);
        var dir = MATRIX.vec3.create();
        MATRIX.vec3.cross(dir, from, to);
        MATRIX.vec3.normalize(dir, dir);
        var result = {
            radian : angle,
            angle : angle / PI * 180,
            dir : dir
        };
        return result;
    }

    /**
     * 让小人站起来
     * @node  小人node
     * @_position  小人位置
     * @name  小人名字
     * @size  放大或缩小 Math.PI*3/2
     * @rotateX
     */
    personStandUp(node, position, name, size, rotateX, view3D){
        let mat = MATRIX.mat4.create();
        MATRIX.mat4.translate(mat, mat, position);
        // 转动模型
        if(rotateX){
            MATRIX.mat4.rotateX(mat, mat, rotateX);
        };
        // 放大或缩小人
        if(size){
            let matScale = MATRIX.mat4.create();
            let scaleVec = MATRIX.vec3.fromValues(size, size, size);
            MATRIX.mat4.fromScaling(matScale, scaleVec);
            MATRIX.mat4.multiply(mat, mat, matScale);
        }
        // 创建站起来的Node
        let standUpNode = view3D.NodeFactory.createTransformation(mat, [node], "");
        let mat2 = MATRIX.mat4.create();
        let standUpNode2 = view3D.NodeFactory.createTransformation(mat2, [standUpNode], name);
        return standUpNode2;
    }

    async destory(){

        this.view3D.removeNode(this.animation.animationNode, this.view3D.rootNode);
        this.view3D.removeNode(this.animation.loadAnimationNode, this.view3D.rootNode);
        this.animation = null;
        this.view3D = null;
        this.render3dView = null;
    }
}
