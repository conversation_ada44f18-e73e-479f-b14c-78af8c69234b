import COLOR from "color";
import MATRIX from "gl-matrix-double";


/**
 * 这个类用于创建一个包围盒并求教，获取包围之内或包围盒之外的数据对象
 * 1、支持绘制一个包围盒
 * 2、支持对包围盒求交
 * 3、支持统计包围盒之中的数据对象
 * 除公有方法外，其他方法皆用Symbol修饰为私有方法，
 * 对于一些特定的内部类，也不对外进行抛出
 *
 * **************************这里是代码调用示例**********************************
 *   示例一：
 *   const calculateNodeInBox = new CalculateNodeInBox(view3D);
 *   calculateNodeInBox.draw( async function (points) {
 *		console.log(points);
 *		const result = calculateNodeInBox.createBox(points);
 *		console.log(result);
 *		const nodes = await calculateNodeInBox.queryNodesInBox(points);
 *		console.log(nodes);
 *	}, 40000, 0);
 *
 *  示例二：
 *  const calculateNodeInBox = new CalculateNodeInBox(view3D);
 *  const points = await new Promise((resolve, reject) => {
 *		calculateNodeInBox.draw( async function (points) {
 *			resolve(points);
 *		}, 40000, 0);
 *	});
 *	console.log(points);
 *	const result = calculateNodeInBox.createBox(points);
 *	console.log(result);
 *	const nodes = await calculateNodeInBox.queryNodesInBox(points);
 *	console.log(nodes);
 *
 */
const createRectangleInteraction = Symbol("a private function createRectangleInteraction");
const calcRectPts = Symbol("a private function calcRectPts");
const getNodes = Symbol("a private function getNodes");


export default class CalculateNodeInBox{
	constructor(view3D) {
		this.view3D = view3D;
		this.defaultInteraction = null;
	}

	/**
	 * 开始绘制一个包围盒
	 * @param {Function} callback 绘制完成三个点的回调
	 * @param {Number} top 构建包围盒的向上超出值
	 * @param {Number} bottom 构建包围盒的向下超出值
	 * @returns {Interaction} 返回包围盒的八个点坐标
	 */
	async draw(callback, top, bottom){
		const interation = this[createRectangleInteraction]([], callback, top, bottom);
		this.defaultInteraction = this.view3D.swapInteraction(interation);
	}

	/**
	 * 中途退出绘制
	 */
	closeDraw(){
		if (this.defaultInteraction){
			this.view3D.swapInteraction(this.defaultInteraction);
		}
	}

	/**
	 * 创建一个包围盒
	 * @param points
	 */
	/**
	 * 创建一个包围盒
	 * @param {Array} points 包围盒的八个坐标顶点
	 * @param {Color} color 可选参 包围盒颜色， 默认 rgb(98,255,211)
	 * @param {Number} transparencyValue 可选参 透明度，默认0.5
	 */
	createBox(points, color, transparencyValue){
		const colorValue = color ? color : "rgb(98,255,211)";
		const node = this.view3D.NodeFactory.createBox({
			points : points,
			color : COLOR(colorValue)
		});
		this.view3D.addNode(node, this.view3D.rootNode);

		const effect = this.view3D.EffectFactory.createTransparencyEffect({
			destNodes : [node],
			transparencyValue : transparencyValue ? transparencyValue : 0.5
		});
		this.view3D.addEffect(effect);
		return {
			node: node,
			effect: effect
		}
	}


	/**
	 * 对某个包围盒求交，用立方体的八个顶点作为传入参数
	 * @param {Array} points 包围盒八个顶点
	 * @returns {Promise<Object>} 求交结果
	 */
	async queryNodesInBox(points){
		const this_ = this;
		return await new Promise((resolve, reject) => {
			// 包围盒与模型求交
			this.view3D.queryNodesInBox({
				boxPoints : points
			}, async function (err, picked) {
				const result = this_[getNodes](picked);
				resolve(result);
			});
		});
	}




	/**
	 * 绘制包围盒的交互，返回值是一个Interaction
	 * @param points 当前通过鼠标交互绘制的点
	 * @param {Function} callback 绘制完成三个点的回调
	 * @param top 构建包围盒的向上超出值
	 * @param bottom 构建包围盒的向下超出值
	 * @returns {Interaction} 绘制包围盒的鼠标交互
	 */
	[createRectangleInteraction](points, callback, top, bottom) {
		const view3D = this.view3D;
		const this_ = this;
		const trackerArray = [];
		const blue = COLOR("rgb(0, 0, 255)").alpha(1);
		const lineStyle = {
			width : 4,
			color : blue,
			type : "solid"
		};
		const black = COLOR("rgb(0, 0, 0)").alpha(1);
		const white = COLOR("rgb(255, 255, 255)");
		const textFrame = {
			border : {
				color : black,
				width : 3
			},
			text : {
				size : 15,
				color : black,
				alignment : "left"
			},
			fillColor : white,
			padding : 2
		};
		let startPoint;
		if (points.length > 0){
			startPoint = points[0];
		}
		if (points.length === 1){
			trackerArray.push(
				view3D.InteractionFactory.createDistanceToPointTracker(startPoint, {
					lineStyle : lineStyle,
					textFrameStyle : textFrame
				})
			);
		}else if (points.length === 2){
			let endPoint = points[1];
			trackerArray.push(
				view3D.InteractionFactory.createLineBasedRectTracker(
					{
						startPt : startPoint,
						endPt : endPoint
					},
					{
						lineStyle : lineStyle,
						textFrameStyle : textFrame
					}
				)
			);
		}
		trackerArray.push(view3D.InteractionFactory.createFollowRectTracker());
		const lineTracker = view3D.InteractionFactory.createCompositeTracker(trackerArray);
		return view3D.InteractionFactory.createPointPickInteraction(async function (err, msg){
			points.push(msg.picked[0].position);
			if (points.length === 3) {
				view3D.swapInteraction(this_.defaultInteraction);
				const boxPoints = this_[calcRectPts](points[0], points[1], points[2], top, bottom);
				callback(boxPoints);
				return;
			}else {
				view3D.swapInteraction(this_[createRectangleInteraction](points, callback, top, bottom));
			}
		}, {
			tracker : lineTracker
		});
	}


	/**
	 * 根据一条线上的两点和不在线上的点计算包围盒八个点坐标
	 * @param lineStartPt 第一个点
	 * @param lineEndPt 第二个点
	 * @param thirdPt 第三个点
	 * @param top 构建包围盒的向上超出值
	 * @param bottom 构建包围盒的向下超出值
	 * @returns {Array} 包围盒八个点的坐标
	 */
	[calcRectPts](lineStartPt, lineEndPt, thirdPt, top, bottom) {
		let startLineDir = MATRIX.vec3.create();
		startLineDir = MATRIX.vec3.sub(startLineDir, lineEndPt, lineStartPt); // e - s
		startLineDir = MATRIX.vec3.normalize(startLineDir, startLineDir);

		let thirdDir = MATRIX.vec3.create();
		thirdDir = MATRIX.vec3.sub(thirdDir, thirdPt, lineEndPt); // t - s
		let thirdLength = MATRIX.vec3.length(thirdDir);
		thirdDir = MATRIX.vec3.normalize(thirdDir, thirdDir);

		let norm = MATRIX.vec3.create();
		norm = MATRIX.vec3.cross(norm, startLineDir, thirdDir);

		let offsetDir = MATRIX.vec3.create();
		offsetDir = MATRIX.vec3.cross(offsetDir, norm, startLineDir);
		offsetDir = MATRIX.vec3.normalize(offsetDir, offsetDir);
		let cosa = MATRIX.vec3.dot(thirdDir, offsetDir);
		let offsetLength = thirdLength * cosa;
		let offsetMove = MATRIX.vec3.create();
		offsetMove = MATRIX.vec3.scale(offsetMove, offsetDir, offsetLength);

		const restPts = [];
		restPts.push(lineStartPt);
		restPts.push(lineEndPt);
		let movedStartPt = MATRIX.vec3.create();
		movedStartPt = MATRIX.vec3.add(movedStartPt, lineStartPt, offsetMove);
		let movedEndPt = MATRIX.vec3.create();
		movedEndPt = MATRIX.vec3.add(movedEndPt, lineEndPt, offsetMove);
		restPts.push(movedEndPt);
		restPts.push(movedStartPt);
		const point1 = MATRIX.vec3.fromValues(restPts[0][0], restPts[0][1], restPts[0][2] - bottom);
		const point2 = MATRIX.vec3.fromValues(restPts[1][0], restPts[1][1], restPts[1][2] - bottom);
		const point3 = MATRIX.vec3.fromValues(restPts[2][0], restPts[2][1], restPts[2][2] - bottom);
		const point4 = MATRIX.vec3.fromValues(restPts[3][0], restPts[3][1], restPts[3][2] - bottom);
		const point5 = MATRIX.vec3.fromValues(restPts[0][0], restPts[0][1], restPts[0][2] + top);
		const point6 = MATRIX.vec3.fromValues(restPts[1][0], restPts[1][1], restPts[1][2] + top);
		const point7 = MATRIX.vec3.fromValues(restPts[2][0], restPts[2][1], restPts[2][2] + top);
		const point8 = MATRIX.vec3.fromValues(restPts[3][0], restPts[3][1], restPts[3][2] + top);
		return [point1, point2, point3, point4, point5, point6, point7, point8];
	}

	/**
	 * 解析获取包围盒求交后的结果
	 * @param picked
	 * @returns {{nodes: *[], nodePaths: *[]}}
	 */
	[getNodes](picked){
		const nodes = [];
		const nodePaths = [];
		const handles = [];
		console.log(picked);
		for (const pickedObj of picked) {
			const nodePath = pickedObj.nodePath;
			if (nodePath.length === 2 && nodePath[1] && nodePath[1].getName() && pickedObj.scale >= 0.5){
				nodes.push(nodePath[1]);
				nodePaths.push(nodePath);
				handles.push(nodePath[1].getName());
			}
		}
		return {
			nodes: nodes,
			nodePaths: nodePaths,
			handles: handles
		};
	};
}
