var COLOR = require("color");
var Matrix = require("gl-matrix-double");


/**
 * 脚手架操作
 * 可以为圆形设备（罐、塔）搭建脚手架
 */
export default class ScaffoldOperation{
	constructor(view3D) {
		this.view3D = view3D;
		this.nodes = [];
	}

	/**
	 * 创建脚手架
	 * ********************外部只推荐调用这一个方法，其他方法不建议用，都是私有方法***************************
	 * @param node 需要搭建脚手架的对象
	 */
	create(nodes){
		const this_ = this;
		this_.view3D.calcAABB(nodes, function(err, aabb) {
			var min = aabb.min;
			var max = aabb.max;
			this_.createScaffolding(min, max, this_.view3D, 2);
		});
	}


	createScaffolding(min, max, view3D, precision) {
		const this_ = this;
		var R = (Math.sqrt(Math.pow((min[0] - max[0]), 2) + Math.pow((min[1] - max[1]), 2))) / (2*Math.sqrt(2));
		var centx = (max[0] + min[0]) / 2;
		var centy = (max[1] + min[1]) / 2;
		var ligan = 1000 * precision;
		var bottomPoints = []; // 第一排 底部圆心坐标
		var topPoints = []; // 第一排 顶部圆心坐标
		var sin = ligan / (2 * R);
		var yuanxinjiao0 = 2 * Math.asin(sin);
		var yuanxinjiao = Math.asin(2 * sin * Math.cos(Math.asin(sin)));
		var count = 1 + _.floor(2 * Math.PI / yuanxinjiao);
		for (var i = 1; i < count; i++) {
			var x = centx + R * Math.cos(yuanxinjiao * i);
			var y = centy + R * Math.sin(yuanxinjiao * i);
			bottomPoints.push(Matrix.vec3.fromValues(x, y, min[2]));
			topPoints.push(Matrix.vec3.fromValues(x, y, max[2]));
		}
		for (var index = 0; index < topPoints.length; index++) {
			var node = createCylinder(bottomPoints[index], topPoints[index], view3D);
			view3D.addNode(node, view3D.rootNode);
			this.nodes.push(node);
		}
		// 第二排立杆
		var zongju = 1000 * precision;
		var bottomPointsS = [];
		var topPointsS = [];
		var MaxR = R + zongju;
		for (var inde = 1; inde < count; inde++) {
			var x2 = centx + MaxR * Math.cos(yuanxinjiao * inde);
			var y2 = centy + MaxR * Math.sin(yuanxinjiao * inde);
			bottomPointsS.push(Matrix.vec3.fromValues(x2, y2, min[2]));
			topPointsS.push(Matrix.vec3.fromValues(x2, y2, max[2]));
		}
		for (var j = 0; j < topPointsS.length; j++) {
			var node2 = createCylinder(bottomPointsS[j], topPointsS[j], view3D);
			view3D.addNode(node2, view3D.rootNode);
			this.nodes.push(node2);
		}
		//横杆 每个一个横杆加一个板子
		var cross = {};
		var buju = 1000 * precision;
		var crossZ = min[2] + 0.2 * 1000;
		var crossCount = 1;
		while (crossZ < max[2]) {
			var ind = 1;
			while (ind < bottomPoints.length) {
				this_.createCross(view3D, bottomPoints, bottomPointsS, crossZ, ind - 1, ind, crossCount)
				ind++;
			}
			this_.createCross(view3D, bottomPoints, bottomPointsS, crossZ, ind - 1, 0, crossCount)
			crossZ += buju;
			crossCount++;
		}
	}
	createCross(view3D, bottomPoints, bottomPointsS, crossZ, first, second, crossCount) {
		// 第一排横杆
		var posx1 = Matrix.vec3.fromValues(bottomPoints[first][0], bottomPoints[first][1], crossZ);
		var posy1 = Matrix.vec3.fromValues(bottomPoints[second][0], bottomPoints[second][1], crossZ);
		var node = createCross(posx1, posy1, view3D);
		view3D.addNode(node, view3D.rootNode);
		this.nodes.push(node);
		// 第二排横杆
		var posx2 = Matrix.vec3.fromValues(bottomPointsS[first][0], bottomPointsS[first][1], crossZ);
		var posy2 = Matrix.vec3.fromValues(bottomPointsS[second][0], bottomPointsS[second][1], crossZ);
		node = createCross(posx2, posy2, view3D);
		view3D.addNode(node, view3D.rootNode);
		this.nodes.push(node);
		// 夹板
		const this_ = this;
		if (crossCount % 2 == 1) {
			var points = [];
			points.push(posx1);
			points.push(posx2);
			points.push(posy2);
			points.push(posy1);
			createPolygon(points, view3D, function(node) {
				view3D.addNode(node, view3D.rootNode);
				this_.nodes.push(node);
			});
		}
	}
}


// 生成纵杆圆柱体
const createCylinder = function(bottomCenter, topCenter, view3D) {
	var radius = 50.0;
	var color = COLOR('rgb(255, 215, 0)').alpha(1);
	var bottomNormal = Matrix.vec3.fromValues(0, 0, 1);
	var topNormal = Matrix.vec3.fromValues(0, 0, -1);
	var option = {
		bottomCenter : bottomCenter,
		bottomRadius : radius,
		bottomNormal : bottomNormal,
		topCenter : topCenter,
		topRadius : radius,
		topNormal : topNormal,
		color : color
	}
	var cylinderNode = view3D.NodeFactory.createCylinder(option);

	return cylinderNode;
}
//求两点间的单位向量
const unitVector = function(pos1, pos2) {
	var vector = [];
	vector.push(pos2[0] - pos1[0]);
	vector.push(pos2[1] - pos1[1]);
	vector.push(pos2[2] - pos1[2]);
	var model = Math.sqrt(Math.pow(vector[0], 2) + Math.pow(vector[1], 2) + Math.pow(vector[2], 2));
	var unit = [];
	unit.push(vector[0] / model);
	unit.push(vector[1] / model);
	unit.push(vector[2] / model);
	return unit;
}
// 横杆
const createCross = function(bottomCenter, topCenter, view3D) {
	var radius = 50.0;
	var color = COLOR('rgb(255, 215, 0)').alpha(1);
	var normal = unitVector(bottomCenter, topCenter);
	normal = Matrix.vec3.fromValues(normal[0], normal[1], normal[2]);
	var option = {
		bottomCenter : bottomCenter,
		bottomRadius : radius,
		bottomNormal : normal,
		topCenter : topCenter,
		topRadius : radius,
		topNormal : normal,
		color : color
	}
	var crossNode = view3D.NodeFactory.createCylinder(option);

	return crossNode;
}

//生成矩形
const createPolygon = function(points, view3D, callBack) {
	var option = {
		points : points,
		color : COLOR('rgb(135, 135, 132)').alpha(1),
		callback : function(err, node) {
			if (err) {
				throw err.message;
			} else {
				callBack(node);
			}
		}
	};
	view3D.NodeFactory.createPolygon(option);
}
