import vic from "vic";


import $ from "jquery";
import Matrix from "gl-matrix-double"
import COLOR from "color";
import {Loading} from "element-ui"
import OSGBModelsAdapter from "./osgbModelLoad.js";
export default class Render3D{
    constructor(pickHandler) {
        this.sceneId = null;
        this.factory = null;
        this.view3D = null;
        this.isLoadModel = false;
        this.currentRootNode = null;
        this.drwingRootNode = null;
        this.pickHandler = pickHandler;

        // 当前鼠标点击产生的变色效果
        this.effect = null;

        this.currentNode = null;

        this.osgbModelsAdapter = new OSGBModelsAdapter();
        this.osgbRootNode = null;
    }

    /**
     * 初始化场景
     * @param sceneId
     */
    async init(sceneId){
        console.log(sceneId);
        this.sceneId = sceneId;
        // 获取BRS的地址
        // const result = await config.get("brs");
        const result = {
            useSSL: true,
            ip : '127.0.0.1',
            port: "9010"
        };
        const server = {
            ip: result.ip,
            port: result.port,
            useSSL: false,
            userName: "15120458411"
        };
        // 创建Factory
        await this.createFactory(server);
        // 创建View3D
        await this.createView3D();
        await this.createInteraction();

        this.callback = null;
    }

    loadOSGBModel(osgbList, center){
        this.osgbModelsAdapter.adapt(osgbList,this.view3D, this.currentRootNode, center);
    }

    positionsWatch(callback){
        this.callback = callback;
    }


    /**
     * 检查View3D是否创建完成
     * @returns {Promise<void>}
     */
    async checkView3D(){
        const this_ = this;
        if (!this_.isLoadModel){
            await new Promise((resolve, reject) => {
                const inter = setInterval(function () {
                    if (this_.isLoadModel) {
                        clearInterval(inter);
                        resolve();
                    }
                }, 100);
            });
        }
    }

    /**
     * 加载三维模型
     */
    async loadModel(name, callback){
        const this_ = this;
        const option = {
            fileName : name,
            callback: this_.loadCompleteCallback("[1.0,0,0,0,0,1.0,0,0,0,0,1.0,0,0,0,0,1.0]", callback),
            progress : this_.loadProcessCallback
        };
        this.view3D.NodeFactory.loadOGFFromROS(option);
    }

    loadProcessCallback(pro){
        console.log("========", pro);
        if(pro === 1){
            let loading = Loading.service({
                lock: true,
                text: '模型加载中……',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.5)'
            })
            loading.close()
        }
    }


    /**
     * 加载结束回调
     *
     * @param    {Error}    options.callback.err    错误
     *                        err {NetworkError}      模型下载失败
     *                        err {DataError}         模型数据出错
     * @param    {Node}        node 加载结果
     */
    loadCompleteCallback(matrix, callback) {
        const this_ = this;
        return function (err, rootNode) {
            console.log(this_.view3D)
            // TODO 可能会影响工程信息
            // const currentNode = this_.createTransformation(rootNode, matrix);
            this_.drwingRootNode = rootNode;
            // 创建天空盒
            this_.view3D.displaySkybox(true);
            // 创建根节点
            this_.drawingRootNode = rootNode;
            const groupRootNode = this_.view3D.NodeFactory.createGroup([rootNode]);
            // 交换根节点
            this_.view3D.swapRootNode(groupRootNode);
            this_.currentRootNode = groupRootNode;
            // 切换视角
            this_.view3D.calcNodesFocusedViewpoint({
                nodes: [groupRootNode],
                callback : function (err, viewpoint) {
                    if (!err){
                        this_.view3D.translateViewpoint(viewpoint, 1000);
                        callback(viewpoint.center)
                    }
                }
            })
        };
    }

    locationGround(){
        const this_ = this;
        this_.view3D.calcNodesFocusedViewpoint({
            nodes: [this_.currentRootNode],
            callback : function (err, viewpoint) {
                if (!err){
                    this_.view3D.translateViewpoint(viewpoint, 1000);
                }
            }
        })
    }


    async createFactory(server){
        if (!this.factory){
            const option = {
                server: server,
                errorHandler: function (err) {
                    // TODO 添加提示信息
                    throw Error(err.message);
                }
            }
            this.factory = await new Promise((resolve, reject) => {
                vic.createFactory(option, function (err, _factory) {
                    if (!err){
                        resolve(_factory);
                    }else{
                        // brs連接异常
                        reject(err.message);
                        throw Error(err.message);
                    }
                });
            });
        }
    }

    /**
     * 创建View
     */
    async createView3D() {
        const this_ = this;
        let sceneElement = $("#" + this.sceneId);
        console.log(sceneElement);
        this.view3D = await new Promise((resolve, reject) => {
            this_.factory.createView3D(sceneElement[0], function (err, _view3D) {
                if (!err){
                    this_.isLoadModel = true;

                    _view3D.displayNavigator();

                    resolve(_view3D);
                }
            });
        });
    }


    /**
     * 创建MatrixTransform节点
     *
     * @param    {Node}      node      需要创建MatrixTransform的节点
     * @param    {String}    matrix    模型偏移量
     */
    createTransformation (node, matrix) {
        matrix = JSON.parse(matrix);
        let mat = Matrix.mat4.create();
        _.forEach(matrix, function (value, i) {
            mat[i] = value;
        });
        this.currentNodeMatrix = mat;
        let mtNode = this.view3D.NodeFactory.createTransformation(mat, [node]);
        return mtNode;
    }


    /**
     * 根据handle获取Node
     * @param handles {Array<String>}
     * @returns {Promise<void>}
     */
    async getNodes(handles){
        const this_ = this;
        return new Promise((resolve, reject) => {
            this.view3D.findChildren(this_.currentRootNode, handles, function (err, nodes) {
                if (!err){
                    resolve(nodes);
                }else {
                    reject();
                }
            });
        })
    }

    /**
     * 设置左键交互
     * @returns {Promise<void>}
     */
    async createInteraction(){
        const this_ = this;
        const interaction = this.view3D.InteractionFactory.createPointPickInteraction(function (err, msg) {
            console.log(msg)
            this_.pickHandler({
                msg : msg
            });
            try {
                const pickedNode = msg.picked[0].nodePath[0] ? msg.picked[0].nodePath[0] : msg.picked[1].nodePath[0];
                // console.log("我点击了一个模型，对象是", pickedNode);
                //
                // const effect = this_.view3D.EffectFactory.createColorChangeEffect([pickedNode], COLOR("rgb(134, 78, 45)"));
                // this_.view3D.addEffect(effect);
                this_.callback(msg.picked[0].position);
                this_.changeColorBypick([pickedNode]);
                this_.currentNode = pickedNode;
            }catch (e){
                this_.changeColorBypick([]);
                this_.currentNode = null;
            }

        });
        this.view3D.swapInteraction(interaction);
    }

    async locationNode(handel){
        const this_ = this;
        const nodes = await this.getNodes([handel]);
        // this_.view3D.calcNodesFocusedViewpoint({
        //     nodes: [node],
        //     callback : function (err, viewpoint) {
        //         if (!err){
        //             this_.view3D.translateViewpoint(viewpoint, 1000);
        //         }
        //     }
        // })
        //
        // const pos = await calcAABBCenterPos([node]);

        this.changeColorBypick(nodes, true);
    }




    /**
     * 鼠标点击模型后改变模型颜色，可多选
     * 如果点击一个已变色的模型，那么所有模型都取消变色
     * @param {ItemModel} itemModel
     */
    async changeColorBypick(nodes, isLocation){
        if (this.effect){
            this.view3D.removeEffect(this.effect);
        }
        if (nodes.length === 0){
            return;
        }
        this.effect = this.view3D.EffectFactory.createColorChangeEffect(nodes, COLOR("#ee0000"));
        this.view3D.addEffect(this.effect);

        // 对模型定位
        if (isLocation){
            const this_ = this;
            this_.view3D.calcNodesFocusedViewpoint({
                nodes: nodes,
                callback : function (err, viewpoint) {
                    if (!err){
                        this_.view3D.translateViewpoint(viewpoint, 1000);

                        this_.osgbModelsAdapter.createLine(viewpoint.eye, viewpoint.center)

                    }
                }
            })
        }
    }


    async getViewPoint(){
        return await  new Promise((resolve, reject) => {
            this.view3D.acquireViewpoint(function (err, viewPoint) {
                resolve(viewPoint);
            });
        })
    }

    /**
     * 计算当前模型包围盒中心坐标
     * @param	[Object]	node	 当前节点
     * @param	[Function]  callback
     * @param	[Object]    pos      包围盒的位置坐标对象
     * @param	[vec3]      pos.bot  底部中心点
     * @param	[vec3]      pos.top  顶部中心点
     * @param	[vec3]      center   中心点
     */
    async calcAABBCenterPos(nodes) {
        return await new Promise((resolve, reject) => {
            this.view3D.calcAABB(nodes, function(err, aabb) {
                const min = aabb.min;
                const max = aabb.max;
                const bot = Matrix.vec3.fromValues(max[0], max[1], min[2]);
                const top = Matrix.vec3.fromValues(min[0], min[1], max[2]);
                const pos = {
                    bot : Matrix.vec3.fromValues((min[0]+bot[0])/2, (min[1]+bot[1])/2, (min[2]+bot[2])/2),
                    top : Matrix.vec3.fromValues((max[0]+top[0])/2, (max[1]+top[1])/2, (max[2]+top[2])/2),
                    center : Matrix.vec3.fromValues((min[0]+max[0])/2, (min[1]+max[1])/2, (min[2]+max[2])/2)
                };
                resolve(pos);
            });
        })
    }

    cvtViewPoint(viewpointObj){
        const eye = cvtVec3(viewpointObj.eye);
        const center = cvtVec3(viewpointObj.center);
        const up = cvtVec3(viewpointObj.up);
        return new vic.Viewpoint(eye, center, up);
    }

    // 定位到PGF模型
    focusPGFModel() {
        const this_ = this;
        if (this.currentRootNode) {
            this_.view3D.calcNodesFocusedViewpoint({
                nodes: [this_.currentRootNode],
                callback: function (err, viewpoint) {
                    if (!err) {
                        this_.view3D.translateViewpoint(viewpoint, 1000);
                    }
                }
            });
        }
    }

    // 定位到OSGB模型
    focusOSGBModel() {
        const this_ = this;
        if (this.osgbModelsAdapter && this.osgbModelsAdapter.node) {
            this_.view3D.calcNodesFocusedViewpoint({
                nodes: [this_.osgbModelsAdapter.node],
                callback: function (err, viewpoint) {
                    if (!err) {
                        this_.view3D.translateViewpoint(viewpoint, 1000);
                    }
                }
            });
        }
    }
}

const cvtVec3 = function (numArray) {
    const vec3 = Matrix.vec3.fromValues(numArray[0], numArray[1], numArray[2]);
    return vec3;
};

