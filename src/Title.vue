<template>
  <div class="transformation-controls">
    <!-- 视角定位控制 -->
    <div class="control-group">
      <h4>视角定位</h4>
      <el-row>
        <el-col :span="12">
          <el-button @click="focusPGFModel" size="small" type="success" icon="el-icon-location">
            定位PGF模型
          </el-button>
        </el-col>
        <el-col :span="12">
          <el-button @click="focusOSGBModel" size="small" type="success" icon="el-icon-location">
            定位OSGB模型
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 测试按钮 -->
    <div class="control-group">
      <h4>测试</h4>
      <el-row>
        <el-col :span="12">
          <el-button @click="testTranslation" size="small" type="info">
            测试平移
          </el-button>
        </el-col>
        <el-col :span="12">
          <el-button @click="testRotation" size="small" type="info">
            测试旋转
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 平移控制 -->
    <div class="control-group">
      <h4>平移控制</h4>
      <el-row>
        <el-col :span="8">
          <label>X轴</label>
          <el-input-number v-model="translateX" :step="1000" size="small"
                          @change="updateTransformMatrix" controls-position="right"></el-input-number>
        </el-col>
        <el-col :span="8">
          <label>Y轴</label>
          <el-input-number v-model="translateY" :step="1000" size="small"
                          @change="updateTransformMatrix" controls-position="right"></el-input-number>
        </el-col>
        <el-col :span="8">
          <label>Z轴</label>
          <el-input-number v-model="translateZ" :step="1000" size="small"
                          @change="updateTransformMatrix" controls-position="right"></el-input-number>
        </el-col>
      </el-row>
      <!-- 快速平移按钮 -->
      <el-row style="margin-top: 10px;">
        <el-col :span="8">
          <el-button-group>
            <el-button size="mini" @click="quickTranslate('x', -5000)">X-</el-button>
            <el-button size="mini" @click="quickTranslate('x', 5000)">X+</el-button>
          </el-button-group>
        </el-col>
        <el-col :span="8">
          <el-button-group>
            <el-button size="mini" @click="quickTranslate('y', -5000)">Y-</el-button>
            <el-button size="mini" @click="quickTranslate('y', 5000)">Y+</el-button>
          </el-button-group>
        </el-col>
        <el-col :span="8">
          <el-button-group>
            <el-button size="mini" @click="quickTranslate('z', -5000)">Z-</el-button>
            <el-button size="mini" @click="quickTranslate('z', 5000)">Z+</el-button>
          </el-button-group>
        </el-col>
      </el-row>
    </div>

    <!-- 旋转控制 -->
    <div class="control-group">
      <h4>旋转控制 (度)</h4>
      <el-row>
        <el-col :span="8">
          <label>X轴</label>
          <el-input-number v-model="rotateX" :step="15" size="small" :min="-360" :max="360"
                          @change="updateTransformMatrix" controls-position="right"></el-input-number>
        </el-col>
        <el-col :span="8">
          <label>Y轴</label>
          <el-input-number v-model="rotateY" :step="15" size="small" :min="-360" :max="360"
                          @change="updateTransformMatrix" controls-position="right"></el-input-number>
        </el-col>
        <el-col :span="8">
          <label>Z轴</label>
          <el-input-number v-model="rotateZ" :step="15" size="small" :min="-360" :max="360"
                          @change="updateTransformMatrix" controls-position="right"></el-input-number>
        </el-col>
      </el-row>
      <!-- 快速旋转按钮 -->
      <el-row style="margin-top: 10px;">
        <el-col :span="8">
          <el-button-group>
            <el-button size="mini" @click="quickRotate('x', -15)">X-</el-button>
            <el-button size="mini" @click="quickRotate('x', 15)">X+</el-button>
          </el-button-group>
        </el-col>
        <el-col :span="8">
          <el-button-group>
            <el-button size="mini" @click="quickRotate('y', -15)">Y-</el-button>
            <el-button size="mini" @click="quickRotate('y', 15)">Y+</el-button>
          </el-button-group>
        </el-col>
        <el-col :span="8">
          <el-button-group>
            <el-button size="mini" @click="quickRotate('z', -15)">Z-</el-button>
            <el-button size="mini" @click="quickRotate('z', 15)">Z+</el-button>
          </el-button-group>
        </el-col>
      </el-row>
    </div>

    <!-- 缩放控制 -->
    <div class="control-group">
      <h4>缩放控制</h4>
      <el-row>
        <el-col :span="16">
          <el-input-number v-model="scale" :step="0.1" :min="0.1" :max="5" size="small"
                          @change="updateTransformMatrix" controls-position="right"></el-input-number>
        </el-col>
        <el-col :span="8">
          <el-button-group>
            <el-button size="mini" @click="quickScale(0.9)">缩小</el-button>
            <el-button size="mini" @click="quickScale(1.1)">放大</el-button>
          </el-button-group>
        </el-col>
      </el-row>
    </div>

    <!-- 控制按钮组 -->
    <div class="control-group">
      <el-row>
        <el-col :span="12">
          <el-button @click="resetTransformation" size="small" type="warning" icon="el-icon-refresh">
            重置变换
          </el-button>
        </el-col>
        <el-col :span="12">
          <el-button @click="applyPreset" size="small" type="primary" icon="el-icon-setting">
            应用预设
          </el-button>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>

import CalculateNodeInBox from "./Util/CalculateNodeInBox";
import ScaffoldOperation from "./Util/ScaffoldOperation";
import CylinderFactory from "./Util/CylinderFactory";
import Polygon from "./Util/Polygon";
import FloatingPanel from "./FloatingPanel"
import Matrix from "gl-matrix-double";
import ClipBox from "./Util/ClipBox";
import PatrolBuilder from "./Util/PatrolBuilder";
import Person from "./Util/Person";

export default {
    name: "Title",
    props: ["render3D"],
    components:{
        FloatingPanel
    },
    data() {
        return {
            sa: false,
            handle: "",
            nodes: Array,
            view3D: null,
            position: Matrix.vec3.fromValues(1257446.75, 746085.0625, 107930.8828125),
            positions: [],
            effect: null,
            clipBox: null,
            // 变换参数
            translateX: 0,
            translateY: 0,
            translateZ: 0,
            rotateX: 0,
            rotateY: 0,
            rotateZ: 0,
            scale: 1,

            // 变换矩阵
            mat: null
        };
    },
    mounted() {
        // 初始化变换矩阵
        this.mat = Matrix.mat4.create();
        Matrix.mat4.identity(this.mat);
    },
    methods: {
        // 定位到PGF模型
        focusPGFModel() {
            if (this.render3D) {
                this.render3D.focusPGFModel();
            }
        },

        // 定位到OSGB模型
        focusOSGBModel() {
            if (this.render3D) {
                this.render3D.focusOSGBModel();
            }
        },

        // 统一的变换更新方法
        updateTransformMatrix() {
            console.log('更新变换矩阵:', {
                translate: [this.translateX, this.translateY, this.translateZ],
                rotate: [this.rotateX, this.rotateY, this.rotateZ],
                scale: this.scale
            });

            // 重置用户变换矩阵
            Matrix.mat4.identity(this.mat);

            // 1. 应用缩放
            if (this.scale !== 1) {
                Matrix.mat4.scale(this.mat, this.mat, [this.scale, this.scale, this.scale]);
            }

            // 2. 应用旋转
            if (this.rotateX !== 0) {
                const radX = Matrix.glMatrix.toRadian(this.rotateX);
                Matrix.mat4.rotateX(this.mat, this.mat, radX);
            }
            if (this.rotateY !== 0) {
                const radY = Matrix.glMatrix.toRadian(this.rotateY);
                Matrix.mat4.rotateY(this.mat, this.mat, radY);
            }
            if (this.rotateZ !== 0) {
                const radZ = Matrix.glMatrix.toRadian(this.rotateZ);
                Matrix.mat4.rotateZ(this.mat, this.mat, radZ);
            }

            // 3. 应用平移
            if (this.translateX !== 0 || this.translateY !== 0 || this.translateZ !== 0) {
                Matrix.mat4.translate(this.mat, this.mat, [this.translateX, this.translateY, this.translateZ]);
            }

            console.log('用户变换矩阵:', this.mat);

            // 应用变换
            if (this.render3D && this.render3D.osgbModelsAdapter) {
                this.render3D.osgbModelsAdapter.updateTransformation(this.mat);
            } else {
                console.warn('render3D 或 osgbModelsAdapter 未初始化');
            }
        },

        resetTransformation() {
            this.translateX = 0;
            this.translateY = 0;
            this.translateZ = 0;
            this.rotateX = 0;
            this.rotateY = 0;
            this.rotateZ = 0;
            this.scale = 1;

            Matrix.mat4.identity(this.mat);

            if (this.render3D && this.render3D.osgbModelsAdapter) {
                // 使用新的重置方法，恢复到基础变换状态
                this.render3D.osgbModelsAdapter.resetUserTransformation();
            }
        },

        // 测试平移
        testTranslation() {
            this.translateX = 10000;
            this.translateY = 0;
            this.translateZ = 0;
            this.updateTransformMatrix();
        },

        // 测试旋转
        testRotation() {
            this.rotateX = 0;
            this.rotateY = 0;
            this.rotateZ = 45;
            this.updateTransformMatrix();
        },

        // 快速平移
        quickTranslate(axis, value) {
            switch(axis) {
                case 'x':
                    this.translateX += value;
                    break;
                case 'y':
                    this.translateY += value;
                    break;
                case 'z':
                    this.translateZ += value;
                    break;
            }
            this.updateTransformMatrix();
        },

        // 快速旋转
        quickRotate(axis, value) {
            switch(axis) {
                case 'x':
                    this.rotateX = (this.rotateX + value) % 360;
                    break;
                case 'y':
                    this.rotateY = (this.rotateY + value) % 360;
                    break;
                case 'z':
                    this.rotateZ = (this.rotateZ + value) % 360;
                    break;
            }
            this.updateTransformMatrix();
        },

        // 快速缩放
        quickScale(factor) {
            this.scale = Math.max(0.1, Math.min(5, this.scale * factor));
            this.updateTransformMatrix();
        },

        // 应用预设变换
        applyPreset() {
            // 示例预设：轻微旋转和平移
            this.translateX = 5000;
            this.translateY = 0;
            this.translateZ = 2000;
            this.rotateX = 0;
            this.rotateY = 0;
            this.rotateZ = 30;
            this.scale = 1.2;
            this.updateTransformMatrix();
        }
    },

    watch: {
        render3D: {
            async handler(newRender3D, oldRender3D){

                this.handle = newRender3D && newRender3D.currentNode && newRender3D.currentNode.getName();

                if (this.view3D){
                    return;
                }
                this.render3D.positionsWatch((position) =>  {
                    this.positions.push(position);
                    console.log(this.positions)
                })
                this.view3D = this.render3D.view3D;
                this.sa = true;
            },
            deep: true,
            immediate:true
        },
    }
}
</script>

<style scoped>
.transformation-controls {
  padding: 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  margin: 10px;
  max-width: 450px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e0e0e0;
}

.control-group {
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.control-group h4 {
  margin-bottom: 15px;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 2px solid #3498db;
  padding-bottom: 5px;
}

.el-row {
  margin-bottom: 10px;
}

.el-col {
  padding: 0 5px;
}

.el-col label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
  font-weight: 500;
}

.el-button {
  width: 100%;
}

.el-button-group .el-button {
  width: auto;
  padding: 5px 8px;
  font-size: 12px;
}

.el-input-number {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .transformation-controls {
    max-width: 100%;
    margin: 5px;
    padding: 15px;
  }

  .control-group {
    padding: 10px;
  }

  .control-group h4 {
    font-size: 14px;
  }
}
</style>

















