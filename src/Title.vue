<template>
  <div class="transformation-controls">
    <!-- 视角定位控制 -->
    <div class="control-group">
      <h4>视角定位</h4>
      <el-row>
        <el-col :span="12">
          <el-button @click="focusPGFModel" size="small" type="success" icon="el-icon-location">
            定位PGF模型
          </el-button>
        </el-col>
        <el-col :span="12">
          <el-button @click="focusOSGBModel" size="small" type="success" icon="el-icon-location">
            定位OSGB模型
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 测试按钮 -->
    <div class="control-group">
      <h4>测试</h4>
      <el-row>
        <el-col :span="12">
          <el-button @click="testTranslation" size="small" type="info">
            测试平移
          </el-button>
        </el-col>
        <el-col :span="12">
          <el-button @click="testRotation" size="small" type="info">
            测试旋转
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 平移控制 -->
    <div class="control-group">
      <h4>平移</h4>
      <el-row>
        <el-col :span="8">
          <el-input-number v-model="translateX" :step="1000" size="small" placeholder="X" 
                          @change="updateTransformMatrix"></el-input-number>
        </el-col>
        <el-col :span="8">
          <el-input-number v-model="translateY" :step="1000" size="small" placeholder="Y"
                          @change="updateTransformMatrix"></el-input-number>
        </el-col>
        <el-col :span="8">
          <el-input-number v-model="translateZ" :step="1000" size="small" placeholder="Z"
                          @change="updateTransformMatrix"></el-input-number>
        </el-col>
      </el-row>
    </div>

    <!-- 旋转控制 -->
    <div class="control-group">
      <h4>旋转</h4>
      <el-row>
        <el-col :span="8">
          <el-input-number v-model="rotateX" :step="15" size="small" placeholder="X轴"
                          @change="updateTransformMatrix"></el-input-number>
        </el-col>
        <el-col :span="8">
          <el-input-number v-model="rotateY" :step="15" size="small" placeholder="Y轴"
                          @change="updateTransformMatrix"></el-input-number>
        </el-col>
        <el-col :span="8">
          <el-input-number v-model="rotateZ" :step="15" size="small" placeholder="Z轴"
                          @change="updateTransformMatrix"></el-input-number>
        </el-col>
      </el-row>
    </div>

    <!-- 缩放控制 -->
    <div class="control-group">
      <h4>缩放</h4>
      <el-input-number v-model="scale" :step="0.1" :min="0.1" :max="5" size="small"
                      @change="updateTransformMatrix"></el-input-number>
    </div>

    <!-- 重置按钮 -->
    <el-button @click="resetTransformation" size="small" type="warning">重置变换</el-button>
  </div>
</template>

<script>

import CalculateNodeInBox from "./Util/CalculateNodeInBox";
import ScaffoldOperation from "./Util/ScaffoldOperation";
import CylinderFactory from "./Util/CylinderFactory";
import Polygon from "./Util/Polygon";
import FloatingPanel from "./FloatingPanel"
import Matrix from "gl-matrix-double";
import ClipBox from "./Util/ClipBox";
import PatrolBuilder from "./Util/PatrolBuilder";
import Person from "./Util/Person";

export default {
    name: "Title",
    props: ["render3D"],
    components:{
        FloatingPanel
    },
    data() {
        return {
            sa: false,
            handle: "",
            nodes: Array,
            view3D: null,
            position: Matrix.vec3.fromValues(1257446.75, 746085.0625, 107930.8828125),
            positions: [],
            effect: null,
            clipBox: null,
            // 变换参数
            translateX: 0,
            translateY: 0,
            translateZ: 0,
            rotateX: 0,
            rotateY: 0,
            rotateZ: 0,
            scale: 1,
            
            // 变换矩阵
            mat: null
        };
    },
    mounted() {
        // 初始化变换矩阵
        this.mat = Matrix.mat4.create();
        Matrix.mat4.identity(this.mat);
    },
    methods: {
        // 定位到PGF模型
        focusPGFModel() {
            if (this.render3D) {
                this.render3D.focusPGFModel();
            }
        },

        // 定位到OSGB模型
        focusOSGBModel() {
            if (this.render3D) {
                this.render3D.focusOSGBModel();
            }
        },

        // 统一的变换更新方法
        updateTransformMatrix() {
            console.log('更新变换矩阵:', {
                translate: [this.translateX, this.translateY, this.translateZ],
                rotate: [this.rotateX, this.rotateY, this.rotateZ],
                scale: this.scale
            });

            // 重置用户变换矩阵
            Matrix.mat4.identity(this.mat);
            
            // 1. 应用缩放
            if (this.scale !== 1) {
                Matrix.mat4.scale(this.mat, this.mat, [this.scale, this.scale, this.scale]);
            }
            
            // 2. 应用旋转
            if (this.rotateX !== 0) {
                const radX = Matrix.glMatrix.toRadian(this.rotateX);
                Matrix.mat4.rotateX(this.mat, this.mat, radX);
            }
            if (this.rotateY !== 0) {
                const radY = Matrix.glMatrix.toRadian(this.rotateY);
                Matrix.mat4.rotateY(this.mat, this.mat, radY);
            }
            if (this.rotateZ !== 0) {
                const radZ = Matrix.glMatrix.toRadian(this.rotateZ);
                Matrix.mat4.rotateZ(this.mat, this.mat, radZ);
            }
            
            // 3. 应用平移
            if (this.translateX !== 0 || this.translateY !== 0 || this.translateZ !== 0) {
                Matrix.mat4.translate(this.mat, this.mat, [this.translateX, this.translateY, this.translateZ]);
            }
            
            console.log('用户变换矩阵:', this.mat);
            
            // 应用变换
            if (this.render3D && this.render3D.osgbModelsAdapter) {
                this.render3D.osgbModelsAdapter.updateTransformation(this.mat);
            } else {
                console.warn('render3D 或 osgbModelsAdapter 未初始化');
            }
        },

        resetTransformation() {
            this.translateX = 0;
            this.translateY = 0;
            this.translateZ = 0;
            this.rotateX = 0;
            this.rotateY = 0;
            this.rotateZ = 0;
            this.scale = 1;
            
            Matrix.mat4.identity(this.mat);
            
            if (this.render3D && this.render3D.osgbModelsAdapter) {
                this.render3D.osgbModelsAdapter.updateTransformation(this.mat);
            }
        },

        // 测试平移
        testTranslation() {
            this.translateX = 10000;
            this.translateY = 0;
            this.translateZ = 0;
            this.updateTransformMatrix();
        },

        // 测试旋转
        testRotation() {
            this.rotateX = 0;
            this.rotateY = 0;
            this.rotateZ = 45;
            this.updateTransformMatrix();
        }
    },

    watch: {
        render3D: {
            async handler(newRender3D, oldRender3D){

                this.handle = newRender3D && newRender3D.currentNode && newRender3D.currentNode.getName();

                if (this.view3D){
                    return;
                }
                this.render3D.positionsWatch((position) =>  {
                    this.positions.push(position);
                    console.log(this.positions)
                })
                this.view3D = this.render3D.view3D;
                this.sa = true;
            },
            deep: true,
            immediate:true
        },
    }
}
</script>

<style scoped>
.transformation-controls {
  padding: 20px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  margin: 10px;
  max-width: 400px;
}

.control-group {
  margin-bottom: 20px;
}

.control-group h4 {
  margin-bottom: 10px;
  color: #333;
  font-size: 14px;
}

.el-row {
  margin-bottom: 10px;
}

.el-col {
  padding: 0 5px;
}

.el-button {
  width: 100%;
}

.el-input-number {
  width:
 100%;
}
</style>

















