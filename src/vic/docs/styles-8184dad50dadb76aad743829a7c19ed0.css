.signature .chainable { background-color: #00aa00 }
        .signature .deprecated {
          background-color: #aa0000;
        }
        .deprecated-box {
          border: 2px solid #aa0000;
        }
        .deprecated-box strong {
          color: white;
          background-color: #aa0000;
        }
          .deprecated-tag-box {
            text-align: center;
            color: #600;
            background-color: #fee;
          }
          .deprecated-tag-box strong {
            text-transform: uppercase;
            border-radius: 2px;
            padding: 0 3px;
          }

        .enum-box {
          color: #060;
          background-color: #efe;
          text-align: center;
        }

        .signature .experimental {
          color: #a00;
          border: 1px dashed #a00;
          background-color: #fee;
        }
        .experimental-box {
          border: 2px dashed #ccc;
        }
        .experimental-box strong {
          margin: 0 3px;
          border: 2px dashed #a00;
          color: #a00;
        }
          .deprecated-tag-box {
            text-align: center;
            color: #600;
            background-color: #fee;
          }
          .deprecated-tag-box strong {
            text-transform: uppercase;
            border-radius: 2px;
            padding: 0 3px;
          }

        .signature .new {
          color: #484848;
          background-color: #F5D833;
        }

.preventable-box { text-align: center }
        .signature .private {
          background-color: #FD6B1B; /* orange */
        }
        .private-box {
          background-color: #fee;
          text-align: center;
          color: #600;
          margin-bottom: 1em;
        }

.signature .protected { background-color: #9B86FC }
        .signature .removed {
          color: #aa0000;
          background-color: transparent;
          border: 1px solid #aa0000;
          text-decoration: line-through;
        }
        .removed-box {
          border: 2px solid #aa0000;
        }
        .removed-box strong {
          color: #aa0000;
          border: 2px solid #aa0000;
          background-color: transparent;
          text-decoration: line-through;
        }
          .deprecated-tag-box {
            text-align: center;
            color: #600;
            background-color: #fee;
          }
          .deprecated-tag-box strong {
            text-transform: uppercase;
            border-radius: 2px;
            padding: 0 3px;
          }

.signature .required { background-color: #484848 }
.signature .static { background-color: #484848 }
        .template-box {
          text-align: center;
          background-color: #eee;
        }
#search-dropdown .icon-cfg { background-image: url(member-icons/cfg.png); background-repeat: no-repeat; }
.members .members-section .icon-cfg { background-image: url(member-icons/cfg.png); background-repeat: no-repeat; }
.members .comments-section .icon-cfg { background-image: url(member-icons/cfg.png); background-repeat: no-repeat; }
.class-overview .x-toolbar.member-links .icon-cfg { background-image: url(member-icons/cfg.png); background-repeat: no-repeat; }
#search-dropdown .icon-property { background-image: url(member-icons/property.png); background-repeat: no-repeat; }
.members .members-section .icon-property { background-image: url(member-icons/property.png); background-repeat: no-repeat; }
.members .comments-section .icon-property { background-image: url(member-icons/property.png); background-repeat: no-repeat; }
.class-overview .x-toolbar.member-links .icon-property { background-image: url(member-icons/property.png); background-repeat: no-repeat; }
#search-dropdown .icon-method { background-image: url(member-icons/method.png); background-repeat: no-repeat; }
.members .members-section .icon-method { background-image: url(member-icons/method.png); background-repeat: no-repeat; }
.members .comments-section .icon-method { background-image: url(member-icons/method.png); background-repeat: no-repeat; }
.class-overview .x-toolbar.member-links .icon-method { background-image: url(member-icons/method.png); background-repeat: no-repeat; }
#search-dropdown .icon-event { background-image: url(member-icons/event.png); background-repeat: no-repeat; }
.members .members-section .icon-event { background-image: url(member-icons/event.png); background-repeat: no-repeat; }
.members .comments-section .icon-event { background-image: url(member-icons/event.png); background-repeat: no-repeat; }
.class-overview .x-toolbar.member-links .icon-event { background-image: url(member-icons/event.png); background-repeat: no-repeat; }
#search-dropdown .icon-css_var { background-image: url(member-icons/css_var.png); background-repeat: no-repeat; }
.members .members-section .icon-css_var { background-image: url(member-icons/css_var.png); background-repeat: no-repeat; }
.members .comments-section .icon-css_var { background-image: url(member-icons/css_var.png); background-repeat: no-repeat; }
.class-overview .x-toolbar.member-links .icon-css_var { background-image: url(member-icons/css_var.png); background-repeat: no-repeat; }
#search-dropdown .icon-css_mixin { background-image: url(member-icons/css_mixin.png); background-repeat: no-repeat; }
.members .members-section .icon-css_mixin { background-image: url(member-icons/css_mixin.png); background-repeat: no-repeat; }
.members .comments-section .icon-css_mixin { background-image: url(member-icons/css_mixin.png); background-repeat: no-repeat; }
.class-overview .x-toolbar.member-links .icon-css_mixin { background-image: url(member-icons/css_mixin.png); background-repeat: no-repeat; }