<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">&quot;use strict&quot;;

var jQuery = require(&quot;jquery&quot;);
var Utility = require(&quot;./Utility.js&quot;);
var handleMouseEvent = require(&quot;./MouseEvent.js&quot;);
var getStats = require(&quot;./getStats&quot;);
var Condicio = require(&quot;condicio&quot;);

function StreamDisplayer(session, viewID, parentElem, turnServer, callback) {
    var _this = this;

	this.session = session;
	this.viewID = viewID;
	this.parentElem = parentElem;
	
	// 创建播放div元素
	this.displayElem = this.createDisplayElem();
	
	// 创建流播放器
	this.video = this.createVideo();
	
	// 处理大小变化
    this.handleDisplayElemSizeChange();
	
	// 处理鼠标事件
	handleMouseEvent(session, viewID, this.displayElem);
	
	// 创建P2P连接
	this.createP2PCommunication(turnServer, callback);
};

StreamDisplayer.prototype.release = function(){
	this.parentElem.removeChild(this.displayElem);
};

// 创建显示元素
StreamDisplayer.prototype.createDisplayElem = function() {
	var displayElem = document.createElement('div');
	this.parentElem.appendChild(displayElem);
	
	// TODO:目标是让外部能够区分显示的div，这里应该由外部想办法区分，而不应该内部处理
	jQuery(this.displayElem).attr(&quot;id&quot;,&quot;bcd&quot;); 
	
	// 不让选中，避免引起闪屏
	displayElem.onselectstart = function(){ return false; };
	
	return displayElem;
};

// 创建视频播放器
StreamDisplayer.prototype.createVideo = function() {
	var video = document.createElement(&quot;video&quot;);
	video.autoplay = true;	// 自动播放
	video.muted = true; 	// 不输出音频
	
	this.displayElem.appendChild(video);
	
	//阻止默认事件，保证不影响上层事件，目的是保证最上层能正确获取鼠标消息
	jQuery(video).on('mousedown', function (e){
		e.preventDefault();	
	});
	
	return video;
};

// 处理DispalyElem的大小变化
StreamDisplayer.prototype.handleDisplayElemSizeChange = function() {
	var _this = this;
	
	// 处理大小变化
    var resizeListener = function () {
        var w = jQuery(_this.parentElem).width();
        var h = jQuery(_this.parentElem).height();

        if (_this.video.width !== w || _this.video.height !== h) {
            _this.video.width = w;
            _this.video.height = h;

            _this.session.request(_this.viewID, &quot;resize&quot;, { width: w, height: h });
       }

        setTimeout(resizeListener, 200)
    }
    resizeListener();
};

// 创建PeerConnetion
function createPeerConnection(turnServer) {
	var iceServer = {
		&quot;iceServers&quot;:[
			{
				&quot;urls&quot;: [&quot;turn:&quot;+turnServer.ip+&quot;:&quot;+ turnServer.port +&quot;?transport=udp&quot;,
						 &quot;turn:&quot;+turnServer.ip+&quot;:&quot;+ turnServer.port +&quot;?transport=tcp&quot;],
				&quot;username&quot;:turnServer.userName,
				&quot;credential&quot;:turnServer.password
			}
		]
	};	
	
	return new RTCPeerConnection(iceServer);
};

// 向服务器注册ICE协商函数
function registerIceCandidateCallback(session, viewID, peerConnection, callback){
	var callbackWrapper = function (error, result) {	
		if(!error) {
			var candidate = new RTCIceCandidate(result.sdpObj);
			peerConnection.addIceCandidate(candidate);
		}
		else {
			callback(error);
		}
	};
	
	var callbackID = Utility.genGUID();
	session.registerCallback(callbackID, callbackWrapper, &quot;IceCandidateCallback&quot;, true);
	session.request(viewID, &quot;registerIceCandidateCallback&quot;, {&quot;callbackID&quot;: callbackID});
}

// 向服务器请求SDP
function requestSDPOffer(session, viewID, peerConnection, callback){
	var callbackWrapper = function (error, offerSDP) {	
		if (error)
			callback(error);
		else {
			peerConnection.setRemoteDescription(new RTCSessionDescription(offerSDP.sdpObj));
			
			var createAnswerSucced = function(answer) {
				peerConnection.setLocalDescription(answer);
				
				var callbackError = function (error) {
					if(error)
						callback(error);
				};
				var callbackID = Utility.genGUID();
				session.registerCallback(callbackID, callbackError);
				
				session.request(viewID,  &quot;sendSDPAnswer&quot;, {&quot;sdpObj&quot;: answer,&quot;callbackID&quot;: callbackID});
			};
			peerConnection.createAnswer(createAnswerSucced, function (error) {
				callback(error);
			});
		}
	};
	
	var callbackID = Utility.genGUID();
	session.registerCallback(callbackID, callbackWrapper);
	session.request(viewID,  &quot;requestSDPOffer&quot;, {&quot;callbackID&quot;: callbackID});
}

// 向服务器发送状态信息
function loopSendStatsInfos(session, viewID, peerConnection) {
	
	var preStatsInfo = &quot;&quot;;
	var handleStatsFunc = function(result) {
		var results = result.results;
		
		for (var i = 0; i &lt; results.length; i++) {
			if (results[i].type == 'ssrc' &amp;&amp; !Condicio.isUndefined(results[i].googTimingFrameInfo)) {
				var statsInfo = {};
				
				var timeInfos = results[i].googTimingFrameInfo.split(&quot;,&quot;);
				statsInfo.encodeTime = Number(timeInfos[3]) - Number(timeInfos[2]);
				statsInfo.packetTime = Number(timeInfos[4]) - Number(timeInfos[3]);
				statsInfo.sendTime = Number(timeInfos[5]) - Number(timeInfos[4]);
				statsInfo.receiveTime = Number(timeInfos[9]) - Number(timeInfos[8]);
				statsInfo.bufferTime = Number(timeInfos[10]) - Number(timeInfos[9]);
				statsInfo.decodeTime = Number(timeInfos[11]) - Number(timeInfos[10]);
				
				if (preStatsInfo != JSON.stringify(statsInfo)) {				
					session.request(viewID, &quot;sendStatsInfo&quot;, { &quot;statsInfo&quot;: statsInfo });
					
					preStatsInfo = JSON.stringify(statsInfo);
				}
			}
		}
	};
	
	getStats(peerConnection, handleStatsFunc, 50);  
};

// 建立P2P通信
StreamDisplayer.prototype.createP2PCommunication = function(turnServer, callback) {
	var _this = this;
	
	var peerConnection = createPeerConnection(turnServer);
	
	peerConnection.onaddstream = function(event) {
        _this.video.srcObject = event.stream;

        loopSendStatsInfos(_this.session, _this.viewID, peerConnection);
	};
	
	peerConnection.oniceconnectionstatechange = function(ev) {
		var connection = ev.target;
		switch(connection.iceConnectionState) {
			case &quot;connected&quot;:
				callback(null);
				break;
			case &quot;failed&quot;:
				callback(&quot;create view3D failed: webrtc connect failed&quot;);
				break;
		}
	}
	
	registerIceCandidateCallback(this.session, this.viewID, peerConnection, callback);
	
	peerConnection.onicecandidate = function(event) {
		if (event.candidate !== null) {
			
			var callbackError = function (error) {
				if(error)
					callback(error);
			};
			var callbackID = Utility.genGUID();
			_this.session.registerCallback(callbackID, callbackError);
			
			_this.session.request(_this.viewID,  &quot;sendIceCandidate&quot;, {&quot;candidate&quot;: event.candidate,&quot;callbackID&quot;: callbackID});
		}
	};
	
	requestSDPOffer(this.session, this.viewID, peerConnection, callback);
};

module.exports = StreamDisplayer;</pre>
</body>
</html>
