<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">&quot;use strict&quot;;

var _ = require(&quot;lodash&quot;),
	Condicio = require(&quot;condicio&quot;),
	COLOR = require(&quot;color&quot;),
	jQuery = require(&quot;jquery&quot;),
	Effect = require(&quot;./Effect.js&quot;),
	Command = require(&quot;./Command.js&quot;),
	ParamValidation = require(&quot;../ParamValidation.js&quot;),
	CvtForJson = require(&quot;../CvtForJson.js&quot;),
	Utility = require(&quot;../Utility.js&quot;),
	Style = require(&quot;./Style.js&quot;);
	
<span id='View3D-LightEffect'>/**
</span> * @class View3D.LightEffect
 * @extend View3D.Effect
 * 光照效果
 *
 * 创建修改光照的命令
 */
function LightEffect(session, objectID) {
	this.session = session;
    this.objectID = objectID;
};

LightEffect.prototype = _.create(Effect.prototype, {
	constructor: LightEffect
});

<span id='View3D-LightEffect-method-createChangeLightCommand'>/**
</span> * &lt;a href=&quot;https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/75956914&quot;&gt;参数相关详情请参考&lt;/a&gt;
 * @param  {Object}		lightStyle  					光照基本属性，参照View3D.Style.LightStyle的定义
 * @return {View3D.Command}
 */
LightEffect.prototype.createChangeLightCommand = function(lightStyle){

	Condicio.checkIsObject(lightStyle, &quot;The type of lightStyle must be 'Object'!&quot;);
	var newStyle = jQuery.extend({}, Style.LightStyle, lightStyle);	
	ParamValidation.checkIsLightStyle(newStyle);
	
	var lightStyle = {
		diffuse: newStyle.diffuse.rgbaArray(),
		specular: newStyle.specular.rgbaArray(),
		ambient: newStyle.ambient.rgbaArray(),		
		position: CvtForJson.cvtVec4(newStyle.position),
		spotCutoff: newStyle.spotCutoff,
		range: newStyle.range,
		direction: CvtForJson.cvtVec3(newStyle.direction)
	} 
	
	var commandID = Utility.genGUID();
	
	var param = {
		commandID: commandID,
		lightStyle: lightStyle
	}
	
    this.session.request(this.objectID, &quot;createChangeLightCommand&quot;, param);
	
	return new Command(commandID);
	
};

module.exports = LightEffect;</pre>
</body>
</html>
