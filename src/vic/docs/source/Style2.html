<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">var COLOR = require(&quot;color&quot;);
	
var black = COLOR('rgb(0, 0, 0)').alpha(1);
var white = COLOR('rgb(255, 255, 255)').alpha(1);
var red = COLOR('rgb(255, 0, 0)').alpha(1);

<span id='ViewPID-Style'>/**
</span> * @class ViewPID.Style
 * 标准属性定义
 */
var Style = { };
 
<span id='ViewPID-Style-LineStyle'>/**
</span> * @class ViewPID.Style.LineStyle
 * 标准线属性定义 
 */
Style.LineStyle = {
<span id='ViewPID-Style-LineStyle-property-color'>	/**
</span>	 * @property {Color}  [color=white] 	线色，颜色中的alpha值无效
	 */
	color: white,
<span id='ViewPID-Style-LineStyle-property-width'>	/**
</span>	 * @property {Number} [width=0] 		线宽（单位：厘米）线宽取值范围（0.0，0.3，0.35，0.4，0.5，0.6，0.7，0.8，0.9，1.0
	 *										，1.2，1.4，1.58，2.0，2.11。）若为0~2.11范围内的其他值，则绘制效果与0.0相同。
	 */
	width: 0,
<span id='ViewPID-Style-LineStyle-property-type'>    /**
</span>	 * @property {String} [type=&quot;solid&quot;]	线型
	 *
	 *	- &quot;solid&quot; 	实线
	 *  - &quot;stipple&quot;	虚线
	 */
	type: 'solid'
};

<span id='ViewPID-Style-TextStyle'>/**
</span> * @class ViewPID.Style.TextStyle
 * 标准文本属性定义 
 */
Style.TextStyle = {
<span id='ViewPID-Style-TextStyle-property-color'>	/**
</span>	 * @property {Color} 	[color=white] 				字体色，颜色中的alpha值无效
	 */
	color: white,
<span id='ViewPID-Style-TextStyle-property-size'>	/**
</span>	 * @property {Number} 	[size=5] 					字体大小（毫米）,定义的是文字高度，宽度自动调整	
	 */
	size: 5,
<span id='ViewPID-Style-TextStyle-property-fontName'>	/**
</span>	 * @property {String} 	[fontName=&quot;楷体&quot;]			字体名，默认为楷体（该名与当前操作系统（c:\windows\fonts）下的字体文件名对应，若不满足时，可以安装新的字体）
	 */
	fontName: &quot;楷体&quot;
};

<span id='ViewPID-Style-TextFrameStyle'>/**
</span> * @class ViewPID.Style.TextFrameStyle
 * 标准文本框属性定义 
 */
Style.TextFrameStyle = {
<span id='ViewPID-Style-TextFrameStyle-property-showBorders'>	/**
</span>	 * @property {boolean} 	[showBorders=false]				控制文本边框的可见性（边框的颜色随字体的颜色）
	 */
	showBorders: false,
<span id='ViewPID-Style-TextFrameStyle-property-backgroundFill'>	/**
</span>	 * @property {boolean} 	[backgroundFill=false]			控制是否填充背景色
	 */
	backgroundFill: false,
<span id='ViewPID-Style-TextFrameStyle-property-backgroundFillColor'>	/**
</span>	 * @property {Color} 	[backgroundFillColor=black]		文字背景填充色，颜色中的alpha值无效
	 */
	backgroundFillColor: black,
<span id='ViewPID-Style-TextFrameStyle-property-padding'>	/**
</span>	 * @property {Number} 	[padding=2] 					文字离边框距离（毫米）
	 */ 
	padding: 2,
<span id='ViewPID-Style-TextFrameStyle-property-text'>	 /**
</span>	 * @property {Object}   [text]							文本属性，参照ViewPID.Style.TextStyle定义
	 */
	text: Style.TextStyle
};

<span id='ViewPID-Style-ArcStyle'>/**
</span> * @class ViewPID.Style.ArcStyle
 * 标准画弧属性定义
 */
 
Style.ArcStyle = {
<span id='ViewPID-Style-ArcStyle-property-color'>	/**
</span>	 * @property {Color} 	[color=red] 					弧线颜色，颜色中的alpha值无效
	 */
	color: red,
<span id='ViewPID-Style-ArcStyle-property-angle'>	/**
</span>	 * @property {Number} 	[angle=120] 					弧线所对应的圆心角：设圆弧所包含的圆心角为A(弧度表示)，凸度=tan(1/4*A)。
	 */
	angle: 120,
<span id='ViewPID-Style-ArcStyle-property-width'>	/**
</span>	 * @property {Number} 	[width=0.5] 					弧线宽度：单位：毫米；范围：大于0
	 */
	width: 0.5
};

<span id='ViewPID-Style-PolygonStyle'>/**
</span> * @class ViewPID.Style.PolygonStyle
 * 标准画多边形属性定义 
 */
Style.PolygonStyle = {
<span id='ViewPID-Style-PolygonStyle-property-color'>	/**
</span>	 * @property {Color}   [color=white] 					多边形颜色
	 */
	color: white,
<span id='ViewPID-Style-PolygonStyle-property-transparencyValue'>	/**
</span>	 * @property {Number}  [transparencyValue=1.0] 			多边形透明度,透明度（范围0~1），0为不透明，1为全透明（值：0&lt;=transparencyValue&lt;=1）
	 */
	transparencyValue: 0.0

};
 
module.exports = Style;</pre>
</body>
</html>
