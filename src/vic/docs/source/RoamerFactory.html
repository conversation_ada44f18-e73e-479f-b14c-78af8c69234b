<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">
&quot;use strict&quot;;

var _ = require(&quot;lodash&quot;),
	Roamer = require(&quot;./Roamer.js&quot;),
	Utility = require(&quot;../Utility.js&quot;),
	Matrix = require(&quot;gl-matrix-double&quot;),
	CvtForJson = require(&quot;../CvtForJson.js&quot;),
	ParamValidation = require(&quot;../ParamValidation.js&quot;),
	Condicio = require(&quot;condicio&quot;),
	jQuery = require(&quot;jquery&quot;);
	

<span id='View3D-RoamerFactory'>/**
</span> * @class View3D.RoamerFactory
 * 提供创建不同{@link Roamer}的接口
 *
 * 应用不应该构建，应该通过{@link View3D}直接获取
 */
function RoamerFactory(session, objectID) {
    this.session = session;
    this.objectID = objectID;

};

<span id='View3D-RoamerFactory-method-createFreeModeRoamer'>/**
</span> * 创建自由模式漫游器: FreeModeRoamer&lt;br&gt;
 * 新增参数可自由为左右中键绑定不同的交互。&lt;br&gt;
 * 为兼容之前版本option.opMode将会保留两个版本，若用户给定了option.opMode则以下四个给定参数leftMouseButton、rightMouseButton、middleMouseButton、mouseWheel不会生效。&lt;br&gt;
 * 若用户未给任何参数则默认的交互模式为mode1，opMode不为mode1或mode2时默认mode1。&lt;br&gt;
 * 若左右中键不想绑定任何操作，则可以给空字符串；例如：option.mouseWheel = &quot;&quot;
 *
 * @param {Object}		[option] 						创建漫游交互的选项
 * @param {String}		[option.opMode = &quot;mode1&quot;]		交互模式
 *							- &quot;mode1&quot; 	鼠标左键拖动旋转、中间滚轮拖动平移、右键拖动缩放、滑动滚轮缩放
 *  						- &quot;mode2&quot;	鼠标左键拖动旋转、右键拖动平移、滑动滚轮缩放
 *
 * @param {String}		[option.leftMouseButtonOperation = &quot;rotate&quot;]  	鼠标左键拖动可绑定旋转（rotate）或平移（pan）或缩放（zoom）
 * @param {String}		[option.rightMouseButtonOperation = &quot;zoom&quot;] 	鼠标右键拖动可绑定旋转（rotate）或平移（pan）或缩放（zoom）
 * @param {String}		[option.middleMouseButtonOperation = &quot;pan&quot;] 	中间滚轮拖动可绑定旋转（rotate）或平移（pan）或缩放（zoom）
 * @param {String}		[option.mouseWheelOperation = &quot;zoom&quot;] 			滑动滚轮可绑定缩放(zoom)，默认是zoom
 * @param {vec3[]}		[option.viewpointLimitation]					限制的可视范围（目前限制范围为Box）Box八个顶点
 *							（&lt;a href=&quot;https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/83200479&quot;&gt;详细请参考&lt;/a&gt;）
 *							缺省时表示没有限制
 * @param {vec3}		[option.fixedRotationCenter]			固定旋转中心；当设置固定旋转中心后，漫游器会固定以该点为中心进行旋转。&lt;br&gt;
 *							缺省时表示没有固定旋转中心，旋转中心会根据鼠标指向的位置动态获取
 * @param {vec3}		[option.fixedZoomCenter]	固定的缩放中心。&lt;br&gt;
 * 													缺省时，缩放操作将以鼠标指向的位置为参考，缩放步长与参考点到相机位置的距离成正比；&lt;br&gt;
 * 													当设置此选项后，漫游器的缩放操作将以此点为中心，不会再根据鼠标指向而动态变化。&lt;br&gt;
 * @param {vec3} 		[option.fixedPanCenter]		固定的平移参考点&lt;br&gt;
 * 													缺省时，平衡操作将以鼠标指向的位置为参考，平移步长与参考点到相机位置的距离成正比；&lt;br&gt;
 * 													当设置此选项后，漫游器的平移操作将以此点为中心，不会再根据鼠标指向而动态变化。&lt;br&gt;
 * @param {Number}		[option.panScale=1]
 * 平移的步长比例。可以使用这个值扩大或限制平移操作的步长。&lt;br&gt;
 * 这个值是直接乘在基础步长上的，值越小，步长越小；值越大，步长越大&lt;br&gt;
 * 所以当取值为1时，就是之前的平移步长；&lt;br&gt;
 * 取值小于1时，会缩小步长，相当于限制了平移的能力。设置为0的时候就禁用了平移。&lt;br&gt;
 * 当取值大于1时，会扩大步长。
 * 
 * @param {IntersectFilter}  [option.intersectFilter]          求交结果过滤器
 * @return {View3D.Roamer}
 */
RoamerFactory.prototype.createFreeModeRoamer = function (option) {
	var mode1 = {
		leftMouseButtonOperation : &quot;rotate&quot;,
		rightMouseButtonOperation : &quot;zoom&quot;,
		middleMouseButtonOperation : &quot;pan&quot;,
		mouseWheelOperation : &quot;zoom&quot;
	};
	var mode2 = {
		leftMouseButtonOperation : &quot;rotate&quot;,
		rightMouseButtonOperation : &quot;pan&quot;,
		middleMouseButtonOperation : &quot;&quot;,
		mouseWheelOperation : &quot;zoom&quot;,
	}
	
	var newOption = mode1;// 默认mode1

	if(!Condicio.isUndefined(option)) {
		newOption = jQuery.extend({}, mode1, option);// 此处mode1与option相同的属性进行合并时，option会覆盖mode1

		if(!Condicio.isUndefined(option.opMode)) {
			if(option.opMode === &quot;mode2&quot;) {
				newOption = jQuery.extend({}, option, mode2);
			}
			else {
				newOption = jQuery.extend({}, option, mode1);// 此处mode1会覆盖option
			}
		}
	} 

	
	Condicio.checkIsString(newOption.rightMouseButtonOperation, &quot;The type of newOption.rightMouseButtonOperation must be 'string'&quot;);
	Condicio.checkIsString(newOption.leftMouseButtonOperation, &quot;The type of newOption.leftMouseButtonOperation must be 'string'&quot;);
	Condicio.checkIsString(newOption.middleMouseButtonOperation, &quot;The type of newOption.middleMouseButtonOperation must be 'string'&quot;);
	Condicio.checkIsString(newOption.mouseWheelOperation, &quot;The type of newOption.mouseWheelOperation must be 'string'&quot;);
	
	var roamerID = Utility.genGUID();
	var params = {
		roamerID: roamerID,
		leftMBOperation : newOption.leftMouseButtonOperation,
		rightMBOperation : newOption.rightMouseButtonOperation,
		middleMBOperation : newOption.middleMouseButtonOperation,
		mouseWheelOperation : newOption.mouseWheelOperation
	};
	
	if (newOption.viewpointLimitation)
	{
		ParamValidation.checkIsVec3Array(newOption.viewpointLimitation
		, &quot;The type of option.viewpointLimitation must be vec3 array and valid!&quot;);
		Condicio.checkArgument(newOption.viewpointLimitation.length === 8, &quot;the length of option.opMode must be 8&quot;);
		params.viewPointLimitation = CvtForJson.cvtVec3Array(newOption.viewpointLimitation);
	}
	if (newOption.fixedRotationCenter)
	{
		ParamValidation.checkIsVec3(newOption.fixedRotationCenter
		, &quot;The type of option.fixedRotationCenter must be vec3!&quot;);
		params.fixedRotationCenter = CvtForJson.cvtVec3(newOption.fixedRotationCenter);
	}

	if(newOption.fixedZoomCenter)
	{
		ParamValidation.checkIsVec3(newOption.fixedZoomCenter
			, &quot;The type of option.fixedZoomCenter must be vec3!&quot;
		);
		params.fixedZoomCenter = CvtForJson.cvtVec3(newOption.fixedZoomCenter);
	}

	if(newOption.fixedPanCenter)
	{
		ParamValidation.checkIsVec3(newOption.fixedPanCenter, &quot;The type of option.fixedPanCenter must be vec3!&quot;);
		params.fixedPanCenter = CvtForJson.cvtVec3(newOption.fixedPanCenter);
	}

	if(newOption.panScale !== undefined)
	{
		Condicio.checkIsNumber(newOption.panScale, &quot;The type of option.panScale must be number!&quot;);
		Condicio.checkArgument(newOption.panScale &gt;= 0, &quot;The value of option.panScale must be greater than 0!&quot;);

		params.panScale = newOption.panScale;
	}
	else
	{
		params.panScale = 1.0;
	}
	
	if (!Condicio.isUndefined(newOption.intersectFilter))
	    params.intersectFilterID = newOption.intersectFilter.objectID;
		
    this.session.request(this.objectID, &quot;createFreeModeRoamer&quot;, params);

    return new Roamer(roamerID);
};

<span id='View3D-RoamerFactory-method-createFlyModeRoamer'>/**
</span> * 创建飞行模式漫游器: FlyModeRoamer&lt;br&gt;
 * 该漫游器移动方向分别为相机的水平方向前后、水平方向左右、竖直方向
 * 鼠标按下后拖动距离越长（相较于鼠标按下位置），鼠标控制移动和旋转的步长越大
 * 键盘鼠标的操作彼此独立，可以同时进行
 * 键盘按键设置不能为大写字母
 * 
 *  @param {Object}		[option] 									创建飞行漫游的选项&lt;br&gt;
 * 
 *  @param {String}		[option.leftMBOperation = &quot;Rotate&quot;]  		鼠标左键拖动可绑定旋转（Rotate）、水平运动（Horizontal）或竖直运动（Vertical）&lt;br&gt;
 *  @param {String}		[option.rightMBOperation = &quot;Horizontal&quot;] 	鼠标右键拖动可绑定旋转（Rotate）、水平运动（Horizontal）或竖直运动（Vertical）&lt;br&gt;
 *  @param {String}		[option.middleMBOperation = &quot;Vertical&quot;]		鼠标中键拖动可绑定旋转（Rotate）、水平运动（Horizontal）或竖直运动（Vertical）&lt;br&gt;
 *  @param {Number}		[option.keyBaseMoveSpeed = 10.0] 			设置漫游器的按键移动速度，m/s&lt;br&gt;
 *  @param {Number}		[option.mouseBaseMoveSpeed = 1.0] 			设置漫游器的鼠标拖动移动步长比例，必须大于0。值越大，移动步长越大。&lt;br&gt;
 *  @param {Number}		[option.mouseBaseRotationSpeed = 1.0]		设置漫游器的鼠标拖动旋转步长比例，必须大于0。值越大，旋转步长越大。&lt;br&gt;
 *  @param {string}		[option.keyEventMode = &quot;wsad&quot;]         	选择漫游器的按键操作模式，默认采用wsad模式&lt;br&gt;
 * 		- &quot;wsad&quot; 	w,s,a,d控制漫游器水平前后左右移动，q,e控制漫游器的竖直上下移动&lt;br&gt;
 * 		- &quot;arrow&quot; 	键盘上下左右方向键控制漫游器水平前后左右移动，q,e控制漫游器的竖直上下移动。 注意：采用该模式时需要关闭浏览器的光标浏览功能&lt;br&gt;
 *
**/
RoamerFactory.prototype.createFlyModeRoamer = function (option)
{
	var roamerID = Utility.genGUID();
	
	var wsad =
	{
		forward : 119,
		back : 115,
		right : 100,
		left : 97,
		up : 113,
		down : 101,
	}

	var arrow =
	{
		forward : 65362,
		back : 65364,
		right : 65363,
		left : 65361,
		up : 113,
		down : 101,
	}

	var newOption = {
		leftMBOperation : &quot;Rotate&quot;,
		middleMBOperation : &quot;Vertical&quot;,
		rightMBOperation : &quot;Horizontal&quot;,

		keyBaseMoveSpeed : 10.0,
		mouseBaseMoveSpeed : 1.0,
		mouseBaseRotationSpeed : 1.0,

		keyEventOption: wsad
	} ;

	if(!Condicio.isUndefined(option))
	{
		if(!Condicio.isUndefined(option.leftMBOperation))
		{
			newOption.leftMBOperation = option.leftMBOperation;
		}
		if(!Condicio.isUndefined(option.middleMBOperation))
		{
			newOption.middleMBOperation = option.middleMBOperation;
		}
		if(!Condicio.isUndefined(option.rightMBOperation))
		{
			newOption.rightMBOperation = option.rightMBOperation;
		}
		if(!Condicio.isUndefined(option.keyBaseMoveSpeed))
		{
			newOption.keyBaseMoveSpeed = option.keyBaseMoveSpeed;
		}
		if(!Condicio.isUndefined(option.mouseBaseMoveSpeed))
		{
			newOption.mouseBaseMoveSpeed = option.mouseBaseMoveSpeed;
		}
		if(!Condicio.isUndefined(option.mouseBaseRotationSpeed))
		{
			newOption.mouseBaseRotationSpeed = option.mouseBaseRotationSpeed;
		}
		if(!Condicio.isUndefined(option.keyEventMode))
		{
			Condicio.checkIsString(option.keyEventMode, &quot;The type of option.keyEventMode must be 'string'&quot;);
			Condicio.checkArgument(option.keyEventMode == &quot;wsad&quot; || option.keyEventMode == &quot;arrow&quot;, &quot;option.keyEventMode is not valid!&quot;);
			
			if(option.keyEventMode == &quot;arrow&quot;)
				newOption.keyEventOption = arrow;
		}
	}

	Condicio.checkIsString(newOption.leftMBOperation, &quot;The type of option.leftMBOperation must be 'string'&quot;);
	Condicio.checkIsString(newOption.middleMBOperation, &quot;The type of option.middleMBOperation must be 'string'&quot;);
	Condicio.checkIsString(newOption.rightMBOperation, &quot;The type of option.rightMBOperation must be 'string'&quot;);
	
	Condicio.checkIsNumber(newOption.keyBaseMoveSpeed, &quot;The type of option.keyBaseMoveSpeed must be 'Number'&quot;);
	Condicio.checkArgument(newOption.keyBaseMoveSpeed &gt; 0, &quot;The value of option.keyBaseMoveSpeed must be greater than 0!&quot;);
	Condicio.checkIsNumber(newOption.mouseBaseMoveSpeed, &quot;The type of option.mouseBaseMoveSpeed must be 'Number'&quot;);
	Condicio.checkArgument(newOption.mouseBaseMoveSpeed &gt; 0, &quot;The value of option.mouseBaseMoveSpeed must be greater than 0!&quot;);
	Condicio.checkIsNumber(newOption.mouseBaseRotationSpeed, &quot;The type of option.mouseBaseRotationSpeed must be 'Number'&quot;);
	Condicio.checkArgument(newOption.mouseBaseRotationSpeed &gt; 0, &quot;The value of option.mouseBaseRotationSpeed must be greater than 0!&quot;);

	var params = {
		roamerID: roamerID,
		leftMBOperation : newOption.leftMBOperation,
		middleMBOperation : newOption.middleMBOperation,
		rightMBOperation : newOption.rightMBOperation,
		keyBaseMoveSpeed : newOption.keyBaseMoveSpeed,
		mouseBaseMoveSpeed : newOption.mouseBaseMoveSpeed,
		mouseBaseRotationSpeed : newOption.mouseBaseRotationSpeed,
		keyEventOption : newOption.keyEventOption
	};

	this.session.request(this.objectID, &quot;createFlyModeRoamer&quot;, params);

    return new Roamer(roamerID);
};


<span id='View3D-RoamerFactory-method-createTrackLongRoamer'>/**
</span> * 创建循线模式漫游器: createTrackLongRoamer&lt;br&gt;
 * option设置与自由模式漫游器完全相同，循线操作通过注册View3D.registerTrackLongCallback回调函数，并调用View3D.setTrackLongState控制进行操作&lt;br&gt;
 * 新增参数可自由为左右中键绑定不同的交互。&lt;br&gt;
 * 为兼容之前版本option.opMode将会保留两个版本，若用户给定了option.opMode则以下四个给定参数leftMouseButton、rightMouseButton、middleMouseButton、mouseWheel不会生效。&lt;br&gt;
 * 若用户未给任何参数则默认的交互模式为mode1，opMode不为mode1或mode2时默认mode1。&lt;br&gt;
 * 若左右中键不想绑定任何操作，则可以给空字符串；例如：option.mouseWheel = &quot;&quot;
 *
 * @param {Object}		[option] 						创建漫游交互的选项
 * @param {String}		[option.opMode = &quot;mode1&quot;]		交互模式
 *							- &quot;mode1&quot; 	鼠标左键拖动旋转、中间滚轮拖动平移、右键拖动缩放、滑动滚轮缩放
 *  						- &quot;mode2&quot;	鼠标左键拖动旋转、右键拖动平移、滑动滚轮缩放
 *
 * @param {String}		[option.leftMouseButtonOperation = &quot;rotate&quot;]  	鼠标左键拖动可绑定旋转（rotate）或平移（pan）或缩放（zoom）
 * @param {String}		[option.rightMouseButtonOperation = &quot;zoom&quot;] 	鼠标右键拖动可绑定旋转（rotate）或平移（pan）或缩放（zoom）
 * @param {String}		[option.middleMouseButtonOperation = &quot;pan&quot;] 	中间滚轮拖动可绑定旋转（rotate）或平移（pan）或缩放（zoom）
 * @param {String}		[option.mouseWheelOperation = &quot;zoom&quot;] 			滑动滚轮可绑定缩放(zoom)，默认是zoom
 * @param {vec3[]}		[option.viewpointLimitation]					限制的可视范围（目前限制范围为Box）Box八个顶点
 *							（&lt;a href=&quot;https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/83200479&quot;&gt;详细请参考&lt;/a&gt;）
 *							缺省时表示没有限制
 * @param {vec3}		[option.fixedRotationCenter]			固定旋转中心；当设置固定旋转中心后，漫游器会固定以该点为中心进行旋转。&lt;br&gt;
 *							缺省时表示没有固定旋转中心，旋转中心会根据鼠标指向的位置动态获取
 * @param {vec3}		[option.fixedZoomCenter]	固定的缩放中心。&lt;br&gt;
 * 													缺省时，缩放操作将以鼠标指向的位置为参考，缩放步长与参考点到相机位置的距离成正比；&lt;br&gt;
 * 													当设置此选项后，漫游器的缩放操作将以此点为中心，不会再根据鼠标指向而动态变化。&lt;br&gt;
 * @param {vec3} 		[option.fixedPanCenter]		固定的平移参考点&lt;br&gt;
 * 													缺省时，平衡操作将以鼠标指向的位置为参考，平移步长与参考点到相机位置的距离成正比；&lt;br&gt;
 * 													当设置此选项后，漫游器的平移操作将以此点为中心，不会再根据鼠标指向而动态变化。&lt;br&gt;
 * @param {Number}		[option.panScale=1]
 * 平移的步长比例。可以使用这个值扩大或限制平移操作的步长。&lt;br&gt;
 * 这个值是直接乘在基础步长上的，值越小，步长越小；值越大，步长越大&lt;br&gt;
 * 所以当取值为1时，就是之前的平移步长；&lt;br&gt;
 * 取值小于1时，会缩小步长，相当于限制了平移的能力。设置为0的时候就禁用了平移。&lt;br&gt;
 * 当取值大于1时，会扩大步长。
 * 
 * @return {View3D.Roamer}
 */
RoamerFactory.prototype.createTrackLongRoamer = function (option) {
	var mode1 = {
		leftMouseButtonOperation : &quot;rotate&quot;,
		rightMouseButtonOperation : &quot;zoom&quot;,
		middleMouseButtonOperation : &quot;pan&quot;,
		mouseWheelOperation : &quot;zoom&quot;
	};
	var mode2 = {
		leftMouseButtonOperation : &quot;rotate&quot;,
		rightMouseButtonOperation : &quot;pan&quot;,
		middleMouseButtonOperation : &quot;&quot;,
		mouseWheelOperation : &quot;zoom&quot;,
	}
	
	var newOption = mode1;// 默认mode1

	if(!Condicio.isUndefined(option)) {
		newOption = jQuery.extend({}, mode1, option);// 此处mode1与option相同的属性进行合并时，option会覆盖mode1

		if(!Condicio.isUndefined(option.opMode)) {
			if(option.opMode === &quot;mode2&quot;) {
				newOption = jQuery.extend({}, option, mode2);
			}
			else {
				newOption = jQuery.extend({}, option, mode1);// 此处mode1会覆盖option
			}
		}
	} 

	
	Condicio.checkIsString(newOption.rightMouseButtonOperation, &quot;The type of newOption.rightMouseButtonOperation must be 'string'&quot;);
	Condicio.checkIsString(newOption.leftMouseButtonOperation, &quot;The type of newOption.leftMouseButtonOperation must be 'string'&quot;);
	Condicio.checkIsString(newOption.middleMouseButtonOperation, &quot;The type of newOption.middleMouseButtonOperation must be 'string'&quot;);
	Condicio.checkIsString(newOption.mouseWheelOperation, &quot;The type of newOption.mouseWheelOperation must be 'string'&quot;);
	
	var roamerID = Utility.genGUID();
	var params = {
		roamerID: roamerID,
		leftMBOperation : newOption.leftMouseButtonOperation,
		rightMBOperation : newOption.rightMouseButtonOperation,
		middleMBOperation : newOption.middleMouseButtonOperation,
		mouseWheelOperation : newOption.mouseWheelOperation
	};
	
	if (newOption.viewpointLimitation)
	{
		ParamValidation.checkIsVec3Array(newOption.viewpointLimitation
		, &quot;The type of option.viewpointLimitation must be vec3 array and valid!&quot;);
		Condicio.checkArgument(newOption.viewpointLimitation.length === 8, &quot;the length of option.opMode must be 8&quot;);
		params.viewPointLimitation = CvtForJson.cvtVec3Array(newOption.viewpointLimitation);
	}
	if (newOption.fixedRotationCenter)
	{
		ParamValidation.checkIsVec3(newOption.fixedRotationCenter
		, &quot;The type of option.fixedRotationCenter must be vec3!&quot;);
		params.fixedRotationCenter = CvtForJson.cvtVec3(newOption.fixedRotationCenter);
	}

	if(newOption.fixedZoomCenter)
	{
		ParamValidation.checkIsVec3(newOption.fixedZoomCenter
			, &quot;The type of option.fixedZoomCenter must be vec3!&quot;
		);
		params.fixedZoomCenter = CvtForJson.cvtVec3(newOption.fixedZoomCenter);
	}

	if(newOption.fixedPanCenter)
	{
		ParamValidation.checkIsVec3(newOption.fixedPanCenter, &quot;The type of option.fixedPanCenter must be vec3!&quot;);
		params.fixedPanCenter = CvtForJson.cvtVec3(newOption.fixedPanCenter);
	}

	if(newOption.panScale !== undefined)
	{
		Condicio.checkIsNumber(newOption.panScale, &quot;The type of option.panScale must be number!&quot;);
		Condicio.checkArgument(newOption.panScale &gt;= 0, &quot;The value of option.panScale must be greater than 0!&quot;);

		params.panScale = newOption.panScale;
	}
	else
	{
		params.panScale = 1.0;
	}
	
    this.session.request(this.objectID, &quot;createTrackLongRoamer&quot;, params);

    return new Roamer(roamerID);
};

<span id='View3D-RoamerFactory-method-createFirstAndThirdPersonRoamer'>/**
</span> * 第一人称和第三人称漫游器: createFirstAndThirdPersonRoamer&lt;br&gt;
 * option.keySettings中forward、backward、left、right控制前后左右移动，option.moveVelocity控制其速度&lt;br&gt;
 * 鼠标左键或右键按下拖动用来改变视口&lt;br&gt;
 * 按下option.keySettings.collisionSwitch触发按键用来切换水平碰撞检测状态，默认为开启水平碰撞检测&lt;br&gt;
 * 按下option.keySettings.home用来移动到option.homePosition坐标点和option.homeDirection朝向&lt;br&gt;
 * 按下option.keySettings.jump用来进行跳跃&lt;br&gt;
 * 按下option.keySettings.firstThirdSwitch用来在漫游器内进行一三人称切换&lt;br&gt;
 * 第三人称状态下，鼠标滚轮用来调整摄像机距离人物眼睛的距离&lt;br&gt;
 * 在此漫游器下可以通过注册View3D.registerFirstThirdPersonCallback回调函数获得实时状态，并可以调用View3D.setFirstThirdPersonState进行参数调整&lt;br&gt;
 * 注意：键盘按键均应处于小写英文状态&lt;br&gt;
 *
 * @param {Object}		option 						创建漫游交互的选项
 *
 * @param {String}		[option.mode = &quot;firstPerson&quot;]     		漫游器初始人称：&quot;firstPerson&quot; 第一人称、&quot;thirdPerson&quot; 第三人称
 * @param {String} 		option.modelUrl			        	    第三人称动画模型文件地址（支持“http://”地址和“file:///”格式），模型文件支持格式：.fbx &lt;br&gt;
 * 																fbx动画模型中需要实现&quot;TakeRun&quot;-奔跑动画、&quot;TakeJump&quot;-跳跃动画、&quot;TakeStand&quot;-站立动画三个Takes动画 &lt;br&gt;
 *                                                              fbx建模要求：人物上方为Y轴，前方为Z轴，人物高度推荐150cm-170cm，使用量纲cm，模型无缩放。&lt;br&gt;
 * 																具体可以参考resource/pao0-24_ThirdPerson.FBX。
 * @param {Function} 	            option.callback             加载动画模型结束回调
 * @param {NetworkError/DataError} 	option.callback.err         加载过程出现错误时返回错误
 *                                  err {NetworkError}          模型文件下载失败
 *                					err {DataError}             模型数据出错
 * @param {Number}		[option.moveVelocity = 3000]			水平移动速度，单位：毫米/秒，需要大于零，推荐小于20000毫米/秒，超出可能出现穿模现象
 * @param {vec3}		[option.homePosition = vec3(0,0,0)]		按option.keySettings.home键返回到的人物脚下位置（人物眼睛距脚下1.5m），世界坐标系，单位：毫米
 * @param {vec3}		[option.homeDirection = vec3(1,0,0)]	按option.keySettings.home键返回到的视口朝向，世界坐标系
 * @param {Object}		[option.keySettings]					按键设置，可设置值&quot;a&quot;-&quot;z&quot;（26个小写字母）、&quot;0&quot;-&quot;9&quot;（主数字按键）、&quot;space&quot;（空格）、&lt;br&gt;
 * 																&quot;up&quot;、&quot;down&quot;、&quot;left&quot;、&quot;right&quot;（键盘方向键上下左右）&lt;br&gt;
 * 																单个按键值设置为&quot;null&quot;可以屏蔽掉此按键功能
 * @param {String}		[option.keySettings.forward = &quot;w&quot;]      前进键设置
 * @param {String}		[option.keySettings.backward = &quot;s&quot;]		后退键设置
 * @param {String}		[option.keySettings.left = &quot;a&quot;]			左移键设置
 * @param {String}		[option.keySettings.right = &quot;d&quot;]		右移键设置
 * @param {String}		[option.keySettings.jump = &quot;f&quot;]			跳跃键设置
 * @param {String}		[option.keySettings.home = &quot;h&quot;]			返回键设置
 * @param {String}		[option.keySettings.collisionSwitch = &quot;g&quot;]	水平碰撞检测切换键设置
 * @param {String}		[option.keySettings.firstThirdSwitch = &quot;r&quot;] 一三人称切换键设置
 * 
 * @return {View3D.Roamer}
 */
 RoamerFactory.prototype.createFirstAndThirdPersonRoamer = function (option) {

	var newKeySettings = {
		forward:&quot;w&quot;,
		backward:&quot;s&quot;,
		left:&quot;a&quot;,
		right:&quot;d&quot;,
		jump:&quot;f&quot;,
		home:&quot;h&quot;,
		collisionSwitch:&quot;g&quot;,
		firstThirdSwitch:&quot;r&quot;,
	};
	var newOption = {
		mode:&quot;firstPerson&quot;,
		modelUrl:&quot;&quot;,
		moveVelocity:3000,
		homePosition:Matrix.vec3.fromValues(0,0,0),
		homeDirection:Matrix.vec3.fromValues(1,0,0),
		keySettings:newKeySettings,
	};

	if(!Condicio.isUndefined(option)){
		if(!Condicio.isUndefined(option.mode)){
			newOption.mode = option.mode;
		}
		if(!Condicio.isUndefined(option.modelUrl)){
			newOption.modelUrl = option.modelUrl;
		}
		if(!Condicio.isUndefined(option.moveVelocity)){
			newOption.moveVelocity = option.moveVelocity;
		}
		if(!Condicio.isUndefined(option.homePosition)){
			newOption.homePosition = option.homePosition;
		}
		if(!Condicio.isUndefined(option.homeDirection)){
			newOption.homeDirection = option.homeDirection;
		}
		if(!Condicio.isUndefined(option.keySettings)){
			if(!Condicio.isUndefined(option.keySettings.forward)){
				newOption.keySettings.forward = option.keySettings.forward;
			}
			if(!Condicio.isUndefined(option.keySettings.backward)){
				newOption.keySettings.backward = option.keySettings.backward;
			}
			if(!Condicio.isUndefined(option.keySettings.left)){
				newOption.keySettings.left = option.keySettings.left;
			}
			if(!Condicio.isUndefined(option.keySettings.right)){
				newOption.keySettings.right = option.keySettings.right;
			}
			if(!Condicio.isUndefined(option.keySettings.jump)){
				newOption.keySettings.jump = option.keySettings.jump;
			}
			if(!Condicio.isUndefined(option.keySettings.home)){
				newOption.keySettings.home = option.keySettings.home;
			}
			if(!Condicio.isUndefined(option.keySettings.collisionSwitch)){
				newOption.keySettings.collisionSwitch = option.keySettings.collisionSwitch;
			}
			if(!Condicio.isUndefined(option.keySettings.firstThirdSwitch)){
				newOption.keySettings.firstThirdSwitch = option.keySettings.firstThirdSwitch;
			}
		}
	}

	Condicio.checkIsString(newOption.mode, &quot;option.mode must be a string&quot;);
	Condicio.checkIsString(newOption.modelUrl, &quot;option.modelUrl must be a string&quot;);
	Condicio.checkIsFunction(option.callback, &quot;The type of callback must be 'Function'!&quot;);
	Condicio.checkIsNumber(newOption.moveVelocity, &quot;The type of option.moveVelocity must be number!&quot;);
	Condicio.checkArgument(newOption.moveVelocity &gt; 0, &quot;The value of option.moveVelocity must be greater than 0!&quot;);
	ParamValidation.checkIsVec3(newOption.homePosition, &quot;The type of option.homePosition must be vec3!&quot;);
	ParamValidation.checkIsVec3(newOption.homeDirection, &quot;The type of option.homeDirection must be vec3!&quot;);

	Condicio.checkIsString(newOption.keySettings.forward, &quot;option.keySettings.forward must be a string&quot;);
	Condicio.checkIsString(newOption.keySettings.backward, &quot;option.keySettings.backward must be a string&quot;);
	Condicio.checkIsString(newOption.keySettings.left, &quot;option.keySettings.left must be a string&quot;);
	Condicio.checkIsString(newOption.keySettings.right, &quot;option.keySettings.right must be a string&quot;);
	Condicio.checkIsString(newOption.keySettings.jump, &quot;option.keySettings.jump must be a string&quot;);
	Condicio.checkIsString(newOption.keySettings.home, &quot;option.keySettings.home must be a string&quot;);
	Condicio.checkIsString(newOption.keySettings.collisionSwitch, &quot;option.keySettings.collisionSwitch must be a string&quot;);
	Condicio.checkIsString(newOption.keySettings.firstThirdSwitch, &quot;option.keySettings.firstThirdSwitch must be a string&quot;);

	var _this = this;
    var callbackWrapper = function (error, result) {
		if(!Condicio.isUndefined(option.callback))
		{
			if (error) {
				option.callback(error);
			}
			else
				option.callback(null);
		}
    };
	
	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callbackWrapper, &quot;createFirstAndThirdPersonRoamer&quot;);

	var roamerID = Utility.genGUID();
	var params = {
		roamerID: roamerID,
		mode: newOption.mode,
		modelUrl: newOption.modelUrl,
		callbackID: callbackID,
		moveVelocity: newOption.moveVelocity,
		homePosition: CvtForJson.cvtVec3(newOption.homePosition),
		homeDirection: CvtForJson.cvtVec3(newOption.homeDirection),
		forward: newOption.keySettings.forward,
		backward: newOption.keySettings.backward,
		left: newOption.keySettings.left,
		right: newOption.keySettings.right,
		jump: newOption.keySettings.jump,
		home: newOption.keySettings.home,
		collisionSwitch: newOption.keySettings.collisionSwitch,
		firstThirdSwitch: newOption.keySettings.firstThirdSwitch,
	};
	
    this.session.request(this.objectID, &quot;createFirstAndThirdPersonRoamer&quot;, params);

    return new Roamer(roamerID);
};
<span id='View3D-RoamerFactory-method-releaseRoamers'>/**
</span> * 删除漫游交互
* @param {Roamer[]} roamers 	 存储待删除漫游交互的数组
*/
RoamerFactory.prototype.releaseRoamers = function (roamers) {
	ParamValidation.checkIsTypeArray(roamers, Roamer, &quot;The type of Resources waiting release must be 'Roamers Array'&quot;);

	var roamerIDs = [];
    for (var i = 0; i &lt; roamers.length; ++i) {
		roamerIDs.push(roamers[i].objectID);
    }

    this.session.request(this.objectID, &quot;releaseRoamer&quot;, { roamerIDs: roamerIDs });
};

module.exports = RoamerFactory;
</pre>
</body>
</html>
