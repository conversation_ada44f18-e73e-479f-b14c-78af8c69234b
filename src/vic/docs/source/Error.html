<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">&quot;use strict&quot;;

var Condicio = require(&quot;condicio&quot;);

<span id='Error-DataError'>/**
</span> * @class Error.DataError
 * 数据错误,请求资源文件的数据错误
 */
function DataError(message) {
<span id='Error-DataError-property-name'>	/**
</span>	 * @property {String}  [name=&quot;DataError&quot;] 	错误名
	 */	
    this.name = &quot;DataError&quot;;
<span id='Error-DataError-property-message'>	/**
</span>	 * @property {String}  message 				错误信息
	 */    
	this.message = message;
};

<span id='Error-NetworkError'>/**
</span> * @class Error.NetworkError
 * 网络错误，请求资源时发生网络错误
 */
function NetworkError(message) {
<span id='Error-NetworkError-property-name'>	/**
</span>	 * @property {String}  [name=&quot;NetworkError&quot;] 	错误名
	 */
    this.name = &quot;NetworkError&quot;;
<span id='Error-NetworkError-property-message'>	/**
</span>	 * @property {String}  message					错误信息
	 */
    this.message = message;
};

<span id='Error-SystemError'>/**
</span> * @class Error.SystemError
 * 系统错误（因权限无法进行文件I/O，对文件资源的使用被占用、无法删除）
 */
function SystemError(message){
<span id='Error-SystemError-property-name'>	/**
</span>	 * @property {String}  [name=&quot;SystemError&quot;] 	错误名
	 */
	this.name = &quot;SystemError&quot;;
<span id='Error-SystemError-property-message'>	/**
</span>	 * @property {String}  message					错误信息
	 */    
	this.message = message;
};

<span id='Error-InvalidArgumentError'>/**
</span> * @class Error.InvalidArgumentError
 * 参数错误，VIC与BRS进行资源或对象请求时传入参数错误
 */
function InvalidArgumentError(message){
<span id='Error-InvalidArgumentError-property-name'>	/**
</span>	 * @property {String}  [name=&quot;InvalidArgumentError&quot;] 	错误名
	 */
	 this.name = &quot;InvalidArgumentError&quot;;
<span id='Error-InvalidArgumentError-property-message'>	/**
</span>	 * @property {String}  message					错误信息
	 */
	 this.message = message;
};

<span id='Error-VersioningError'>/**
</span> * @class Error.VersioningError
 * 版本管理异常，VIC与BRS进行版本协商不通过
 */
function VersioningError(message){
<span id='Error-VersioningError-property-name'>	/**
</span>	 * @property {String}  [name=&quot;VersioningError&quot;] 	错误名
	 */
	this.name = &quot;VersioningError&quot;;
<span id='Error-VersioningError-property-message'>	/**
</span>	 * @property {String}  message					错误信息
	 */
	this.message = message;
};
 
<span id='ErrorCode'>/** 
</span> * @ignore
 * @enum {Number} ErrorCode   错误代码
 */
var ErrorCode = {
    E_Data: -10001,
    E_Network: -10002,
	E_InvalidArgument: -10003,
	E_System: -10004,
	E_Versioning: -10005
};

<span id='global-method-genError'>/**
</span> * @ignore
 * 
 * 包装所有的错误
 */
var genError = function(error){
	if(error.code === ErrorCode.E_Data){
		return new DataError(error.message);
	}
	else if(error.code === ErrorCode.E_Network){
		return new NetworkError(error.message);
	}
	else if (error.code === ErrorCode.E_InvalidArgument){
		return new InvalidArgumentError(error.message);
	}
	else if (error.code === ErrorCode.E_System){
		return new SystemError(error.message);
	}
	else if (error.code === ErrorCode.E_Versioning){
		return new VersioningError(error.message);
	}
	else {
		throw Error(&quot;Unknown Error&quot;);
	}
 };
 
exports.genError = genError;
exports.DataError = DataError;
exports.NetworkError = NetworkError;
exports.SystemError = SystemError;
exports.VersioningError = VersioningError;
exports.InvalidArgumentError = InvalidArgumentError;

</pre>
</body>
</html>
