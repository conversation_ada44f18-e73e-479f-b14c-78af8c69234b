<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">&quot;use strict&quot;;

var _ = require(&quot;lodash&quot;);

<span id='View3D-Roamer'>/**
</span> * @class View3D.Roamer
 * 漫游器
 */
function Roamer(objectID) {
    this.objectID = objectID;
};

Roamer.prototype = _.create(Roamer.prototype, {
	constructor: Roamer
});

module.exports = Roamer;</pre>
</body>
</html>
