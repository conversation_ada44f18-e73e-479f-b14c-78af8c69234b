<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">&quot;use strict&quot;;

var Condicio = require(&quot;condicio&quot;),
	Matrix = require(&quot;gl-matrix-double&quot;),
	ParamValidation = require(&quot;../ParamValidation.js&quot;);



<span id='View3D-Viewpoint-method-constructor'><span id='View3D-Viewpoint'>/**
</span></span> * @class View3D.Viewpoint
 * &lt;a href=&quot;https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/75956834&quot;&gt;视角&lt;/a&gt;
 *
 * @constructor						创建一个新视点
 * @param {vec3} eye 				眼睛的位置
 * @param {vec3} center 			眼睛所看到物体的中心位置
 * @param {vec3} [up = vec3(0,0,1)] 视角正方向
 */
var Viewpoint = function(eye, center, up) {
	ParamValidation.checkIsVec3(eye, &quot;The type of eye must be 'vec3' and valid&quot;);
	ParamValidation.checkIsVec3(center, &quot;The type of center must be 'vec3' and valid&quot;);
	
<span id='View3D-Viewpoint-property-eye'>	/**
</span>	 * @property {vec3} eye 眼睛的位置
	 */
	this.eye = eye;
<span id='View3D-Viewpoint-property-center'>	/**
</span>	 * @property {vec3} center 眼睛所看到物体的中心位置
	 */
	this.center = center;
	
<span id='View3D-Viewpoint-property-up'>	/**
</span>	 * @property {vec3} up 眼睛的正方向
	 */
	if(Condicio.isUndefined(up))
		up = Matrix.vec3(0,0,1);
	else
	    ParamValidation.checkIsVec3(up, &quot;The type of up must be 'vec3' and valid&quot;);
	this.up = up;
};

module.exports = Viewpoint;</pre>
</body>
</html>
