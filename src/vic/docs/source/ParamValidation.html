<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">&quot;use strict&quot;;
var Condicio = require(&quot;condicio&quot;),
	COLOR = require(&quot;color&quot;),
	Matrix = require(&quot;gl-matrix-double&quot;);

<span id='ParamValidation'>/**
</span> * @ignore
 * 参数判断
 */
var ParamValidation = {	
	checkIsLineStyle: function(lineStyle) {
		Condicio.checkIsObject(lineStyle, &quot;The type of lineStyle must be 'Object'!&quot;);
		Condicio.checkIsNumber(lineStyle.width, &quot;The type of lineStyle.width must be 'Number'!&quot;);
		//Condicio.checkIsType(lineStyle.color, COLOR, &quot;The type of lineStyle.color must be 'COLOR'!&quot;);
		Condicio.checkIsString(lineStyle.type, &quot;The type of lineStyle.type must be 'String'!&quot;);
		Condicio.checkArgument(lineStyle.type === 'solid' || lineStyle.type === 'stipple', &quot;The type of lineStyle.type must be valid&quot;);
	},
	
	checkIsPointStyle: function(pointStyle) {
		Condicio.checkIsObject(pointStyle, &quot;The type of pointStyle must be 'Object'!&quot;);
		Condicio.checkIsNumber(pointStyle.size, &quot;The type of pointStyle.size must be 'Number'!&quot;);
		Condicio.checkArgument(pointStyle.size &gt; 0, &quot;The pointStyle.size must be &gt; 0 !&quot;);
		//Condicio.checkIsType(pointStyle.color, COLOR, &quot;The type of pointStyle.color must be 'COLOR'!&quot;);
	},
	
	checkIsBorderStyle: function(borderStyle) {
		Condicio.checkIsObject(borderStyle, &quot;The type of borderStyle must be 'Object'!&quot;);
		Condicio.checkIsNumber(borderStyle.width, &quot;The type of borderStyle.width must be 'Number'!&quot;);
		//Condicio.checkIsType(borderStyle.color, COLOR, &quot;The type of borderStyle.color must be 'COLOR'!&quot;);
	},
	
	checkIsTextStyle: function(textStyle) {
		Condicio.checkIsObject(textStyle, &quot;The type of textStyle must be 'Object'!&quot;);
		Condicio.checkIsNumber(textStyle.size, &quot;The type of textStyle.size must be 'Number'!&quot;);
		Condicio.checkIsString(textStyle.alignment, &quot;The type of textStyle.alignment must be 'String'!&quot;);
		//Condicio.checkIsType(textStyle.color, COLOR, &quot;The type of textStyle.color must be 'COLOR'!&quot;);
		Condicio.checkArgument(textStyle.alignment !== 'left' || textStyle.alignment !== 'center' 
								|| textStyle.alignment !== 'right', &quot;The textStyle.alignment muset be valid&quot;);
		Condicio.checkIsNumber(textStyle.lineSpacing, &quot;The type of textStyle.lineSpacing must be 'Number'!&quot;);
	},
	
	checkIsTextFrameStyle: function(textFrameStyle) {
		Condicio.checkIsObject(textFrameStyle, &quot;The type of textFrameStyle must be 'Object'!&quot;);
		this.checkIsBorderStyle(textFrameStyle.border);
		this.checkIsTextStyle(textFrameStyle.text);
		Condicio.checkIsNumber(textFrameStyle.padding, &quot;param textFrameStyle.padding must be 'Number'&quot;);
		Condicio.checkIsString(textFrameStyle.shape, &quot;param textFrameStyle.shape must be 'String'&quot;);
		Condicio.checkArgument(textFrameStyle.shape !== &quot;rectangle&quot; || textFrameStyle.shap !== &quot;circle&quot;, &quot;param textFrameStyle.shape must be valid!&quot;);
		//Condicio.checkIsType(textFrameStyle.fillColor, COLOR, &quot;The type of textFrameStyle.fillColor must be 'COLOR'!&quot;);
	},
	
	checkIsTextLabelStyle: function(textLabelStyle) {
		Condicio.checkIsObject(textLabelStyle, &quot;The type of textLabelStyle must be 'Object'!&quot;);
		this.checkIsTextFrameStyle(textLabelStyle.textFrame);
		this.checkIsLineStyle(textLabelStyle.pointer.line);
		Condicio.checkIsObject(textLabelStyle.connectPosition, &quot;The type of textLabelStyle.connectPositio must be 'Object'!&quot;);
		Condicio.checkIsString(textLabelStyle.connectPosition.x, &quot;The type of textLabelStyle.connectPosition.x must be 'String'!&quot;);
		Condicio.checkIsString(textLabelStyle.connectPosition.y, &quot;The type of textLabelStyle.connectPosition.y must be 'String'!&quot;);
		Condicio.checkArgument(textLabelStyle.connectPosition.x === 'left' || textLabelStyle.connectPosition.x === 'center' 
								|| textLabelStyle.connectPosition.x === 'right', &quot;The textLabelStyle.connectPosition.x muset be valid&quot;);
		Condicio.checkArgument(textLabelStyle.connectPosition.y !== 'bottom' || textLabelStyle.connectPosition.y !== 'middle' 
								|| textLabelStyle.connectPosition.y !== 'top', &quot;The textLabelStyle.connectPosition.y muset be valid&quot;);					
	},
	
	checkIsLightStyle: function(lightStyle) {
		Condicio.checkIsObject(lightStyle, &quot;The type of textLabelStyle must be 'Object'!&quot;);
		//Condicio.checkIsType(lightStyle.ambient, COLOR, &quot;The type of lightStyle.ambient must be 'COLOR'!&quot;);
		//Condicio.checkIsType(lightStyle.diffuse, COLOR, &quot;The type of lightStyle.diffuse must be 'COLOR'!&quot;);
		//Condicio.checkIsType(lightStyle.specular, COLOR, &quot;The type of lightStyle.specular must be 'COLOR'!&quot;);
		this.checkIsVec4(lightStyle.position, &quot;The type of lightStyle.position must be Vec4!&quot;);
		this.checkIsVec3(lightStyle.direction, &quot;The type of lightStyle.direction must be Vec3!&quot;);
		Condicio.checkIsNumber(lightStyle.spotCutoff, &quot;The type of lightStyle.spotCutoff must be 'Number'!&quot;);
		Condicio.checkIsNumber(lightStyle.range, &quot;The type of lightStyle.range must be 'Number'!&quot;);
	},
	
	checkIsArcsStyle: function(arcsStyle) {
		Condicio.checkIsObject(arcsStyle, &quot;The type of arcsStyle must be 'Object'!&quot;);
		//Condicio.checkIsType(arcsStyle.color, COLOR, &quot;The type of arcsStyle.color must be 'COLOR'!&quot;);
		Condicio.checkIsNumber(arcsStyle.angle, &quot;The type of arcsStyle.angle must be 'Number'!&quot;);
		Condicio.checkIsNumber(arcsStyle.width, &quot;The type of arcsStyle.width must be 'Number'!&quot;);
		Condicio.checkArgument(arcsStyle.width &gt; 0, &quot;The ArcsStyle.width must &gt; 0! &quot;);
	},
	
	checkIsPidTextStyle: function(textStyle) {
		Condicio.checkIsObject(textStyle, &quot;The type of textStyle must be 'Object'!&quot;);
		Condicio.checkIsNumber(textStyle.size, &quot;The type of textStyle.size must be 'Number'!&quot;);
		Condicio.checkIsString(textStyle.fontName, &quot;The type of textStyle.fontName must be 'String'!&quot;);
		//Condicio.checkIsType(textStyle.color, COLOR, &quot;The type of textStyle.color must be 'COLOR'!&quot;);
	},
	
	checkIsPidTextFrameStyle: function(textFrameStyle) {
		Condicio.checkIsObject(textFrameStyle, &quot;The type of textFrameStyle must be 'Object'!&quot;);
		this.checkIsPidTextStyle(textFrameStyle.text);
		Condicio.checkIsNumber(textFrameStyle.padding, &quot;param textFrameStyle.padding must be 'Number'&quot;);
		//Condicio.checkIsType(textFrameStyle.backgroundFillColor, COLOR, &quot;The type of textFrameStyle.backgroundFillColor must be 'COLOR'!&quot;);
		Condicio.checkIsBoolean(textFrameStyle.backgroundFill, &quot;The type of textFrameStyle.backgroundFill must be 'boolean'!&quot;);
		Condicio.checkIsBoolean(textFrameStyle.showBorders, &quot;The type of textFrameStyle.showBorders must be 'boolean'!&quot;);
	},
	
	checkIsAxisStyle: function(axisStyle) {
		Condicio.checkIsObject(axisStyle, &quot;The type of textFrameStyle must be 'Object'!&quot;);
		Condicio.checkIsNumber(axisStyle.length, &quot;param axisStyle.length must be 'Number'&quot;);
		Condicio.checkArgument(axisStyle.length &gt; 0, &quot;The axisStyle.length must &gt; 0! &quot;);
		Condicio.checkIsNumber(axisStyle.width, &quot;param axisStyle.width must be 'Number'&quot;);
		Condicio.checkArgument(axisStyle.width &gt; 0, &quot;The axisStyle.width must &gt; 0! &quot;);
		Condicio.checkIsNumber(axisStyle.pickedRadius, &quot;param axisStyle.pickedRadius must be 'Number'&quot;);
		Condicio.checkArgument(axisStyle.pickedRadius &gt; 0, &quot;The axisStyle.pickedRadius must &gt; 0! &quot;);
		//Condicio.checkIsType(axisStyle.color, COLOR, &quot;The type of axisStyle.color must be 'COLOR'!&quot;);
		//Condicio.checkIsType(axisStyle.pickedColor, COLOR, &quot;The type of axisStyle.pickedColor must be 'COLOR'!&quot;);
	},
	
	checkIsPolygonStyle: function(polygonStyle) {
		Condicio.checkIsObject(polygonStyle, &quot;The type of polygonStyle must be 'Object'!&quot;);
		//Condicio.checkIsType(lineStyle.color, COLOR, &quot;The type of lineStyle.color must be 'COLOR'!&quot;);
	},

	checkIsView3DConfig: function(view3DConfig) {

		Condicio.checkIsNumber(view3DConfig.maxFPS, &quot;The type of maxFPS must be 'Number'!&quot;);
		Condicio.checkArgument(view3DConfig.maxFPS &gt; 0, &quot;The maxFPS must greater than 0&quot;);

		Condicio.checkIsNumber(view3DConfig.LODScale, &quot;The type of LODScale must be 'Number'!&quot;);
		Condicio.checkArgument(view3DConfig.LODScale &gt;= 0, &quot;The LODScale must greater than 0 or&quot;);

		Condicio.checkIsNumber(view3DConfig.maxPagedLODNumber, &quot;The type of maxPagedLODNumber must be 'Number'!&quot;);
		Condicio.checkArgument(view3DConfig.maxPagedLODNumber &gt;= 0, &quot;The maxPagedLODNumber must greater than 0&quot;);

		Condicio.checkIsBoolean(view3DConfig.enableTransparency, &quot;The type of enableTransparency must be 'Boolean'!&quot;);
		Condicio.checkArgument(view3DConfig.enableTransparency == false || view3DConfig.enableTransparency == true, &quot;The enableTransparency must false or true&quot;);

		Condicio.checkIsBoolean(view3DConfig.loadDurMoving, &quot;The type of loadDurMoving must be 'Boolean'!&quot;);
		Condicio.checkArgument(view3DConfig.loadDurMoving == false || view3DConfig.loadDurMoving == true, &quot;The loadDurMoving must false or true&quot;);
		
		Condicio.checkIsNumber(view3DConfig.maxFrameTime, &quot;The type of maxFrameTime must be 'Number'!&quot;);
		Condicio.checkArgument(view3DConfig.maxFrameTime &gt; 0, &quot;The maxFrameTime must greater than 0&quot;);

		Condicio.checkIsNumber(view3DConfig.computeNearFarMode, &quot;The type of computeNearFarMode must be 'Number'!&quot;);
		Condicio.checkArgument(view3DConfig.computeNearFarMode == 0 || view3DConfig.computeNearFarMode == 1, &quot;The computeNearFarMode must 0 or 1&quot;);
	},


	checkIsParticalSystemStyle: function(particalSystemStyle) {

		this.checkIsVec3(particalSystemStyle.direction,&quot;The type of style-direction must be 'vec3'&quot; );
		this.checkIsVec3(particalSystemStyle.acceleration,&quot;The type of style-acceleration must be 'vec3'&quot; );
	
		Condicio.checkIsNumber(particalSystemStyle.angle, &quot;The type of style-angle must be 'Number'!&quot;);

		Condicio.checkIsNumber(particalSystemStyle.size, &quot;The type of style-size must be 'Number'!&quot;);
		Condicio.checkArgument(particalSystemStyle.size &gt; 0, &quot;The size must greater than 0&quot;);

		Condicio.checkIsNumber(particalSystemStyle.flowRate, &quot;The type of style-flowRate must be 'Number'!&quot;);
		Condicio.checkArgument(particalSystemStyle.flowRate &gt; 0, &quot;The flowRate must greater than 0&quot;);

		Condicio.checkIsNumber(particalSystemStyle.lifeTime, &quot;The type of style-lifeTime must be 'Number'!&quot;);
		Condicio.checkArgument(particalSystemStyle.lifeTime &gt; 0, &quot;The lifeTime must greater than 0&quot;);

		Condicio.checkIsString(particalSystemStyle.imageName, &quot;The type of style-imageName must be 'String'!&quot;);

		if(particalSystemStyle.alphaInterpolations)
		{
			this.checkIsTypeArray(particalSystemStyle.alphaInterpolations, Object, &quot;The type of style-alphaInterpolations must be 'Object Array'!&quot;);
		}
	},
	
	checkIsTypeArray: function(arr, type, message) { 
		Condicio.checkIsArray(arr, message);
		
		// 仅支持自定义的类和Number，Object和字符串类型
		for (var i=0; i&lt;arr.length; ++i){
			if (type === String)
				Condicio.checkIsString(arr[i], message);
			else if (type === Number)
				Condicio.checkIsNumber(arr[i], message);
			else if (type === Object)
				Condicio.checkIsObject(arr[i], message);
			else
				Condicio.checkIsType(arr[i], type, message);
		}
	},

	checkIsVec2Array: function(vec2Array, message)
	{
		Condicio.checkIsArray(vec2Array, message);
	    for (var i=0; i&lt;vec2Array.length; ++i){
	        Condicio.checkIsType(vec2Array[i], Matrix.glMatrix.ARRAY_TYPE, message);
	        Condicio.checkArgument(vec2Array[i].length === 2, message);
	    }
	},
	checkIsVec3Array: function(vec3Array, message)
	{
		Condicio.checkIsArray(vec3Array, message);
	    for (var i=0; i&lt;vec3Array.length; ++i){
	        Condicio.checkIsType(vec3Array[i], Matrix.glMatrix.ARRAY_TYPE, message);
	        Condicio.checkArgument(vec3Array[i].length === 3, message);
	    }
	},
	checkIsVec4: function (vec4, message){
		Condicio.checkIsType(vec4, Matrix.glMatrix.ARRAY_TYPE, message);
		Condicio.checkArgument(vec4.length === 4, message);
		for(var i = 0; i &lt; vec4.length; ++i){
			Condicio.checkArgument(!isNaN(vec4[i]), message);
		}
	},
	checkIsVec3: function (vec3, message){
		Condicio.checkIsType(vec3, Matrix.glMatrix.ARRAY_TYPE, message);
		Condicio.checkArgument(vec3.length === 3, message);
		for(var i = 0; i &lt; vec3.length; ++i){
			Condicio.checkArgument(!isNaN(vec3[i]), message);
		}
	},
	
	checkIsVec2: function (vec2, message){
		Condicio.checkIsType(vec2, Matrix.glMatrix.ARRAY_TYPE, message);
		Condicio.checkArgument(vec2.length === 2, message);
		for(var i = 0; i &lt; vec2.length; ++i){
			Condicio.checkArgument(!isNaN(vec2[i]), message);
		}
	},
	
	checkIsMat4: function(mat, message) {
		Condicio.checkIsType(mat, Matrix.glMatrix.ARRAY_TYPE, message);
		Condicio.checkArgument(mat.length === 16, message);
	}
};

module.exports = ParamValidation;</pre>
</body>
</html>
