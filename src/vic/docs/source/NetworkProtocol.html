<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">&quot;use strict&quot;;

var Condicio = require(&quot;condicio&quot;);

<span id='PackageType'>/**
</span> * @ignore
 * 网络通信协议
 *
 */

var PackageType = {
	Request: 0,
    Response: 1,
    SessionReady: 2,
	Error: 3,
	Ping: 4,
	Pong: 5
};

var VersionNumber = {
	CurrentVIC: &quot;6.0.0&quot;,
	SupportBRSMinimum: &quot;5.12.0&quot;
};

function Package(type, body) {
    if (Condicio.isUndefined(type))
        this.type = null;
    else
        this.type = type;

    if (Condicio.isUndefined(body))
        this.body = null;
    else
        this.body = body;
};

Package.prototype.fromString = function (data) {
    var obj = JSON.parse(data);

    this.type = obj.type;
    this.body = obj.body;
};

Package.prototype.toString = function () {
    var obj = new Object;
    obj.type = this.type;

    if (this.body)
        obj.body = this.body;

    return JSON.stringify(obj);
};


exports.PackageType = PackageType;
exports.VersionNumber = VersionNumber;
exports.Package = Package;




</pre>
</body>
</html>
