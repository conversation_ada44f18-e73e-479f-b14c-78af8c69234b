<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">
&quot;use strict&quot;;

var _ = require(&quot;lodash&quot;),
	Condicio = require(&quot;condicio&quot;),
	Matrix = require(&quot;gl-matrix-double&quot;),
	CvtForJson = require(&quot;../CvtForJson.js&quot;),
	Node = require(&quot;./Node.js&quot;),
	Interaction = require(&quot;./Interaction.js&quot;),
	Viewpoint = require(&quot;./Viewpoint.js&quot;),
	ComputeNearFarMode = require(&quot;./ComputeNearFarMode&quot;),
	Utility = require(&quot;../Utility.js&quot;),
	ImageDisplay = require(&quot;../ImageDisplay.js&quot;),
	StreamDisplay = require(&quot;../StreamDisplay.js&quot;),
	ParamValidation = require(&quot;../ParamValidation.js&quot;),
	Roamer = require(&quot;./Roamer.js&quot;),
	jQuery = require(&quot;jquery&quot;),
	Filter = require(&quot;./Filter.js&quot;),
	Error = require(&quot;../Error.js&quot;),
	COLOR = require(&quot;color&quot;),
	Effect = require(&quot;./Effect.js&quot;),
	LightEffect = require(&quot;./LightEffect.js&quot;),
	NodeFactory = require(&quot;./NodeFactory.js&quot;),
	EffectFactory = require(&quot;./EffectFactory.js&quot;),
	InteractionFactory = require(&quot;./InteractionFactory.js&quot;),
	RoamerFactory = require(&quot;./RoamerFactory.js&quot;),
	cvtToNodeArray = require(&quot;./CvtToNodeArray&quot;),
	Command = require(&quot;./Command.js&quot;);

var InvalidArgumentError = Error.InvalidArgumentError;

<span id='View3D-View3D'>/**
</span> * @class View3D.View3D
 * 三维渲染场景
 *
 * 创建节点工厂、创建效果工厂、创建交互工厂
 *
 * 提供HTML与三维显示窗口的通信接口
 *
 * 不应该直接创建，应该通过{@link Factory}提供的createView3D得到
 */
function View3D(session, elem, result, mode, enableSSL, proxyUri, callback) {
	var _this = this;

	this.session = session;
	this.objectID = result.viewID;
	this.rootNode = null;

<span id='View3D-View3D-property-NodeFactory'>	/**
</span>	* @property {NodeFactory}   		NodeFactory 		节点创建工厂
	*/
	this.NodeFactory = new NodeFactory(this.session, &quot;NodeFactory&quot;);
<span id='View3D-View3D-property-EffectFactory'>	/**
</span>	* @property {EffectFactory} 		EffectFactory		效果创建工厂
	*/
	this.EffectFactory = new EffectFactory(this.session, &quot;EffectFactory&quot;);
<span id='View3D-View3D-property-InteractionFactory'>	/**
</span>	* @property {InteractionFactory}	InteractionFactory	交互创建工厂
	*/
	this.InteractionFactory = new InteractionFactory(this.session, &quot;InteractionFactory&quot;);
<span id='View3D-View3D-property-RoamerFactory'>	/**
</span>	* @property {RoamerFactory}	RoamerFactory	漫游创建工厂
	*/
	this.RoamerFactory = new RoamerFactory(this.session, &quot;RoamerFactory&quot;);

	this.interaction = new Interaction(&quot;default interaction&quot;);
	this.roamer = null;

	this.lightEffects = {};
	if (!Condicio.isUndefined(result.lightEffectIDs.skyLightEffect)) {
		this.lightEffects.skyLightEffect = new LightEffect(this.session, result.lightEffectIDs.skyLightEffect);
		this.addEffect(this.lightEffects.skyLightEffect);
	}
	if (!Condicio.isUndefined(result.lightEffectIDs.cameraLightEffect)) {
		this.lightEffects.cameraLightEffect = new LightEffect(this.session, result.lightEffectIDs.cameraLightEffect);
		this.addEffect(this.lightEffects.cameraLightEffect);
	}

	switch (mode) {
		case 0:
			this.display = new ImageDisplay(session, this.objectID, elem, enableSSL, proxyUri);
			callback(null, this);
			break;
		case 1:
			var callbackWrapper = function (error) {
				if (!error)
					callback(null, _this);
				else {
					_this.release();
					callback(error, null);
				}
			};
			this.display = new StreamDisplay(session, this.objectID, elem, result.turnServer, callbackWrapper);
			break;
	}

	this.performanceListenerID = null;
};

View3D.prototype = _.create(View3D.prototype, {
	constructor: View3D
});

View3D.prototype.release = function () {
	if (this.performanceListenerID) {
		this.session.unregisterCallback(this.performanceListenerID);
	}

	this.display.release();
};


View3D.prototype.getPresetLightEffects = function () {
	return this.lightEffects;
};

<span id='View3D-View3D-method-swapRootNode'>/**
</span> * 切换场景根节点
 *
 * @param  {Node} node 新场景根节点，可以为空
 *
 * @return {Node}
 */
View3D.prototype.swapRootNode = function (node) {
	if (Condicio.isNull(node))
		this.session.request(this.objectID, &quot;swapRootNode&quot;, { nodeID: &quot;null&quot; });
	else {
		Condicio.checkIsType(node, Node, &quot;The type of rootNode must be 'Node'!&quot;);
		this.session.request(this.objectID, &quot;swapRootNode&quot;, { nodeID: node.objectID });
	}

	var oldRoot = this.rootNode;
	this.rootNode = node;
	return oldRoot;
};

<span id='View3D-View3D-method-addNode'>/**
</span> * 增加节点
 *
 * @param {Node} node 		待增加的节点
 * @param {Node} parentNode 父节点
 */
View3D.prototype.addNode = function (node, parentNode) {
	Condicio.checkIsType(node, Node, &quot;The type of childNodes must be 'Node'!&quot;);
	Condicio.checkIsType(parentNode, Node, &quot;The type of parentNode must be 'Node'!&quot;);

	var params = {
		nodeID: node.objectID,
		parentNodeID: parentNode.objectID
	};
	this.session.request(this.objectID, &quot;addNode&quot;, params);
};

<span id='View3D-View3D-method-removeNode'>/**
</span> * 删除节点
 * 
 * 只能对没有effect的节点生效
 *
 * @param {Node} node 		待删除的节点
 * @param {Node} parentNode 父节点
 */
View3D.prototype.removeNode = function (node, parentNode) {
	Condicio.checkIsType(node, Node, &quot;The type of childNodes must be 'Node'!&quot;);
	Condicio.checkIsType(parentNode, Node, &quot;The type of parentNode must be 'Node'!&quot;);

	var params = {
		nodeID: node.objectID,
		parentNodeID: parentNode.objectID
	};
	this.session.request(this.objectID, &quot;removeNode&quot;, params);
};

<span id='View3D-View3D-method-addEffect'>/**
</span> * 增加效果
 * 
 * 注意：只能对场景树中的节点调用此接口
 * 注意：要移除添加了Effect的节点前，应该先调用removeEffect接口，再调用removeNode接口
 * 
 *
 * @param {View3D.Effect} effect 	效果
 */
View3D.prototype.addEffect = function (effect) {
	Condicio.checkNotNull(effect, &quot;Effect must`t be NULL!&quot;);
	Condicio.checkNotUndefined(effect, &quot;Effect is undefined!&quot;);
	Condicio.checkIsType(effect, Effect, &quot;The type of effect must be 3DEffect&quot;);

	this.session.request(this.objectID, &quot;addEffect&quot;, { effectID: effect.objectID });

	effect.on(this.session);
};

<span id='View3D-View3D-method-setNearFarPlane'>/**
</span> * 设置远近裁剪面
 * @param {Object} 						option	 						                           设置远近裁剪面参数
 * @param {Number} 					    [option.computeNearFarMode = 2] 设置远近裁剪面模式
 * 包括ComputeNearFarMode.PrecisionMode=0 精确模式, ComputeNearFarMode.FastMode=1 快速模式, 
 * ComputeNearFarMode.FixedMode=2 固定模式, 2 可以指定远近裁剪面
 * FastMode可以解决某些视角下漫游会卡的问题，但可能导致模型闪面
 * @param {Number} 						option.nearPlane				                           近裁剪面值(单位：毫米)
 * @param {Number} 						option.farPlane    			                               远裁剪面值(单位：毫米)
 */
View3D.prototype.setNearFarPlane = function (option) {
	if (Condicio.isUndefined(option.computeNearFarMode)) {
		option.computeNearFarMode = ComputeNearFarMode.PrecisionMode;
	}
	Condicio.checkIsNumber(option.computeNearFarMode, &quot;The type of option.computeNearFarMode must be 'Number'!&quot;);
	Condicio.checkArgument(option.computeNearFarMode &gt;= 0 &amp;&amp; option.computeNearFarMode &lt;= 2, &quot;The computeNearFarMode must &gt;= 0 &amp;&amp; &lt;= 2&quot;);

	if (option.computeNearFarMode == ComputeNearFarMode.FixedMode) {
		Condicio.checkNotUndefined(option.nearPlane, &quot;option.nearPlane must be defined in FixedMode!&quot;);
		Condicio.checkNotUndefined(option.farPlane, &quot;option.farPlane must be defined in FixedMode!&quot;);
		Condicio.checkIsNumber(option.nearPlane, &quot;The type of option.nearPlane must be 'Number'!&quot;);
		Condicio.checkArgument(option.nearPlane &gt; 0, &quot;The option.nearPlane must &gt; 0&quot;);
		Condicio.checkIsNumber(option.farPlane, &quot;The type of option.farPlane must be 'Number'!&quot;);
		Condicio.checkArgument(option.farPlane &gt; option.nearPlane, &quot;The option.farPlane must &gt; option.nearPlane&quot;);
	}
	else {
		if (!Condicio.isUndefined(option.nearPlane)) {
			console.error(&quot;option.nearPlane must be undefined in PrecisionMode &amp;&amp; FastMode!&quot;);
		}
		if (!Condicio.isUndefined(option.farPlane)) {
			console.error(&quot;option.farPlane must be undefined in PrecisionMode &amp;&amp; FastMode!&quot;);
		}
	}

	var params = {
		computeNearFarMode: option.computeNearFarMode,
		nearPlane: option.nearPlane,
		farPlane: option.farPlane
	};
	this.session.request(this.objectID, &quot;setNearFarPlane&quot;, params);
};

<span id='View3D-View3D-method-removeEffect'>/**
</span> * 删除效果
 *
 * @param {View3D.Effect} effect 	效果
 */
View3D.prototype.removeEffect = function (effect) {
	Condicio.checkNotNull(effect, &quot;Effect must`t be NULL!&quot;);
	Condicio.checkNotUndefined(effect, &quot;Effect is undefined!&quot;);
	Condicio.checkIsType(effect, Effect, &quot;The type of effect must be 3DEffect&quot;);

	this.session.request(this.objectID, &quot;removeEffect&quot;, { effectID: effect.objectID });

	effect.off(this.session);
};

<span id='View3D-View3D-method-swapInteraction'>/**
</span> * 交换交互操作
 *
 * @param {Interaction}  interaction 新的交互
 *
 * @return {Interaction} 前一个交互结果
 */
View3D.prototype.swapInteraction = function (interaction) {
	Condicio.checkNotNull(interaction, &quot;Interaction must`t be NULL!&quot;);
	Condicio.checkNotUndefined(interaction, &quot;Interaction is undefined!&quot;);

	// 开启新交互，停止原交互
	var preInteraction = this.interaction;
	this.interaction = interaction;

	if (!Condicio.isNull(preInteraction))
		preInteraction.off(this.session);
	interaction.on(this.session);

	this.session.request(this.objectID, &quot;swapInteraction&quot;, { interactionID: interaction.objectID });

	return preInteraction;
};


<span id='View3D-View3D-method-swapRoamer'>/**
</span> * 设置漫游器
 *
 * @param {Roamer}  		roamer 								要设置的漫游器
 * @param {boolean}			[KeepOldRoamerView = false]				是否保留原视角
 *
 * @return {Roamer}  		返回之前的漫游器
 *
 */
View3D.prototype.swapRoamer = function (roamer, KeepOldRoamerView) {
	
	if(Condicio.isUndefined(KeepOldRoamerView)) {
			KeepOldRoamerView = false;
		}
	
	Condicio.checkIsType(roamer, Roamer, &quot;The type of roamer must be 'Roamer'&quot;);
	Condicio.checkIsBoolean(KeepOldRoamerView, &quot;The KeepOldRoamerView must be 'boolean'!&quot;)

	var params = {
		roamerID : roamer.objectID,
		isKeepOldRoamerView : KeepOldRoamerView
	};
	this.session.request(this.objectID, &quot;swapRoamer&quot;, params);

	var preRoamer = this.roamer;
	this.roamer = roamer;

	return preRoamer;
};

<span id='View3D-View3D-method-findChildren'>/**
</span> * 查询指定名称的子孙节点
 * 
 * 注：当存在多个DrawingRoot时，为了实现最佳性能，建议依次传入每个DrawingRoot及其names进行查找&lt;br/&gt;
 * 注：当找不到node下所查询的名字时(名字错误或传入二维handle)，该接口会返回空值。&lt;br/&gt;
 * 
 * @param {Node} 		node 			父节点（该节点必须是DrawingRootNode）
 * @param {String[]} 	names 			待查找节点的名字，要求名字不能重复，重复的名字在外部事先过滤
 * @param {Function} 	callback 		返回查询结果
 * @param {Error} 		callback.error 	错误
 * @param {Node[]} 		callback.nodes 	查找到的节点集合，如果某个名字对应的节点没有找到，对应位置返回null
 */
View3D.prototype.findChildren = function (node, names, callback) {
	Condicio.checkIsType(node, Node, &quot;The type of parentNode must be 'Node'!&quot;);
	ParamValidation.checkIsTypeArray(names, String, &quot;The type of names must be 'String Array' and valid&quot;);
	Condicio.checkArgument(names.length != 0, &quot;The names-Array must`t be NULL!&quot;);
	Condicio.checkIsFunction(callback, &quot;The type of callback must be 'Function'!&quot;);

	var _this = this;
	var callbackWrapper = function (error, result) {
		if (error)
			callback(error, []);
		else {
			var nodes = result.nodes;

			var tempNodes = [];
			for (var i = 0; i &lt; nodes.length; ++i) {
				if (nodes[i].nodeID !== &quot;&quot;)
					tempNodes.push(new Node(_this.session, nodes[i].nodeID, nodes[i].name));
				else
					tempNodes.push(null); // 没找到给一个空
			}

			callback(null, tempNodes);
		}
	};

	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callbackWrapper, &quot;findChildren&quot;);

	var params = {
		nodeID: node.objectID,
		names: names,
		callbackID: callbackID
	};
	this.session.request(this.objectID, &quot;findChildren&quot;, params);
};

<span id='View3D-View3D-method-findChildrenSet'>/**
</span> * 查询指定名称的子孙节点，并返回节点集合
 * 
 * 注：得到的集合节点可以施加透明、隐藏、变色效果，可以用作排除节点、计算视角的传入参数，除以上应用外其余效果不支持&lt;br/&gt;
 * 注：当存在多个DrawingRoot时，为了实现最佳性能，建议依次传入每个DrawingRoot及其names进行查找&lt;br/&gt;
 * 注：当找不到node下所查询的名字时(名字错误或传入二维handle)，该接口会返回空值。&lt;br/&gt;
 * 
 * @param {Node} 		node 							父节点（该节点必须是DrawingRootNode）
 * @param {Object} 		option	 						传入参数
 * @param {Boolean} 	[option.useHandleSign = false]	是否使用handle标记，若为true使用带handle标记的url查询需要传入url、header；若为false使用节点名字集合查询，需要传入names
 * @param {String[]} 	[option.names] 					待查找节点的名字，要求名字不能重复，重复的名字在外部事先过滤
 * @param {String} 		[option.url] 					带handle标记的url，例如：http://10.1.1.151:443/AIMS/handle/signVerify/38kmh25lp25ug
 * @param {String} 		[option.header] 				头信息，如果使用Cookie则传入例如：Cookie: FULONGTECH_SESSION=XXX
 * 														如果使用token则传入例如：Authorization-UAMS: XXX
 * @param {Function} 	callback 						返回查询结果
 * @param {Error} 		callback.error 					错误
 * @param {Node} 		callback.nodesSet 				查找到的节点集合
 * @param {String} 		callback.warning 				警告
 */
 View3D.prototype.findChildrenSet = function (node, option, callback) {

	Condicio.checkIsType(node, Node, &quot;The type of parentNode must be 'Node'!&quot;);
	Condicio.checkIsType(option, Object, &quot;The type of option must be 'Object'!&quot;);
	if(Condicio.isUndefined(option.useHandleSign)) {
		option.useHandleSign = false;
	}
	Condicio.checkIsBoolean(option.useHandleSign, &quot;The option.useHandleSign must be 'boolean'!&quot;)
	Condicio.checkArgument(option.useHandleSign == false || option.useHandleSign == true, &quot;The option.useHandleSign must false or true&quot;);
	if(option.useHandleSign){
		if (Condicio.isUndefined(option.url)) {
			console.error(&quot;option.url is null&quot;);
		}
		if (Condicio.isUndefined(option.header)) {
			console.error(&quot;option.header is null&quot;);
		}
		Condicio.checkIsString(option.url, &quot;url must be a string&quot;);
		Condicio.checkIsString(option.header, &quot;header must be a string&quot;);
		option.names = [];
	}
	else{
		if (Condicio.isUndefined(option.names)) {
			console.error(&quot;option.names is null&quot;);
		}
		ParamValidation.checkIsTypeArray(option.names, String, &quot;The type of names must be 'String Array' and valid&quot;);
		Condicio.checkArgument(option.names.length != 0, &quot;The names-Array must`t be NULL!&quot;);
		option.url = &quot;&quot;;
		option.header = &quot;&quot;;
	}

	Condicio.checkIsFunction(callback, &quot;The type of callback must be 'Function'!&quot;);

	var newID = Utility.genGUID();
	var _this = this;
	var callbackWrapper = function (error, result) {
		if (error)
			callback(error, null, &quot;&quot;);
		else {
			var warning = &quot;&quot;;
			if(result.message){
				warning = result.message;
			}
			callback(null, new Node(this.session, newID, &quot;&quot;), warning);
		}
	};

	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callbackWrapper, &quot;findChildrenSet&quot;);

	var params = {
		nodeID: node.objectID,
		newID: newID,
		useHandleSign: option.useHandleSign,
		names: option.names,
		url: option.url,
		header: option.header,
		callbackID: callbackID
	};
	this.session.request(this.objectID, &quot;findChildrenSet&quot;, params);
};

<span id='View3D-View3D-method-calcNodesFocusedViewpoint'>/**
</span> * 计算定位到一组节点的视角
 * 
 * @param {Object} 						option	 							计算定位视角参数
 * @param {Node[]} 						option.nodes  						需要计算视角的节点集合(出现为空的节点返回错误)
 * @param {vec3} 						[option.direction]					要求视角的方向，若指定该参数将会按照指定的视角方向计算视角，否则选择最适合的视角方向计算视角
 * @param {Number} 						[option.daRatio=0.6]    			以屏幕宽度或者高度的较小值为基数，乘以该百分比作为显示区域大小的边长（值：0&lt;daRate&lt;=1）传入0时会是默认值
 * @param {Function} 					option.callback   					计算完成后回调
 * @param {InvalidArgumentError} 		option.callback.err  				计算失败返回错误:节点集合中的节点存在一个节点有多个父节点，引发错误
 * @param {Viewpoint} 					option.callback.viewpoint  			返回计算的视角
 */
View3D.prototype.calcNodesFocusedViewpoint = function (option) {
	option.direction = option.direction || new Matrix.vec3.fromValues(0, 0, 0);
	option.daRatio = option.daRatio || 0.6;

	Condicio.checkIsType(option, Object, &quot;The type of option must be 'Object'!&quot;);
	ParamValidation.checkIsTypeArray(option.nodes, Node, &quot;The type of nodes must be 'Node Array' and valid&quot;);
	Condicio.checkArgument(option.nodes.length != 0, &quot;The nodes-Array must`t be NULL!&quot;);
	ParamValidation.checkIsVec3(option.direction, &quot;The type of viewpoint-direction must be 'vec3' and valid&quot;);

	for (var i = 0; i &lt; option.direction.length; ++i) {
		Condicio.checkArgument(!isNaN(option.direction[i]), &quot;The viewpoint-direction is invalid!&quot;);
		Condicio.checkIsNumber(option.direction[i], &quot;The viewpoint-direction must be valid!&quot;);
	}
	Condicio.checkIsNumber(option.daRatio, &quot;The type of data-Ratio must be 'Number'!&quot;);
	Condicio.checkArgument(option.daRatio &lt;= 1 &amp;&amp; option.daRatio &gt; 0, &quot;The data-Ratio must: &gt; 0 &amp;&amp; &lt;= 1&quot;);
	Condicio.checkIsFunction(option.callback, &quot;The type of callback must be 'Function'!&quot;);

	var nodeIDs = [];
	for (var i = 0; i &lt; option.nodes.length; ++i) {
		nodeIDs.push(option.nodes[i].objectID);
	}

	var callbackWrapper = function (error, result) {
		if (!error) {
			var viewpoint = result.viewpoint;

			var eye = CvtForJson.cvtToVec3(viewpoint.eye);
			var center = CvtForJson.cvtToVec3(viewpoint.center);
			var up = CvtForJson.cvtToVec3(viewpoint.up);

			option.callback(null, new Viewpoint(eye, center, up));
		}
		else {
			option.callback(error, null);
		}
	};

	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callbackWrapper, &quot;calcNodesFocusedViewpoint&quot;);

	var params = {
		nodeIDs: nodeIDs,
		direction: [option.direction[0], option.direction[1], option.direction[2]],
		daRatio: option.daRatio,
		callbackID: callbackID
	};
	this.session.request(this.objectID, &quot;calcNodesFocusedViewpoint&quot;, params);
};


var analyseViewgroups = function (viewgroups, session) {
	var viewgroupArr = [];
	for (var i = 0; i &lt; viewgroups.length; ++i) {
		var viewgroup = new Object;

		var eye = CvtForJson.cvtToVec3(viewgroups[i].viewpoint.eye);
		var center = CvtForJson.cvtToVec3(viewgroups[i].viewpoint.center);
		var up = CvtForJson.cvtToVec3(viewgroups[i].viewpoint.up);
		
		viewgroup.viewpoint = new Viewpoint(eye, center, up);
		viewgroup.weight = viewgroups[i].weight;

		viewgroupArr.push(viewgroup);
	}
	return viewgroupArr;
};

<span id='View3D-View3D-method-calcNodesFocusedViewpointGroupByPosition'>/**
</span> * 通过空间位置将节点分组，并计算每一组节点的视点，返回的结果按照每一组的节点个数从多到少排序（当传入的drawingRootNode节点时，则对它包含的所有子节点进行分组计算视角）
 * 
 * @param {Object} 						option	 							  计算定位视角参数
 * @param {Node[]} 						option.nodes  						  需要计算视角的节点集合(出现为空的节点返回错误)，节点类型必须相同，当为drawingRootNode节点时，则对它包含的所有子节点进行视角计算
 * @param {vec3} 						[option.direction]					  要求视角的方向，若指定该参数将会按照指定的视角方向计算视角，否则选择最适合的视角方向计算视角
 * @param {Number} 						[option.daRatio=0.6]    			  以屏幕宽度或者高度的较小值为基数，乘以该百分比作为显示区域大小的边长（值：0&lt;daRate&lt;=1）传入0时会是默认值
 * @param {Function} 					option.callback   					  计算完成后回调
 * @param {InvalidArgumentError} 		option.callback.err  				  计算失败返回错误:节点集合中的节点存在一个节点有多个父节点，引发错误
 * @param {Object[]} 				    option.callback.viewgroups  		  返回所有节点分组的视角
 * @param {Viewpoint} 					option.callback.viewgroups.viewpoint  返回计算的视角
 * @param {Number}						option.callback.viewgroups.weight     视角节点个数所占的权重
 */
 View3D.prototype.calcNodesFocusedViewpointGroupByPosition = function (option) {
	option.direction = option.direction || new Matrix.vec3.fromValues(0, 0, 0);
	option.daRatio = option.daRatio || 0.6;

	Condicio.checkIsType(option, Object, &quot;The type of option must be 'Object'!&quot;);
	ParamValidation.checkIsTypeArray(option.nodes, Node, &quot;The type of nodes must be 'Node Array' and valid&quot;);
	Condicio.checkArgument(option.nodes.length != 0, &quot;The nodes-Array must`t be NULL!&quot;);
	ParamValidation.checkIsVec3(option.direction, &quot;The type of viewpoint-direction must be 'vec3' and valid&quot;);

	for (var i = 0; i &lt; option.direction.length; ++i) {
		Condicio.checkArgument(!isNaN(option.direction[i]), &quot;The viewpoint-direction is invalid!&quot;);
		Condicio.checkIsNumber(option.direction[i], &quot;The viewpoint-direction must be valid!&quot;);
	}
	Condicio.checkIsNumber(option.daRatio, &quot;The type of data-Ratio must be 'Number'!&quot;);
	Condicio.checkArgument(option.daRatio &lt;= 1 &amp;&amp; option.daRatio &gt; 0, &quot;The data-Ratio must: &gt; 0 &amp;&amp; &lt;= 1&quot;);
	Condicio.checkIsFunction(option.callback, &quot;The type of callback must be 'Function'!&quot;);

	var nodeIDs = [];
	for (var i = 0; i &lt; option.nodes.length; ++i) {
		nodeIDs.push(option.nodes[i].objectID);
	}

	var _this = this;
	var callbackWrapper = function (error, result) {
		if (!error) {

			var viewgroups = analyseViewgroups(result.viewgroups, _this.session);

			option.callback(null, viewgroups);
		}
		else {
			option.callback(error, []);
		}
	};

	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callbackWrapper, &quot;calcNodesFocusedViewpointGroupByPosition&quot;);

	var params = {
		nodeIDs: nodeIDs,
		direction: [option.direction[0], option.direction[1], option.direction[2]],
		daRatio: option.daRatio,
		callbackID: callbackID,
	};
	this.session.request(this.objectID, &quot;calcNodesFocusedViewpointGroupByPosition&quot;, params);
};

<span id='View3D-View3D-method-translateViewpoint'>/**
</span> * 定位到某个视角
 * 
 * @param {Viewpoint}   viewpoint	    需要定位到的视角
 * @param {Number}      [time = 0.0]    定位视角时移动的时间(单位：毫秒)，会形成动画效果（不给时间默认为0，会直接定位过去）
 * @param {Function}    [callback]		定位视角完成之后回调,不给回调会有默认值,如果在定位视角后还要进行切换漫游器的操作，必须写在回调里面，确保执行完定位视角再执行后续操作
 */
View3D.prototype.translateViewpoint = function (viewpoint, time, callback) {
	time = time || 0.0;
	callback = callback || function () { };

	Condicio.checkIsType(viewpoint, Viewpoint, &quot;The type of viewpoint must be 'Viewpoint'!&quot;);
	Condicio.checkIsNumber(time, Number, &quot;The type of move-time must be 'Number'!&quot;);
	Condicio.checkArgument(time &gt;= 0, &quot;The param time must '&gt;=0&quot;);
	Condicio.checkIsFunction(callback, Function, &quot;The type of callback must be 'Function'!&quot;);

	time = time / 1000;

	// note：在不需要回调的时候这种调用是否合适
	var callbackWrapper = function (error, result) {
		callback();
	};

	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callbackWrapper, &quot;translateViewpoint&quot;);

	var params = {
		viewpoint: {
			eye: [viewpoint.eye[0], viewpoint.eye[1], viewpoint.eye[2]],
			center: [viewpoint.center[0], viewpoint.center[1], viewpoint.center[2]],
			up: [viewpoint.up[0], viewpoint.up[1], viewpoint.up[2]]
		},
		time: time,
		callbackID: callbackID
	};
	this.session.request(this.objectID, &quot;translateViewpoint&quot;, params);
};

<span id='View3D-View3D-method-calcFocusedViewpointWithPoint'>/**
</span> * 计算定位到指定点的视角
 * 
 * @param {Object} 						option	 							计算定位视角参数
 * @param {vec3} 						option.center					    要求定位的中心点
 * @param {Node} 						option.node  						需要计算视角的节点(出现为空的节点返回错误)
 * @param {vec3} 						[option.direction]					要求视角的方向，若指定该参数将会按照指定的视角方向计算视角，否则选择最适合的视角方向计算视角
 * @param {Number} 						[option.daRatio=0.6]    			以屏幕宽度或者高度的较小值为基数，乘以该百分比作为显示区域大小的边长（值：0&lt;daRate&lt;=1）传入0时会是默认值
 * @param {Function} 					option.callback   					计算完成后回调
 * @param {InvalidArgumentError} 		option.callback.err  				计算失败返回错误:节点集合中的节点存在一个节点有多个父节点，引发错误
 * @param {Viewpoint} 					option.callback.viewpoint  			返回计算的视角
 */
View3D.prototype.calcFocusedViewpointWithPoint = function (option) {
	option.direction = option.direction || new Matrix.vec3.fromValues(0, 0, 0);
	option.daRatio = option.daRatio || 0.6;
	if (Condicio.isUndefined(option.center)) {
		console.error(&quot;option.center is undefined&quot;);
		return;
	}
	if (Condicio.isNull(option.node)) {
		console.error(&quot;option.node is null&quot;);
		return;
	}
	Condicio.checkIsType(option, Object, &quot;The type of option must be 'Object'!&quot;);
	Condicio.checkIsType(option.node, Node, &quot;The type of option.node must be 'Node'!&quot;);
	ParamValidation.checkIsVec3(option.direction, &quot;The type of viewpoint-direction must be 'vec3' and valid&quot;);
	ParamValidation.checkIsVec3(option.center, &quot;The type of viewpoint-center must be 'vec3' and valid&quot;);

	for (var i = 0; i &lt; option.direction.length; ++i) {
		Condicio.checkArgument(!isNaN(option.direction[i]), &quot;The viewpoint-direction is invalid!&quot;);
		Condicio.checkIsNumber(option.direction[i], &quot;The viewpoint-direction must be valid!&quot;);
	}
	for (var i = 0; i &lt; option.center.length; ++i) {
		Condicio.checkArgument(!isNaN(option.center[i]), &quot;The viewpoint-center is invalid!&quot;);
		Condicio.checkIsNumber(option.center[i], &quot;The viewpoint-center must be valid!&quot;);
	}
	Condicio.checkIsNumber(option.daRatio, &quot;The type of data-Ratio must be 'Number'!&quot;);
	Condicio.checkArgument(option.daRatio &lt;= 1 &amp;&amp; option.daRatio &gt; 0, &quot;The data-Ratio must: &gt; 0 &amp;&amp; &lt;= 1&quot;);
	Condicio.checkIsFunction(option.callback, &quot;The type of callback must be 'Function'!&quot;);

	var nodeID = option.node.objectID;

	var callbackWrapper = function (error, result) {
		if (!error) {
			var viewpoint = result.viewpoint;

			var eye = CvtForJson.cvtToVec3(viewpoint.eye);
			var center = CvtForJson.cvtToVec3(viewpoint.center);
			var up = CvtForJson.cvtToVec3(viewpoint.up);

			option.callback(null, new Viewpoint(eye, center, up));
		}
		else {
			option.callback(error, null);
		}
	};

	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callbackWrapper, &quot;calcFocusedViewpointWithPoint&quot;);

	var params = {
		center: [option.center[0], option.center[1], option.center[2]],
		nodeID: nodeID,
		direction: [option.direction[0], option.direction[1], option.direction[2]],
		daRatio: option.daRatio,
		callbackID: callbackID
	};
	this.session.request(this.objectID, &quot;calcFocusedViewpointWithPoint&quot;, params);
};

<span id='View3D-View3D-method-acquireViewpoint'>/**
</span> * 获取当前视角
 *
 * @param {Function} 	callback            获取视角完成后的回调
 * @param {Error} 		callback.err		获取视角失败返回错误
 * @param {Viewpoint} 	callback.viewpoint  获取视角成功返回视角 
 */
View3D.prototype.acquireViewpoint = function (callback) {
	Condicio.checkIsFunction(callback, &quot;The type of callback must be 'Function'!&quot;);

	var callbackWrapper = function (error, result) {
		if (!error) {
			var viewpoint = result.viewpoint;

			var eye = CvtForJson.cvtToVec3(viewpoint.eye);
			var center = CvtForJson.cvtToVec3(viewpoint.center);
			var up = CvtForJson.cvtToVec3(viewpoint.up);

			callback(null, new Viewpoint(eye, center, up));
		}
		else
			callback(error, null);
	}

	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callbackWrapper, &quot;acquireViewpoint&quot;);

	this.session.request(this.objectID, &quot;acquireViewpoint&quot;, { callbackID: callbackID });
};


<span id='View3D-View3D-method-worldCoordToScreenCoord'>/**
</span> * 世界坐标转屏幕坐标
 * 
 * @param {vec3} worldCrood 		需要转换成屏幕坐标的世界坐标
 * @param {mat4} viewMatrix 		视图变换矩阵,利用registerVpMatrixChangedCallback获取
 * @param {mat4} projectionMatrix 	投影变换矩阵,利用registerVpMatrixChangedCallback获取
 * @param {mat4} windowsMatrix 		窗口变换矩阵,利用registerVpMatrixChangedCallback获取
 * @returns {vec3}					计算后的屏幕坐标,vec3[0] 为X坐标,vec3[1]为y坐标,vec3[2]为深度.深度值介于0~1之间,可以用于判断遮挡关系,深度值越小,离观察者越近,越大则越远
 */
View3D.prototype.worldCoordToScreenCoord = function (worldCrood, viewMatrix,projectionMatrix,windowsMatrix) {
	ParamValidation.checkIsVec3(worldCrood, &quot;The type of worldCrood must be 'vec3' and valid&quot;);
	ParamValidation.checkIsMat4(viewMatrix, &quot;The type of worldCrood must be 'mat4' and valid&quot;);
	ParamValidation.checkIsMat4(projectionMatrix, &quot;The type of worldCrood must be 'mat4' and valid&quot;);
	ParamValidation.checkIsMat4(windowsMatrix, &quot;The type of worldCrood must be 'mat4' and valid&quot;);
	
	var s1 = Matrix.vec3.create();
	var s2 = Matrix.vec3.create();
	var s3 = Matrix.vec3.create();
	Matrix.vec3.transformMat4(s1,worldCrood,viewMatrix);
	Matrix.vec3.transformMat4(s2,s1,projectionMatrix);
	Matrix.vec3.transformMat4(s3,s2,windowsMatrix);

	return s3;
};


<span id='View3D-View3D-method-acquireScreenCrood'>/**
</span> * 获取世界坐标对应的屏幕坐标
 * @param {vec3} 		worldCrood 				世界坐标
 * @param {Function} 	callback 				获取屏幕坐标完成候的回调
 * @param {Error} 		callback.err			获取屏幕坐标失败返回错误
 * @param {vec3} 		callback.screencrood  	获取屏幕坐标成功返回屏幕坐标
 */
 View3D.prototype.acquireScreenCrood = function (worldCrood,callback) {
	ParamValidation.checkIsVec3(worldCrood, &quot;The type of worldCrood must be 'vec3' and valid&quot;);
	Condicio.checkIsFunction(callback, &quot;The type of callback must be 'Function'!&quot;);
	
	var callbackWrapper = function (error,vm,pm,wm ) {
		if (!error) {
            var screenCoord = this.worldCoordToScreenCoord(worldCrood,vm,pm,wm);
			callback(null, screenCoord);
		}
		else
			callback(error, null);
	}.bind(this)
	this.acquireVpMatrix(callbackWrapper);
};

<span id='View3D-View3D-method-acquireVpMatrix'>/**
</span> * 获取VpMatrix三个视图相关的矩阵
 * 回调返回三个矩阵,再配合worldCoordToScreenCoord函数计算世界坐标对应的屏幕坐标
 * @param {Function} 	callback 					获取VPWMatrix完成后的回调
 * @param {Error} 		callback.err				获取VPWMatrix失败返回错误
 * @param {matrix} 		callback.viewMatrix  		获取VPWMatrix成功返回viewMatrix
 * @param {matrix} 		callback.projectionMatrix   获取VPWMatrix成功返回projectionMatrix
 * @param {matrix} 		callback.windowsMatrix  	获取VPWMatrix成功返回windowsMatrix 
 */
 View3D.prototype.acquireVpMatrix = function (callback) {
	Condicio.checkIsFunction(callback, &quot;The type of callback must be 'Function'!&quot;);

	var callbackWrapper = function (error, result) {
		if (!error) {
			var vm = CvtForJson.cvtToMat4(result.viewMatrix);
			var pm = CvtForJson.cvtToMat4(result.projectionMatrix);
			var wm = CvtForJson.cvtToMat4(result.windowMatrix);	
			callback(null, vm,pm,wm);
		}
		else
			callback(error, null,null,null);
	}

	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callbackWrapper, &quot;acquireVpMatrix&quot;);

	var params = {
		callbackID: callbackID
	};

	this.session.request(this.objectID, &quot;acquireVpMatrix&quot;,params);
};

<span id='View3D-View3D-method-registerVpMatrixChangedCallback'>/**
</span> * 注册VpMatrix改变后回调函数
 * 回调返回三个矩阵,再配合worldCoordToScreenCoord函数计算世界坐标对应的屏幕坐标
 * @param {Function} 	callback 					获取VPWMatrix完成后的回调
 * @param {Error} 		callback.err				获取VPWMatrix失败返回错误
 * @param {matrix} 		callback.viewMatrix  		获取VPWMatrix成功返回viewMatrix
 * @param {matrix} 		callback.projectionMatrix   获取VPWMatrix成功返回projectionMatrix
 * @param {matrix} 		callback.windowsMatrix  	获取VPWMatrix成功返回windowsMatrix 
 */
View3D.prototype.registerVpMatrixChangedCallback = function (callback) {
	Condicio.checkIsFunction(callback, &quot;The type of callback must be 'Function'!&quot;);

	var callbackWrapper = function (error, result) {
		if (!error) {

			var vm = CvtForJson.cvtToMat4(result.viewMatrix);
			var pm = CvtForJson.cvtToMat4(result.projectionMatrix);
			var wm = CvtForJson.cvtToMat4(result.windowMatrix);			

			callback(null, vm,pm,wm);
		}
		else
			callback(error, null,null,null);
	}

	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callbackWrapper, &quot;registerVpMatrixChangedCallback&quot;,true);

	var params = {
		callbackID: callbackID
	};

	this.session.request(this.objectID, &quot;registerVpMatrixChangedCallback&quot;,params);
};

<span id='View3D-View3D-method-transformNodes'>/**
</span> * 移动节点（该节点是MatrixTransform节点才支持移动）
 * 支持平移、旋转
 * 进行移动的所有点坐标信息，必须统一为世界坐标系坐标或局部坐标系坐标
 * 注意：当使用matrix进行移动节点的时候，如果此时的matrix是一个复合的mat（有多个旋转、移动），且有移动时间时，
 * 此时的动画效果中间过程是无法预估的运动轨迹，但是始末位置都是对的。
 * 移动方式有两种：1.使用matrix进行节点移动， 2.使用translations或者rotation进行节点移动，二者必用其一。
 * @param {Object}      			option	 													移动节点参数
 * @param {Object[]}    			option.transformations										需要做的移动变换集合(同一组集合中要移动的节点不能重复)
 * @param {Node}        			option.transformations.node  								需要移动的节点
 * @param {mat4}					[option.transformations.matrix]								需要进行变换的矩阵信息（&lt;a href=&quot;https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/75957738/VIC&quot;&gt;参考文档详见&lt;/a&gt;）
 * @param {Boolean}					[option.transformations.isLocalCoordinate = false]			移动信息坐标是否为局部坐标（默认处理方式为世界坐标）
 * @param {vec3}        			[option.transformations.translation]						需要将目标节点向某个方向移动的位置
 * @param {Object}					[option.transformations.rotation]							目标节点旋转参数
 * @param {vec3}					option.transformations.rotation.position 					旋转点
 * @param {vec3}					option.transformations.rotation.direction					旋转轴方向
 * @param {Number}					option.transformations.rotation.angle						旋转角度（角度大于零度，根据其旋转轴方向，使用右手法则，可确定模型旋转方向，负角度则相反）
 * @param {Number}      			[option.duration = 0]										移动节点经过的时间（单位：毫秒）
 * @param {Function}    			option.callback 				    						节点移动完成时回调
 * @param {InvalidArgumentError} 	option.callback.err 										错误:某个节点是不可移动的
 */
View3D.prototype.transformNodes = function (option) {
	option.duration = option.duration || 0;

	//进行一次深拷贝 之后对深拷贝的对象进行操作
	var deepClonelOption = _.cloneDeep(option);

	Condicio.checkIsObject(option, &quot;The type of option must be 'Object'!&quot;);
	ParamValidation.checkIsTypeArray(option.transformations, Object, &quot;The type of transformations must be 'Object Array'!&quot;);
	Condicio.checkArgument(option.transformations.length !== 0, &quot;The transformations-Array must`t be NULL!&quot;);

	var transformations = [];
	for (var i = 0; i &lt; deepClonelOption.transformations.length; ++i) {
		Condicio.checkIsType(option.transformations[i].node, Node, &quot;Every node must be valid!&quot;);
		var isLocalCoordinate = deepClonelOption.transformations[i].isLocalCoordinate || false;

		//使用mat或者translation/rotation进行变换

		if ((Condicio.isUndefined(option.transformations[i].matrix))
			&amp;&amp; (Condicio.isUndefined(option.transformations[i].translation))
			&amp;&amp; (Condicio.isUndefined(option.transformations[i].rotation))) {
			console.error(&quot;参数错误&quot;);
			return;
		}

		if (!Condicio.isUndefined(option.transformations[i].matrix)) {
			ParamValidation.checkIsMat4(option.transformations[i].matrix, &quot;The type of matrix must be 'mat4' and valid&quot;);

			var transformMat = [];
			for (var n = 0; n &lt; 16; n++)
				transformMat.push(deepClonelOption.transformations[i].matrix[n]);

			deepClonelOption.transformations[i].matrix = transformMat;
			transformations.push({ nodeID: deepClonelOption.transformations[i].node.objectID, matrix: deepClonelOption.transformations[i].matrix });
		}

		var param = {
			nodeID: deepClonelOption.transformations[i].node.objectID,
			isLocalCoordinate: isLocalCoordinate
		};

		if (!Condicio.isUndefined(option.transformations[i].translation)) {
			ParamValidation.checkIsVec3(option.transformations[i].translation, &quot;The type of translation must be 'vec3' and valid&quot;);

			var translation = [];
			for (var j = 0; j &lt; 3; ++j)
				translation.push(deepClonelOption.transformations[i].translation[j]);

			deepClonelOption.transformations[i].translation = translation;


			param.translation = deepClonelOption.transformations[i].translation;
		}

		if (!Condicio.isUndefined(option.transformations[i].rotation)) {
			Condicio.checkIsType(option.transformations[i].rotation, Object, &quot;The type of rotation must be 'Object'!&quot;);
			ParamValidation.checkIsVec3(option.transformations[i].rotation.position, &quot;The type of rotation.position must be 'vec3' and valid&quot;);
			ParamValidation.checkIsVec3(option.transformations[i].rotation.direction, &quot;The type of rotation.direction must be 'vec3' and valid&quot;);
			

			var position = [];
			for (var k = 0; k &lt; 3; ++k)
				position.push(deepClonelOption.transformations[i].rotation.position[k]);

			var direction = [];
			for (var l = 0; l &lt; 3; ++l)
				direction.push(deepClonelOption.transformations[i].rotation.direction[l]);

			deepClonelOption.transformations[i].rotation.position = position;
			deepClonelOption.transformations[i].rotation.direction = direction;

			param.rotation = deepClonelOption.transformations[i].rotation;
		}
		transformations.push(param);
	}
	Condicio.checkIsNumber(option.duration, &quot;The type of transform-node-time must be 'Number'!&quot;);
	Condicio.checkIsFunction(option.callback, &quot;The type of callback must be 'Function'!&quot;);

	var callbackWrapper = function (error, result) {
		if (error) {
			option.callback(error);
		}
		else
			option.callback(null);
	};

	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callbackWrapper, &quot;transformNodes&quot;,true);

	var params = {
		transformations: transformations,
		duration: option.duration / 1000,
		callbackID: callbackID
	};
	this.session.request(this.objectID, &quot;transformNodes&quot;, params);
};

<span id='View3D-View3D-method-executeCommand'>/**
</span> * 执行Command命令
 *
 * @param {Command} 	command            需要执行的Command
 *
 */
View3D.prototype.executeCommand = function (command) {
	Condicio.checkIsType(command, Command, &quot;The type of command must be 'Command'!&quot;);
	Condicio.checkNotNull(command, &quot;command parameter cannot be null&quot;);
	Condicio.checkNotUndefined(command, &quot;command parameter cannot be undefined&quot;);

	this.session.request(this.objectID, &quot;executeCommand&quot;, { commandID: command.objectID });
};

<span id='View3D-View3D-method-captureScreenShot'>/**
</span> * 屏幕截图
 *
 * @param {Function} 	callback            获取屏幕截图完成后的回调
 * @param {Error} 		callback.err		获取屏幕截图失败返回错误
 * @param {String} 		callback.screenShot 获取屏幕截图成功返回截图数据
 */
View3D.prototype.captureScreenShot = function (callback) {
	Condicio.checkIsFunction(callback, Function, &quot;The type of 'callback' must be 'Function'!&quot;);

	var callbackWrapper = function (error, result) {
		if (error) {
			callback(error, null);
		}
		else
			callback(null, result.imageData);
	};

	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callbackWrapper, &quot;captureScreenShot&quot;);

	this.session.request(this.objectID, &quot;captureScreenShot&quot;, { callbackID: callbackID });
};

//判断是否为逆时针顺序的点
function isClockWise(p1, p2, p3, norm) {
	norm = norm || Matrix.vec3.fromValues(0, 0, 1);
	var norm1 = Matrix.vec3.create();
	Matrix.vec3.sub(norm1, p2, p1);
	var norm2 = Matrix.vec3.create();
	Matrix.vec3.sub(norm2, p3, p2);
	var norm0 = Matrix.vec3.create();
	Matrix.vec3.cross(norm0, norm1, norm2);
	return Matrix.vec3.dot(norm0, norm) &lt; 0;
};
//判断是否为逆时针顺序的点集合
function isPtArrayClockWise(pts) {
	if (pts.length &lt; 3) {
		return true;
	}
	if (pts.length == 3) {
		isClockWise(pts[0], pts[1], pts[2]);
	}

	var ptInit = pts[0];
	var xMin = ptInit[0];
	var xMax = ptInit[0];
	var yMin = ptInit[1];
	var yMax = ptInit[1];
	var index1 = 0;
	var index2 = 0;
	var index3 = 0;
	var index4 = 0;

	for (var i = 1; i &lt; 4; ++i) {
		var ptCur = pts[i];
		if (xMin &gt; ptCur[0]) {
			xMin = ptCur[0]
			index1 = i;
		}

		if (xMax &lt; ptCur[0]) {
			xMax = ptCur[0];
			index2 = i;
		}

		if (yMin &gt; ptCur[1]) {
			yMin = ptCur[1];
			index3 = i;
		}

		if (yMax &lt; ptCur[1]) {
			yMax = ptCur[1];
			index4 = i;
		}
	}

	var indexArray = [];
	for (var i = 0; i &lt; 4; ++i) {
		if (i === index1) {
			indexArray.push(i);
		}
		if (i === index2) {
			indexArray.push(i);
		}
		if (i === index3) {
			indexArray.push(i);
		}
		if (i === index4) {
			indexArray.push(i);
		}
	}
	return isClockWise(pts[indexArray[0]], pts[indexArray[1]], pts[indexArray[2]]);
};
//将点序转为逆时针
function setPtsToAntiClock(pt3darr) {
	var vpt3darr = [];
	if (isPtArrayClockWise(pt3darr)) {
		for (var i = 3; i &gt;= 0; --i) {
			vpt3darr.push(pt3darr[i]);
		}
		for (var i = 7; i &gt;= 4; --i) {
			vpt3darr.push(pt3darr[i]);
		}
	}
	else {
		vpt3darr = pt3darr;
	}
	return vpt3darr;
};

var analysePicked = function (picked, session) {
	var pickedArray = [];
	for (var i = 0; i &lt; picked.length; ++i) {
		var pick = new Object;

		pick.scale = picked[i].scale;
		pick.area = picked[i].area;

		pick.nodePath = [];

		var nodePath = picked[i].nodePath;
		for (var j = 0; j &lt; nodePath.length; ++j) {
			pick.nodePath.push(new Node(session, nodePath[j].nodeID, nodePath[j].name));
		}

		pickedArray.push(pick);
	}
	return pickedArray;
};


<span id='View3D-View3D-method-queryNodesInBox'>/**
</span> * 得到box范围内的节点以及节点在其中所占比例及面积
 * 
 * @param {Object}      option	 				 box求交参数
 * @param {vec3[]}      option.boxPoints		 Box八个顶点（按照先底面后顶面，每个面的点为连续（顺时针或逆时针），上下底面对应）
* @param {Function} 	callback                 计算完成后的回调 
 * @param {Error} 		callback.err		     求交失败返回错误
 * @param {Object[]}    callback.picked          计算结果
 * @param {Number} 		callback.picked.scale    节点在所选区域中的部分占整体的比例
 * @param {Number} 		callback.picked.area     节点在所选区域中的部分占的面积
 * @param {Node[]} 		callback.picked.nodePath 选中的节点路径，即从被选中的节点到根节点的所有节点按序排列
 * 
 *
 */
View3D.prototype.queryNodesInBox = function (option, callback) {

	Condicio.checkIsType(option, Object, &quot;The type of option must be 'Object'!&quot;);
	ParamValidation.checkIsTypeArray(option.boxPoints, Matrix.glMatrix.ARRAY_TYPE, &quot;The type of points must be 'Number Array' and valid!&quot;);
	Condicio.checkIsFunction(callback, &quot;The type of callback must be 'Function'!&quot;);

	var boxPointArray = [];
	for (var i = 0; i &lt; option.boxPoints.length; ++i) {
		var point = [];
		for (var j = 0; j &lt; 3; ++j) {
			point.push(option.boxPoints[i][j]);
		}
		boxPointArray.push(point);
	}

	boxPointArray = setPtsToAntiClock(boxPointArray);

	var _this = this;
	var callbackWrapper = function (error, result) {

		var picks = result.picked;
		//解析点击结果
		var picked = analysePicked(picks, _this.session);

		callback(null, picked);

	}

	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callbackWrapper, &quot;queryNodesInBox&quot;);

	var params = {
		boxPointArray: boxPointArray,
		callbackID: callbackID
	};

	this.session.request(this.objectID, &quot;queryNodesInBox&quot;, params);
};

<span id='View3D-View3D-method-calcAABB'>/**
</span> * 计算一组节点的AABB包围盒
 * 注：只支持对当前加载的模型节点进行计算, 目前不支持对装置根节点计算AABB（就是nodes给一个drawingRoot）
 * 计算的是能够包围所有这组节点的包围盒，而不是分别求出各自的包围盒
 * &lt;a href=&quot;https://baike.baidu.com/item/AABB%E7%9B%92/10087682?fr=aladdin&quot;&gt;AABB相关解释请参考&lt;/a&gt;
 * 
 * @param {Node[]}     	nodes 					 			需要包围的一组节点 
 * @param {Function} 	callback                 			计算完成后的回调 
 * @param {Error} 		callback.err		     			返回错误
 *               		err {InvalidArgumentError}       	节点不支持计算包围盒（如有两个父节点）
 * @param {Object}    	callback.aabb	         			计算结果
 * @param {vec3} 		callback.aabb.min 					左下角坐标
 * @param {vec3} 		callback.aabb.max		 			右上角坐标
 *
 */
View3D.prototype.calcAABB = function (nodes, callback) {
	ParamValidation.checkIsTypeArray(nodes, Node, &quot;The  type of nodes must be 'Node Array' and valid!&quot;);

	var nodeIDs = CvtForJson.cvtNodeArray(nodes);

	var callbackWrapper = function (error, result) {
		if (error)
			callback(error, null);
		else {
			var min = result.aabb.min;
			var max = result.aabb.max;

			var aabb = {
				min: CvtForJson.cvtToVec3(min),
				max: CvtForJson.cvtToVec3(max)
			};

			callback(null, aabb);
		}
	}

	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callbackWrapper, &quot;calcAABB&quot;);

	var params = {
		nodeIDs: nodeIDs,
		callbackID: callbackID
	};

	this.session.request(this.objectID, &quot;calcAABB&quot;, params);
};

<span id='View3D-View3D-method-queryNodesAcrossPolygon'>/**
</span> * 计算一个有边界面与指定节点的相交结果
 * 只会对可见节点求交，可见节点是已加载并且没有被隐藏的节点
 *
 * @param {Object}     	option 					 			求交需要的参数
 * @param {vec3[]} 		option.polygon               		一个平面上的闭合边界点组（必须是凸多边形的边界点）
 * @param {Function} 	option.callback                 	计算完成后的回调 
 * @param {Error} 		option.callback.err		     		返回错误
 *               		err {InvalidArgumentError}       	polygon不满足要求
 * @param {Object[]}    option.callback.results	        	求交结果
 * @param {Node[]} 		option.callback.results.nodePath  	求交到的节点路径，即从被选中的节点到根节点的所有节点按序排列
 * @param {vec3[]} 		option.callback.results.polyline  	交到的点集合
 *
 */

View3D.prototype.queryNodesAcrossPolygon = function (option) {
	Condicio.checkIsType(option, Object, &quot;The type of option must be 'Object'!&quot;);
	ParamValidation.checkIsVec3Array(option.polygon, &quot;The type of option.polygon must be 'Number Array' and valid!&quot;);
	Condicio.checkIsFunction(option.callback, &quot;The type of option.callback must be 'Function'!&quot;);

	var bndPointArray = CvtForJson.cvtVec3Array(option.polygon);

	var _this = this;
	var callbackWrapper = function (error, result) {
		if (error)
			option.callback(error, null);
		else {
			var len = result.intersections.length;
			var itns = [];
			for (var i = 0; i &lt; len; ++i) {
				var polyline = CvtForJson.cvtToVec3Array(result.intersections[i].polyline);
				var np = result.intersections[i].nodePath;
				var nodePath = cvtToNodeArray(np, _this.session);

				var itn = {
					nodePath: nodePath,
					polyline: polyline
				};
				itns.push(itn);
			}

			option.callback(null, itns);
		}
	}

	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callbackWrapper, &quot;queryNodesAcrossPolygon&quot;);

	var params = {
		polygon: bndPointArray,
		callbackID: callbackID
	};

	this.session.request(this.objectID, &quot;queryNodesAcrossPolygon&quot;, params);
};

<span id='View3D-View3D-method-changeBackgroundColor'>/**
</span> * 改变场景的背景色
 * @param {Color}  	color				背景颜色，颜色中的alpha值无效
 *
 */
View3D.prototype.changeBackgroundColor = function (color) {
	//Condicio.checkIsType(color, COLOR, &quot;The type of color must be 'Color'&quot;);

	var params = {
		color: color.rgbaArray()
	};

	this.session.request(this.objectID, &quot;changeBackgroundColor&quot;, params);
};

<span id='View3D-View3D-method-displayNavigator'>/**
</span> * 显示导航立方体，该接口直接调用就可以，不需要调用addNode接口挂在指定节点下
 * &lt;a href=&quot;https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/136446059/2019-06-25+BRS&quot;&gt;详细请参考&lt;/a&gt;
 * @param {Object} 		  [option]  							显示导航立方体参数
 * @param {vec2} 		  [option.offset = vec2(-90, 80)]   导航立方体的位置参数，第一个值为正（负）表示模型中央与窗口左侧（右侧）的像素偏移量，第二个值为正（负）表示模型中央与窗口上（下）侧的像素偏移量
 * @param {double} 	      [option.scale = 150]				自定义导航立方体的大小，默认值为立方体边长为150像素
 * @param {Boolean}       [option.isDisplay = true]			是否显示导航立方体，默认值为显示
 */
 View3D.prototype.displayNavigator = function (option) {
	
	if (Condicio.isUndefined(option))  {
		option = {
			offset : Matrix.vec2.fromValues(-90, 80),
			scale : 150,
			isDisplay : true
		};
	}
	if (Condicio.isUndefined(option.offset))  {
		 option.offset = Matrix.vec2.fromValues(-90, 80)
	}
	if (Condicio.isUndefined(option.scale))  {
		 option.scale = 150
	}
	if (Condicio.isUndefined(option.isDisplay))  {
		option.isDisplay = true
   }
	 Condicio.checkIsObject(option, &quot;param option must be a Object&quot;);
	 ParamValidation.checkIsVec2(option.offset, &quot;The type of option-offset must be 'vec2' &quot;);
	 Condicio.checkIsNumber(option.scale, &quot;The type of option-scale must be 'Number'!&quot;);
     Condicio.checkIsBoolean(option.isDisplay, &quot;The type of option-isDisplay must be 'Boolen'!&quot;);

	 var posArray = CvtForJson.cvtVec2(option.offset);
	 var params = {
		 posArray: posArray,
		 scale: option.scale,
		 isDisplay : option.isDisplay
	 };
	 this.session.request(this.objectID, &quot;displayNavigator&quot;, params);
 };

<span id='View3D-View3D-method-displaySkybox'> /**
</span> * 显示天空盒，该接口直接调用就可以，不需要调用addNode接口
 * @param {Boolean}       [isDisplay = true]			是否显示天空盒，默认值为显示
 */
  View3D.prototype.displaySkybox = function (isDisplay) {
	
	if (Condicio.isUndefined(isDisplay))  {
		isDisplay = true
    }
	
     Condicio.checkIsBoolean(isDisplay, &quot;The type of isDisplay must be 'Boolen'!&quot;);

	 var params = {
		 isDisplay : isDisplay
	 };
	 this.session.request(this.objectID, &quot;displaySkybox&quot;, params);
 };
<span id='View3D-View3D-method-createAddEffectCommand'>/**
</span> * 创建增加效果命令
 *
 * @param  {Effect} effect    想要创建增加效果的命令。  备注：此接口创建的command，当移除Effect时，应使用createRemoveEffectCommand接口，不支持使用removeEffect操作
 *
 * @return {Command}
 */
View3D.prototype.createAddEffectCommand = function (effect) {
	Condicio.checkNotNull(effect, &quot;Effect must`t be NULL!&quot;);
	Condicio.checkNotUndefined(effect, &quot;Effect is undefined!&quot;);
	Condicio.checkIsType(effect, Effect, &quot;The type of effect must be 3DEffect&quot;);

	var commandID = Utility.genGUID();
	var params = {
		commandID: commandID,
		effectID: effect.objectID
	};
	this.session.request(this.objectID, &quot;createAddEffectCommand&quot;, params);

	effect.on(this.session);

	return new Command(commandID);
};

<span id='View3D-View3D-method-createRemoveEffectCommand'>/**
</span> * 创建删除效果命令
 *
 * @param  {Effect} effect    想要创建删除效果的命令。   备注：此接口和createAddEffectCommand接口对应使用,
 *                            createAddEffectCommand创建的command被应用后，若要移除Effect，则必须用此接口，不支持使用removeEffect操作！
 *
 * @return {Command}
 */
View3D.prototype.createRemoveEffectCommand = function (effect) {
	Condicio.checkNotNull(effect, &quot;Effect must`t be NULL!&quot;);
	Condicio.checkNotUndefined(effect, &quot;Effect is undefined!&quot;);
	Condicio.checkIsType(effect, Effect, &quot;The type of effect must be 3DEffect&quot;);

	var commandID = Utility.genGUID();
	var params = {
		commandID: commandID,
		effectID: effect.objectID
	};
	this.session.request(this.objectID, &quot;createRemoveEffectCommand&quot;, params);

	effect.off(this.session);

	return new Command(commandID);
};

<span id='View3D-View3D-method-createCompositeCommand'>/**
</span> * 创建组合命令	
 * 
 * @param  {Command[]} commands		创建组合命令 commands是按顺序执行的。
 * @return {Command}
 */
View3D.prototype.createCompositeCommand = function (commands) {

	ParamValidation.checkIsTypeArray(commands, Command, &quot;The type of commands must be 'Command Array' and valid&quot;)
	Condicio.checkArgument((commands.length !== 0), &quot;The commands-Array must`t be NULL!&quot;);

	var commandIDs = CvtForJson.cvtCommandArray(commands)
	var commandID = Utility.genGUID();
	var params = {
		commandID: commandID,
		commandIDs: commandIDs
	};
	this.session.request(this.objectID, &quot;createCompositeCommand&quot;, params);

	return new Command(commandID);
};

var checkThreePointsCollinear = function (vec3Array) {
	var y = (vec3Array[2][1] - vec3Array[0][1]) / (vec3Array[1][1] - vec3Array[0][1]);
	var x = (vec3Array[2][0] - vec3Array[0][0]) / (vec3Array[1][0] - vec3Array[0][0]);
	var z = (vec3Array[2][2] - vec3Array[0][2]) / (vec3Array[1][2] - vec3Array[0][2]);
	Condicio.checkArgument(x != y || y != z || x != z, &quot;The interface supports up to 32 planes&quot;);
};

<span id='View3D-View3D-method-queryNodesInConvexPolyhedron'>/**
</span> * 计算凸多面体范围内的节点以及节点在其中所占比例及面积（最大支持32个面数量的求交）
 * 
 * @param   {Object[]}  planeArray               面存储面的数组
 * @param	{Object}	planeArray.plane		 面对象
 * @param  	{vec3[]} 	planeArray.plane.points  组成面的点
 * @param  	{vec3[]} 	planeArray.plane.normal  该面的法线（取该面三个点，三个点的存放顺序决定这个面的法线，顺序遵循右手定理）
 * 
 * @param {Function} 	callback                 计算完成后的回调 
 * @param {Error} 		callback.err		     求交失败返回错误
 * @param {Object[]}    callback.picked          计算结果
 * @param {Number} 		callback.picked.scale    节点在所选区域中的部分占整体的比例
 * @param {Number} 		callback.picked.area     节点在所选区域中的部分占的面积
 * @param {Node[]} 		callback.picked.nodePath 选中的节点路径，即从被选中的节点到根节点的所有节点按序排列
 */
View3D.prototype.queryNodesInConvexPolyhedron = function (planeArray, callback) {

	ParamValidation.checkIsTypeArray(planeArray, Object, &quot;The type of planeArray must be 'Object Array&quot;);

	Condicio.checkArgument(planeArray.length &lt;= 32, &quot;The interface supports up to 32 planes&quot;);

	for (var i = 0; i &lt; planeArray.length; i++) {
		var plane = planeArray[i];
		Condicio.checkIsType(plane, Object, &quot;The type of planeArray.plane must be 'Object'!&quot;);

		Condicio.checkArgument(plane.points != undefined &amp;&amp; plane.points.length != 0, &quot;plane.points Cannot be empty&quot;);
		ParamValidation.checkIsVec3Array(plane.points, &quot;The type of planeArray.plane.points must be 'Vec3 Array' and valid!&quot;);

		Condicio.checkArgument(plane.normal != undefined &amp;&amp; plane.normal.length != 0, &quot;plane.normal Cannot be empty&quot;);
		ParamValidation.checkIsVec3Array(plane.normal, &quot;The type of planeArray.plane.normal must be 'Vec3 Array' and valid!&quot;);
		Condicio.checkArgument(plane.normal.length === 3, &quot;plane.normal It should be three points&quot;);
		checkThreePointsCollinear(plane.normal);
	}

	Condicio.checkIsFunction(callback, &quot;The type of callback must be 'Function'!&quot;);

	var planesArray = [];
	for (var i = 0; i &lt; planeArray.length; i++) {
		var plane = planeArray[i];
		for (var j = 0; j &lt; plane.normal.length; j++) {
			var normalPoint = plane.normal[j];
			var arrayPoint = CvtForJson.cvtVec3(normalPoint);
			planesArray.push(arrayPoint);
		}
	}

	var _this = this;
	var callbackWrapper = function (error, result) {
		if (error)
			callback(error, null);
		else {
			var picks = result.picked;
			//解析点击结果
			var picked = analysePicked(picks, _this.session);

			callback(error, picked);
		}
	}

	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callbackWrapper, &quot;queryNodesInConvexPolyhedron&quot;);

	var params = {
		planeArray: planesArray,
		callbackID: callbackID
	};

	this.session.request(this.objectID, &quot;queryNodesInConvexPolyhedron&quot;, params);
};

<span id='View3D-View3D-method-calculateCircularPoints'>/**
</span> * 计算组成圆形的点的集合（圆形tracker和该接口组合使用时，二者pointNum需要一致）
 *
 * @param {Object}     	option 					      需要的参数
 * @param {vec3} 		option.startPt                起始点
 * @param {vec3} 	    option.endPt                  终点 
 * @param {Number} 	    option.pointNum               画圆的点数量（整数 3-65536） 
 * 
 * @param {Function} 	callback                      计算完成后的回调 
 * @param {Error} 		callback.err		          求交失败返回错误
 * @param {vec3[]}      callback.circularPointsArray  计算结果数组
 */

View3D.prototype.calculateCircularPoints = function (option, callback) {
	ParamValidation.checkIsVec3(option.startPt, &quot;The type of option.startPt must be 'vec3' and valid&quot;);
	ParamValidation.checkIsVec3(option.endPt, &quot;The type of option.endPt must be 'vec3' and valid&quot;);
	Condicio.checkArgument(option.startPt[0] != option.endPt[0] &amp;&amp; option.startPt[1] != option.endPt[1], &quot;option.startPt and option.endPt The XY of cannot be the same&quot;);

	if (option.pointNum === undefined) { option.pointNum = 30; }
	Condicio.checkIsNumber(option.pointNum, &quot;option.pointNum must be a number&quot;);
	Condicio.checkArgument(option.pointNum &gt;= 3 &amp;&amp; option.pointNum &lt;= 65536 &amp;&amp; option.pointNum % 1 === 0, &quot;option.pointNum must be a positive integer with values from 3 to 65536&quot;);

	Condicio.checkIsFunction(callback, &quot;The type of callback must be 'Function'!&quot;);

	var callbackID = Utility.genGUID();
	var callbackWrapper = function (error, result) {
		if (error)
			callback(error, null);
		else {
			var circularPointsArray = CvtForJson.cvtToVec3Array(result.circularPointsArray);
			callback(null, circularPointsArray);
		}
	}
	this.session.registerCallback(callbackID, callbackWrapper, &quot;calculateCircularPoints&quot;);

	var startPoint = CvtForJson.cvtVec3(option.startPt);
	var endPoint = CvtForJson.cvtVec3(option.endPt);

	var params = {
		callbackID: callbackID,
		pointNum: option.pointNum,
		startPoint: startPoint,
		endPoint: endPoint

	};
	this.session.request(this.objectID, &quot;calculateCircularPoints&quot;, params);
};

<span id='View3D-View3D-method-coneCvtConvexPolyhedron'>/**
</span> * 此接口用于圆坑点集合向凸多面体求交接口的对接
 *
 * @param  	{vec3[]} 	topPts     		圆柱或圆台的上底点数组
 * @param  	{vec3[]} 	bottomPts  		圆柱或圆台的下底点数组
 * @return  {Object[]}  planesArray 	返还用于queryNodesInConvexPolyhedron接口的数组
 */
View3D.prototype.coneCvtConvexPolyhedron = function (topPts, bottomPts) {
	// 求交面数组 
	var planeArray = [];
	// 顶面
	planeArray.push(topPts[2]);
	planeArray.push(topPts[1]);
	planeArray.push(topPts[0]);

	//底面
	planeArray.push(bottomPts[0]);
	planeArray.push(bottomPts[1]);
	planeArray.push(bottomPts[2]);

	// 侧面
	var i2;
	for (i2 = 1; i2 &lt; topPts.length; i2++) {
		planeArray.push(topPts[i2]);
		planeArray.push(bottomPts[i2]);
		planeArray.push(topPts[i2 - 1]);
	}
	planeArray.push(topPts[0]);
	planeArray.push(bottomPts[0]);
	planeArray.push(topPts[i2 - 1]);
	
	var planesArray = [];
	for (var i = 0; i &lt; planeArray.length; i = i + 3) {
		var normal = [];
		normal.push(planeArray[i]);
		normal.push(planeArray[i + 1]);
		normal.push(planeArray[i + 2]);

		var plane = {
			points: normal,
			normal: normal
		}
		planesArray.push(plane);
	}
	return planesArray;
};

<span id='View3D-View3D-method-setPictureQuality'>/**
</span> * 设置画质清晰度
 *
 * @param {Object} [option]
 * @param {number} [option.staticQuality = 100]	客户端不进行三维场景漫游操作（拖动，放大等操作）时画质的清晰度(0,100]
 *												值越大画质清晰度越高占用带宽越高
 * @param {number} [option.dynamicQuality = 80] 客户端进行三维场景漫游操作（拖动，放大等操作）时画质的清晰度(0,100]
 *												值越小画质清晰度越低占用带宽越低
 */
View3D.prototype.setPictureQuality = function (option) {
	var defaultOption = {
		staticQuality: 100,
		dynamicQuality: 80
	};
	var newOption = jQuery.extend({}, defaultOption, option);


	Condicio.checkIsNumber(newOption.staticQuality, &quot;The type of staticQuality must be 'Number'!&quot;);
	Condicio.checkIsNumber(newOption.dynamicQuality, &quot;The type of dynamicQuality must be 'Number'!&quot;);
	Condicio.checkArgument(newOption.staticQuality &gt; 0 &amp;&amp; newOption.staticQuality &lt;= 100, &quot;option.staticQuality must Less than or equal to 100 and greater than 0!&quot;);
	Condicio.checkArgument(newOption.dynamicQuality &gt; 0 &amp;&amp; newOption.dynamicQuality &lt;= 100, &quot;option.dynamicQuality must Less than or equal to 100 and greater than 0!&quot;);

	this.session.request(this.objectID, &quot;setPictureQuality&quot;, newOption);
};

<span id='View3D-View3D-method-setPerformanceListener'>/**
</span> * 设置性能监听器
 * 当用户漫游浏览三维区域的时候按照固定频率通知当前传输性能
 * @param {Object}      	performanceListener              			性能监听器
 * @param {Number}      	[performanceListener.interval]				通知的频率（x秒/次），默认为5.0，可以带小数，值要求大于0，推荐不低于1，不高于10
 * @param {Functcion}   	performanceListener.notifier     			性能情况通知回调
 * @param {Object} 			performanceListener.notifier.info 			性能信息
 * @param {Number}			performanceListener.notifier.info.imageTransferDelay 			图片传输延时(单位：ms)
 * 							--图片传输延时低于20ms认为是流畅的
 * 							--图片传输延时低于60ms认为可用
 * 							--图片传输延时大于60ms认为不可用为true，代表当前用户正在漫游浏览三维，反之则否
 */
View3D.prototype.setPerformanceListener = function (performanceListener) {
	Condicio.checkIsObject(performanceListener, &quot;The type of performanceMonitor must be 'Object'!&quot;);
	performanceListener.interval = performanceListener.interval || 5;
	Condicio.checkIsNumber(performanceListener.interval, &quot;The type of interaval must be 'Number'!&quot;);
	Condicio.checkArgument(performanceListener.interval &gt; 0, &quot;Interval must greater than 0!&quot;)
	Condicio.checkIsFunction(performanceListener.notifier, &quot;The type of notifier must be 'Function'!&quot;);

	var notifierWrapper = function (error, result) {
		performanceListener.notifier(result.info);
	};

	if (this.performanceListenerID) {
		this.session.unregisterCallback(this.performanceListenerID);
	}

	var notifierID = Utility.genGUID();
	this.session.registerCallback(notifierID, notifierWrapper, &quot;setPerformanceListener&quot;, true);
	this.performanceListenerID = notifierID;

	var params = {};
	params.performanceListener = {
		notifierID: notifierID,
		interval: performanceListener.interval
	}

	this.session.request(this.objectID, &quot;setPerformanceListener&quot;, params);
};
<span id='View3D-View3D-method-calcNodesArea'>/**
</span> * 计算一组节点的面片面积和
 * 注：只支持对当前加载的模型节点进行计算, 目前不支持对装置根节点计算面积
 * 计算的是所有节点的面片面积和，不是表面积
 * 
 * @param {Node[]}     	nodes 					 			需要计算的一组节点 
 * @param {Function} 	callback                 			计算完成后的回调 
 * @param {Error} 		callback.err		     			返回错误
 *               		err {InvalidArgumentError}       	节点不支持计算面积（如有两个父节点、DrawingRoot节点等）
 * @param {Number}    	callback.area	         			计算结果，单位平方毫米
 *
 */
View3D.prototype.calcNodesArea = function (nodes, callback) {
	ParamValidation.checkIsTypeArray(nodes, Node, &quot;The  type of nodes must be 'Node Array' and valid!&quot;);

	var nodeIDs = CvtForJson.cvtNodeArray(nodes);

	var callbackWrapper = function (error, result) {
		if (error)
			callback(error, null);
		else {
			callback(null, result.area);
		}
	}

	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callbackWrapper, &quot;calcNodesArea&quot;);

	var params = {
		nodeIDs: nodeIDs,
		callbackID: callbackID
	};

	this.session.request(this.objectID, &quot;calcNodesArea&quot;, params);
};

<span id='View3D-View3D-method-registerTrackLongCallback'>/**
</span> * 注册循线状态改变后回调函数
 * @param {Function} 	callback 					获取循线状态改变完成后的回调
 * @param {Error} 		callback.err				获取回调失败返回的错误
 * @param {Number} 		callback.moveState  		获取回调成功返回运动状态（0:无效，1:暂停，2:前进，3:后退）
 * @param {vec3} 		callback.forwardDir         获取回调成功返回前进方向向量
 * @param {vec2} 		callback.windowPoint  	    获取回调成功返回运动参考点窗口坐标
 * @param {vec3} 		callback.movePoint  	    获取回调成功返回运动参考点三维坐标
 * @param {Number} 		callback.speed  	        获取回调成功返回运动速度（mm/帧）
 */
 View3D.prototype.registerTrackLongCallback = function (callback) {
	 Condicio.checkIsFunction(callback, &quot;The type of callback must be 'Function'!&quot;);
	 
	var callbackWrapper = function (error, result) {
		if (!error) {

			var moveState = result.moveState;
			var forwardDir = CvtForJson.cvtToVec3(result.forwardDir);
			var windowPoint= CvtForJson.cvtToVec2(result.windowPoint);	
			var movePoint= CvtForJson.cvtToVec3(result.movePoint);	
			var speed = result.speed;
			callback(null, moveState,forwardDir,windowPoint,movePoint,speed);
		}
		else
			callback(error, null,null,null,null,null);
	}

	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callbackWrapper, &quot;registerTrackLongCallback&quot;,true);

	var params = {
		callbackID: callbackID
	};

	this.session.request(this.objectID, &quot;registerTrackLongCallback&quot;,params);
};

<span id='View3D-View3D-method-setTrackLongState'>/**
</span> * 设置循线状态
 *
 * @param {Object}     	option 					      需要的参数
 * @param {Number} 		option.stateType              设置状态类型（&quot;1&quot;:暂停，&quot;2&quot;:前进，&quot;3&quot;:后退，&quot;4&quot;:设置速度）
 * @param {Number} 	    option.speed                  设置移动速度（当状态类型为&quot;4&quot;时起效，实数[0.0,500.0]，单位 mm/帧）
 */

 View3D.prototype.setTrackLongState = function (option) {
	if (option.stateType === undefined) { option.stateType = 0; }
	if (option.speed === undefined) { option.speed = 10; }
	Condicio.checkIsNumber(option.stateType, &quot;option.stateType must be a number&quot;);
	Condicio.checkArgument(option.stateType &gt;= 1 &amp;&amp; option.stateType &lt;= 4 &amp;&amp; option.stateType % 1 === 0, &quot;option.stateType must be a positive integer with values from 1 to 4&quot;);
	Condicio.checkIsNumber(option.speed, &quot;option.speed must be a number&quot;);
	Condicio.checkArgument(option.speed &gt;= 0 &amp;&amp; option.speed &lt;= 500, &quot;option.speed must in [0.0,500.0]&quot;);

	var params = {
		stateType: option.stateType,
		speed: option.speed,
	};
	this.session.request(this.objectID, &quot;setTrackLongState&quot;, params);
};

<span id='View3D-View3D-method-registerFirstThirdPersonCallback'>/**
</span> * 注册一三人称漫游回调函数
 * @param {Function} 	callback 					获取一三人称漫游的回调
 * @param {Error} 		callback.err				获取回调失败返回的错误
 * @param {boolean} 	callback.isFirstPersonMode  获取回调成功返回是否是第一人称漫游模式
 * @param {Number} 		callback.moveVelocity       获取回调成功返回水平移动速度，单位：毫米/秒
 * @param {boolean} 	callback.canCollision  	    获取回调成功返回是否允许水平碰撞
 * @param {vec3} 		callback.position  	    	获取回调成功返回人物脚下位置，世界坐标系，单位：毫米
 * @param {vec3} 		callback.direction  	    获取回调成功返回视口朝向，世界坐标系
 */
 View3D.prototype.registerFirstThirdPersonCallback = function (callback) {
	Condicio.checkIsFunction(callback, &quot;The type of callback must be 'Function'!&quot;);
	
    var callbackWrapper = function (error, result) {
	    if (!error) {
	 	   var isFirstPersonMode = result.isFirstPersonMode;
	 	   var moveVelocity = result.moveVelocity;
	 	   var canCollision = result.canCollision;
	 	   var position= CvtForJson.cvtToVec3(result.position);	
	 	   var direction= CvtForJson.cvtToVec3(result.direction);	
	 	   callback(null, isFirstPersonMode,moveVelocity,canCollision,position,direction);
	    }
	    else
	 	   callback(error, null,null,null,null,null);
    }
 
    var callbackID = Utility.genGUID();
    this.session.registerCallback(callbackID, callbackWrapper, &quot;registerFirstThirdPersonCallback&quot;,true);
 
    var params = {
	    callbackID: callbackID
    };
 
    this.session.request(this.objectID, &quot;registerFirstThirdPersonCallback&quot;,params);
};

<span id='View3D-View3D-method-setFirstThirdPersonState'>/**
</span>* 设置一三人称漫游状态
*
* @param {Object}     	option 					      	需要的参数
* @param {String} 		[option.mode]                   设置漫游器人称模式：&quot;firstPerson&quot; 第一人称、&quot;thirdPerson&quot; 第三人称
* @param {Number} 	    [option.moveVelocity]           设置水平移动速度，单位：毫米/秒，需要大于零，推荐小于20000毫米/秒，超出可能出现穿模现象
* @param {boolean} 	    [option.canCollision]           设置是否允许水平碰撞
*/

View3D.prototype.setFirstThirdPersonState = function (option) {
	if (!Condicio.isUndefined(option.mode))
	{
		Condicio.checkIsString(option.mode, &quot;option.mode must be a string&quot;);
	}
	if (!Condicio.isUndefined(option.moveVelocity))
	{
		Condicio.checkIsNumber(option.moveVelocity, &quot;option.moveVelocity must be a number&quot;);
    	Condicio.checkArgument(option.moveVelocity &gt; 0, &quot;option.moveVelocity must be more than 0&quot;);
	}
	if (!Condicio.isUndefined(option.canCollision))
	{
		Condicio.checkIsBoolean(option.canCollision, &quot;The type of option.canCollision must be 'Boolean'!&quot;);
		Condicio.checkArgument(option.canCollision == false || option.canCollision == true, &quot;The option.canCollision must false or true&quot;);
	}

    var params = {
    };

	if (!Condicio.isUndefined(option.mode))
		params.mode = option.mode;
	if (!Condicio.isUndefined(option.moveVelocity))
		params.moveVelocity = option.moveVelocity;
	if (!Condicio.isUndefined(option.canCollision))
		params.canCollision = option.canCollision;

    this.session.request(this.objectID, &quot;setFirstThirdPersonState&quot;, params);
};

module.exports = View3D;
</pre>
</body>
</html>
