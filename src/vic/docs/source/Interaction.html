<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">&quot;use strict&quot;;

var _ = require(&quot;lodash&quot;);

<span id='View3D-Interaction'>/**
</span> * @class View3D.Interaction
 * 交互基接口
 *
 * 应用不应该构建，应该通过InteractionFactory.createXXInteraction创建得到
 * @abstract
 */
function Interaction(objectID) {
	this.objectID = objectID;
};

<span id='View3D-Interaction-method-on'>/**
</span> * @hide
 * 开启交互
 *
 * @param {Session} session	连接实例
 */
Interaction.prototype.on = function(session){ };

<span id='View3D-Interaction-method-off'>/**
</span> * @hide
 * 结束交互
 *
 * @param {Session} session	连接实例
 */
Interaction.prototype.off = function(session){ };

module.exports = Interaction;</pre>
</body>
</html>
