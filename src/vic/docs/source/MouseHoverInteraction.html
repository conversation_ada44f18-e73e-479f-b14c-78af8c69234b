<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">
&quot;use strict&quot;;

var _ = require(&quot;lodash&quot;);

var Interaction = require(&quot;./Interaction.js&quot;);
	

<span id='MouseHoverInteraction'>/**
</span> * @ignore
 * @class
 * 鼠标悬停交互
 */
function MouseHoverInteraction(objectID, hoverHandlerID, hoverHandler) {
	this.objectID = objectID;
	this.hoverHandler = hoverHandler;
	this.hoverHandlerID = hoverHandlerID;
};

MouseHoverInteraction.prototype = _.create(Interaction.prototype, {
    constructor: MouseHoverInteraction
});

MouseHoverInteraction.prototype.on = function (session) {
	session.registerCallback(this.hoverHandlerID, this.hoverHandler, &quot;MouseHoverInteraction&quot;, true);
};

MouseHoverInteraction.prototype.off = function (session) {
	session.unregisterCallback(this.hoverHandlerID);
};
	

module.exports = MouseHoverInteraction;
</pre>
</body>
</html>
