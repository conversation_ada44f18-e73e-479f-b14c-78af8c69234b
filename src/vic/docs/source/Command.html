<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">&quot;use strict&quot;;


<span id='View3D-Command'>/**
</span> * @class View3D.Command
 * 三维Command命令
 * 
 * 不应该由应用直接创建，应该由{@link NodeFactory}或者{@link EffectFactory}等自己构建自己需要的Command
 */
function Command(objectID){
	this.objectID = objectID;
};


module.exports = Command;</pre>
</body>
</html>
