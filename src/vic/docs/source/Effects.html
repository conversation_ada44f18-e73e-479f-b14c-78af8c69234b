<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">&quot;use strict&quot;;

var _ = require(&quot;lodash&quot;),
	Condicio = require(&quot;condicio&quot;),
	Effect = require(&quot;./Effect.js&quot;);

<span id='View3D-ClipEffect'>/**
</span> * @class View3D.ClipEffect
 * @extend  View3D.Effect
 * 剖切效果
 *
*/
function ClipEffect(objectID, pickHandlerID, pickHandler) {
	this.objectID = objectID;
	this.pickHandler = pickHandler;
	this.pickHandlerID = pickHandlerID;
};

ClipEffect.prototype = _.create(Effect.prototype, {
	constructor: ClipEffect
});

ClipEffect.prototype.on = function(session){
	session.registerCallback(this.pickHandlerID, this.pickHandler, &quot;&quot;, true);
};

ClipEffect.prototype.off = function(session){
	session.unregisterCallback(this.pickHandlerID);
};

<span id='View3D-DraggableEffect'>/**
</span> * @class View3D.DraggableEffect
 * @extend View3D.Effect
 * 拖拽移动效果
 *
 */
function DraggableEffect(objectID, dragger) {
	this.objectID = objectID;
	this.dragger = dragger;
};

DraggableEffect.prototype = _.create(Effect.prototype, {
	constructor: DraggableEffect
});

DraggableEffect.prototype.on = function(session){
	this.dragger.on(session);
};

DraggableEffect.prototype.off = function(session){
	this.dragger.off(session);
};	

exports.ClipEffect = ClipEffect;
exports.DraggableEffect = DraggableEffect;</pre>
</body>
</html>
