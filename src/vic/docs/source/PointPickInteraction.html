<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">
&quot;use strict&quot;;

var _ = require(&quot;lodash&quot;);

var Interaction = require(&quot;./Interaction.js&quot;);
	

<span id='PointPickInteraction'>/**
</span> * @ignore
 * @class PointPickInteraction
 * 点选交互
 */
function PointPickInteraction(objectID, pickHandlerID, pickHanlder) {
	this.objectID = objectID;
	this.pickHanlder = pickHanlder;
	this.pickHandlerID = pickHandlerID;
};

PointPickInteraction.prototype = _.create(Interaction.prototype, {
	constructor: PointPickInteraction
});

PointPickInteraction.prototype.on = function(session){
	session.registerCallback(this.pickHandlerID, this.pickHanlder,&quot;PointPickInteraction&quot;, true);
};

PointPickInteraction.prototype.off = function(session){
	session.unregisterCallback(this.pickHandlerID);
};
	

module.exports = PointPickInteraction;
</pre>
</body>
</html>
