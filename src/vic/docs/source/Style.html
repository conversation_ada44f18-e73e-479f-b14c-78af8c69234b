<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">var COLOR = require(&quot;color&quot;);
var Matrix = require(&quot;gl-matrix-double&quot;);
	
var black = COLOR('rgb(0, 0, 0)').alpha(1);
var white = COLOR('rgb(255, 255, 255)');
var yellow = COLOR('rgb(255, 255, 0)');
var red = COLOR('rgb(255, 0, 0)');

<span id='View3D-Style'>/**
</span> * @class View3D.Style
 * 标准属性定义
 */
var Style = { };
 
<span id='View3D-Style-LineStyle'>/**
</span> * @class View3D.Style.LineStyle
 * 标准线属性定义 
 */
Style.LineStyle = {
<span id='View3D-Style-LineStyle-property-color'>	/**
</span>	 * @property {Color}  [color=black] 	线色，颜色中的alpha值无效
	 */
	color: black,
<span id='View3D-Style-LineStyle-property-width'>	/**
</span>	 * @property {Number} [width=2] 		线宽（像素）范围（1 ~ 10）
	 */
	width: 2,
<span id='View3D-Style-LineStyle-property-type'>    /**
</span>	 * @property {String} [type=&quot;solid&quot;]	线型
	 *
	 *	- &quot;solid&quot; 	实线
	 *  - &quot;stipple&quot;	虚线
	 */
	type: 'solid'
};

<span id='View3D-Style-BorderStyle'>/**
</span> * @class View3D.Style.BorderStyle
 * 标准边框属性定义 
 */
Style.BorderStyle = {
<span id='View3D-Style-BorderStyle-property-color'>	/**
</span>	 * @property {Color}  [color=black] 	边框色，颜色中的alpha值有效
	 */
	color: black,
<span id='View3D-Style-BorderStyle-property-width'>	/**
</span>	 * @property {Number} [width=3] 		边框宽（像素）范围（1 ~ 10）
	 */
	width: 3,
};

<span id='View3D-Style-TextStyle'>/**
</span> * @class View3D.Style.TextStyle
 * 标准文本属性定义 
 */
Style.TextStyle = {
<span id='View3D-Style-TextStyle-property-color'>	/**
</span>	 * @property {Color}  [color=black] 		字体色，颜色中的alpha值无效
	 */
	color: black,
<span id='View3D-Style-TextStyle-property-size'>	/**
</span>	 * @property {Number} [size=15] 			字体大小（像素）,定义的是文字高度，宽度自动调整	
	 */
	size: 15,
<span id='View3D-Style-TextStyle-property-alignment'>    /**
</span>	 * @property {String} [alignment=&quot;left&quot;]	多行文本之间对齐方式
	 *
	 *	- &quot;left&quot; 	左对齐
	 *  - &quot;center&quot;	居中对齐
	 *  - &quot;right&quot;   右对齐
	 */
	alignment: 'left',
<span id='View3D-Style-TextStyle-property-fontName'>	/**
</span>	 * @property {String} [fontName=&quot;&quot;]			字体名，设置为空，默认为楷体：simkai（该名与当前操作系统（c:\windows\fonts）下的字体名对应，若不满足时，可以安装新的字体）
	 */
	fontName: &quot;&quot;,
<span id='View3D-Style-TextStyle-property-backdropType'>	/**
</span>	 * @property {Number} [backdropType]		字体的阴影效果，取值范围[0,8]（&lt;a href=&quot;https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/75957780/VIC+Style&quot;&gt;具体效果详见&lt;/a&gt;）
	 */
	backdropType: -1,
<span id='View3D-Style-TextStyle-property-backdropColor'>	/**
</span>	 * @property {Color}  [backdropColor=black]	阴影效果的颜色，颜色中的alpha值无效
	 */
	backdropColor: black,
<span id='View3D-Style-TextStyle-property-lineSpacing'>	/**
</span>	 * @property {Number}  [lineSpacing=0]	行间距，以字符高度的百分比表示；建议值为25％（即，将行距设置为0.25）
	 */
	lineSpacing: 0
};

<span id='View3D-Style-TextFrameStyle'>/**
</span> * @class View3D.Style.TextFrameStyle
 * 标准文本框属性定义 
 */
Style.TextFrameStyle = {
<span id='View3D-Style-TextFrameStyle-property-shape'>	/**
</span>	 * @property {String}  		[shape=&quot;rectangle&quot;] 	文本框外形
	 *
	 * rectangle	矩形框
	 * circle 		圆形框
	 */
	shape: &quot;rectangle&quot;,
<span id='View3D-Style-TextFrameStyle-property-fillColor'>	/**
</span>	 * @property {Color}  		[fillColor=white] 	填充色，颜色中的alpha值有效
	 */
	fillColor: white,
<span id='View3D-Style-TextFrameStyle-property-padding'>	/**
</span>	 * @property {Number} 		[padding=2] 	文字离边框距离（像素）
	 */
	padding: 2,
<span id='View3D-Style-TextFrameStyle-property-border'>    /**
</span>	 * @property {Object} 		[border]		边框属性，参照View3D.Style.BorderStyle定义
	 */
	border: Style.BorderStyle,
<span id='View3D-Style-TextFrameStyle-property-text'>    /**
</span>	 * @property {Object} 		[text]			文本属性，参照View3D.Style.TextStyle定义
	 */
	text: Style.TextStyle,
};

<span id='View3D-Style-IdentifyLocationPoint'>/**
</span> * @class View3D.Style.IdentifyLocationPoint
 * 标准起始点属性定义 
 */
Style.IdentifyLocationPoint = {
<span id='View3D-Style-IdentifyLocationPoint-property-color'>	/**
</span>	 * @property {Color}  [color=black] 	点颜色，颜色中的alpha值无效
	 */
	color: black,
<span id='View3D-Style-IdentifyLocationPoint-property-radius'>	/**
</span>	 * @property {Number} [ radius=0] 		点的半径
	 */
	radius: 0,
};

<span id='View3D-Style-PointerStyle'>/**
</span> * @class View3D.Style.PointerStyle
 * 线及相关点属性定义 
 */
Style.PointerStyle = {
<span id='View3D-Style-PointerStyle-property-line'>	/**
</span>	 * @property {Object}  	[line] 						指向线属性，参照View3D.Style.LineStyle的定义
	 */
	line: Style.LineStyle,
<span id='View3D-Style-PointerStyle-property-identifyLocationPoint'>	/**
</span>	 * @property {Object}  	[identifyLocationPoint] 		位置标识点属性，参照View3D.Style.IdentifyLocationPoint的定义
	 */
	identifyLocationPoint: Style.IdentifyLocationPoint,
};

<span id='View3D-Style-TextLabelStyle'>/**
</span> * @class View3D.Style.TextLabelStyle
 * 标准文本标签属性定义 
 */
Style.TextLabelStyle = {
<span id='View3D-Style-TextLabelStyle-property-pointer'>	/**
</span>	 * @property {Object}  	[pointer] 						指向线及点的属性，参照View3D.Style.PointerStyle的定义
	 */
	pointer: Style.PointerStyle,
<span id='View3D-Style-TextLabelStyle-property-textFrame'>	/**
</span>	 * @property {Object}	[textFrame] 					文本框属性，参照View3D.Style.TextFrameStyle的定义
	 */
	textFrame: Style.TextFrameStyle,
<span id='View3D-Style-TextLabelStyle-property-connectPosition'>    /**
</span>	 * @property {Object} 	[connectPosition]				线与边框连接位置
	 * @property {String} 	[connectPosition.x=&quot;center&quot;]	线与边框连接位置
	 *
	 * - &quot;left&quot; 	左
	 * - &quot;center&quot; 	中
	 * - &quot;right&quot;   	右
	 * @property {String} 	[connectPosition.y=&quot;bottom&quot;]	垂直连接位置
	 *
	 * - &quot;bottom&quot; 	下
	 * - &quot;middle&quot; 	中
	 * - &quot;top&quot;   	上
	 */
	connectPosition: {
		x: &quot;center&quot;,
		y: &quot;bottom&quot;
	},
<span id='View3D-Style-TextLabelStyle-property-fixSizeRange'>	/** @property {Object} 	[fixSizeRange]					文本标签固定大小的显示范围，没有量纲，max应该大于min
</span>	 *  文本标签在该范围内能够维持固定大小，否则不维持屏幕占比
	 * 
	 *	例如：fixSizeRange{0, 10}，即文本标签固定大小的显示范围为0-10
	 *
	 *	@property {Number} 	[fixSizeRange.min = 0]			最小值（取值范围[0,+∞)），取0代表文本会随着视点距离的靠近会无限缩小，一直维持在屏幕显示的大小不变
	 *  @property {Number} 	[fixSizeRange.max = 3.4E38]		最大值（取值范围(0,+∞)），默认值表示最大放大倍数为3.4E38倍，超过该倍数无法维持占屏幕大小不变
	 * 
	 */
	fixSizeRange:{											
		min: 0,
		max: 3.402823466 * Math.pow(10,38)
	},
<span id='View3D-Style-TextLabelStyle-property-enableOvershadow'>	/**
</span>	 *	@property {Boolean}	[enableOvershadow = false]				文本标签是否需要一直显示不会被遮挡
	 *	如果该值设置为false，那么文本标注会在所有模型之前显示，永远不会遮挡
	 *  如果该值设置为true，从看得视角来说该文本标注会被在它之前的模型挡住，转动视角该看见的时候也会看见，且标注是一直面向屏幕的
	 */
	enableOvershadow: false
};

<span id='View3D-Style-PictureLabelStyle'>/**
</span> * @class View3D.Style.PictureLabelStyle
 * 标准图片标签属性定义 
 */
 Style.PictureLabelStyle = {
<span id='View3D-Style-PictureLabelStyle-property-fixSizeRange'>	/** @property {Object} 	[fixSizeRange]					图像标签固定大小的显示范围，没有量纲，max应该大于min
</span>	 *  图片在该范围内能够维持固定大小，否则不维持屏幕占比
	 *  
	 *	例如：fixSizeRange{0, 10}，即图像标签固定大小的显示范围为0-10
	 *  
	 *	@property {Number} 	[fixSizeRange.min = 0]			最小值（取值范围[0,+∞)），取0代表图片会随着视点距离的靠近会无限缩小，一直维持在屏幕显示的大小不变
	 *  @property {Number} 	[fixSizeRange.max = 40.0]		最大值（取值范围(0,+∞)），默认值表示最大放大倍数为40倍，超过该倍数无法维持占屏幕大小不变 
	 * 
	 */
	fixSizeRange:{											
		min: 0,
		max: 40.0
	},
<span id='View3D-Style-PictureLabelStyle-property-width'>	/**
</span>	 *  @property {Number} [width = 48]		图像标签的宽度,高度大小会根据图像高宽比自动调整
	*/
	width: 48
};

<span id='View3D-Style-PointStyle'>/**
</span> * @class View3D.Style.PointStyle
 * 标准点属性定义 
 */
Style.PointStyle = {
<span id='View3D-Style-PointStyle-property-color'>	/**
</span>	 * @property {Color}  [color=black] 	点颜色，颜色中的alpha值无效
	 */
	color: black,
<span id='View3D-Style-PointStyle-property-size'>	/**
</span>	 * @property {Number} [size=4] 		    点大小（像素）范围（大于0）
	 */
	size: 4,
};



<span id='View3D-Style-LightStyle'>/**
</span> * @class View3D.Style.LightStyle
 * 光属性定义  * &lt;a href=&quot;https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/75956914&quot;&gt;参数相关详情请参考&lt;/a&gt;
 */
 Style.LightStyle = {	 
<span id='View3D-Style-LightStyle-property-ambient'>	/**
</span>	 * @property {Color}  [ambient=black] 	  					环境光颜色
	 */
	ambient: black,
<span id='View3D-Style-LightStyle-property-diffuse'>	/** 
</span>	 * @property {Color}  [diffuse=black] 	  					镜面光颜色
	 */
    diffuse: black,
<span id='View3D-Style-LightStyle-property-specular'>	/**
</span>	 * @property {Color}  [specular=black] 	 					漫射光颜色
	 */
	specular: black,
<span id='View3D-Style-LightStyle-property-spotCutoff'>	/**
</span>	 * @property {Number} [spotCutoff=180]      				聚光灯聚光角度
	 */
	spotCutoff: 180,
<span id='View3D-Style-LightStyle-property-position'>	/**
</span>	 * @property {vec4}   [position]							光源位置（vec4的最后一位决定使用的是哪种光源）
	 */
	position: Matrix.vec4.fromValues(0,0,1,0),
<span id='View3D-Style-LightStyle-property-direction'>	/**	
</span>	 * @property {vec3}   [direction]							光照方向
	 */	
	direction: Matrix.vec3.fromValues(0,0,-1),
<span id='View3D-Style-LightStyle-property-range'>	/**
</span>	 * @property {Number}   [range]								光照范围
	 */	
	range: 0,
	

 };

<span id='View3D-Style-AxisStyle'> /**
</span> * @class View3D.Style.AxisStyle
 * 轴属性定义 
 */
Style.AxisStyle = {
<span id='View3D-Style-AxisStyle-property-length'>	/**
</span>	 * @property {Number} [length=150]				长（像素）
	 */
	length: 150,
<span id='View3D-Style-AxisStyle-property-width'>	/**
</span>	 * @property {Number} [width=3] 				轴宽（像素）
	 */
	width: 3,
<span id='View3D-Style-AxisStyle-property-color'>	/**
</span>	 * @property {Color}  [color=red] 				颜色，颜色中的alpha值无效
	 */
	color: red,
<span id='View3D-Style-AxisStyle-property-pickedRadius'>    /**
</span>	 * @property {Number} [pickedRadius=6]			轴可被选中半径（像素）
	 */
	pickedRadius: 6,
<span id='View3D-Style-AxisStyle-property-pickedColor'>	/**
</span>	 * @property {Color}  [pickedColor=yellow]		轴被选中后的颜色，颜色中的alpha值无效
	 *
	 */
	pickedColor: yellow,
};

module.exports = Style;</pre>
</body>
</html>
