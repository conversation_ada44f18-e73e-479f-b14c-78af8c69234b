<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">&quot;use strict&quot;;

var _ = require(&quot;lodash&quot;),
    Condicio = require(&quot;condicio&quot;),
    Node = require(&quot;./Node.js&quot;),
    Utility = require(&quot;../Utility.js&quot;),
    CvtForJson = require(&quot;../CvtForJson.js&quot;),
    ParamValidation = require(&quot;../ParamValidation.js&quot;),
    Command = require(&quot;./Command.js&quot;);

<span id='View3D-DrawingRootNode'>/**
</span> * @class View3D.DrawingRootNode
 * Drawing文件对应模型根节点
 *
 * 不应该直接创建，应该由{@link NodeFactory}创建出来
 */
function DrawingRootNode(session, objectID, name) {
    this.objectID = objectID;
    this.session = session;
    this.name = name;
};

DrawingRootNode.prototype = _.create(Node.prototype, {
    constructor: DrawingRootNode
});

<span id='View3D-DrawingRootNode-method-createDetachCommand'>/**
</span> * 创建分离Command，将子节点从父节点上分离&lt;br/&gt;
 * 
 * 注：返回的节点支持放置在Drawing之外使用&lt;br/&gt;
 * 注：通过findDescendNodes或者点选获取的Drawing下的节点仅限于放在Drawing之下使用&lt;br/&gt;
 * 注：&lt;b&gt;如果是OGF文件，该接口必须和transformNodes接口一起使用&lt;/b&gt;&lt;br/&gt;
 * 
 * @param {Node[]}     	nodes 			需要分离的节点
 * @param {Function} 	callback		返回分离结果
 * @param {Error}       callback.error 	返回错误
 * @param {Node[]}		callback.nodes  返回分离后的节点
 *
 * @return {View3D.Command}
 */
DrawingRootNode.prototype.createDetachCommand = function(nodes, callback) {
    ParamValidation.checkIsTypeArray(nodes, Node, &quot;The  type of nodes must be 'Node Array' and valid!&quot;);
    Condicio.checkIsFunction(callback, &quot;The type of callback must be 'Function'!&quot;);

    var callbackWrapper = function(error, result) {
        if (!error) {
            var nodes = result.nodes;

            var tempNodes = [];
            for (var i = 0; i &lt; nodes.length; ++i) {
                if (nodes[i].nodeID !== &quot;&quot;)
                    tempNodes.push(new Node(this.session, nodes[i].nodeID, nodes[i].name));
                else
                    tempNodes.push(null); // 没找到给一个空
            }

            callback(error, tempNodes);
		}else {
            callback(error, null);
        }
    };

    var commandID = Utility.genGUID();
    var callbackID = Utility.genGUID();
    this.session.registerCallback(callbackID, callbackWrapper);

    var params = {
        commandID: commandID,
        nodeIDs: CvtForJson.cvtNodeArray(nodes),
        callbackID: callbackID
    };
    this.session.request(this.objectID, &quot;createDetachCommand&quot;, params);

    return new Command(commandID);
};


<span id='View3D-DrawingRootNode-method-createDetachToCommand'>/**
</span> * 创建分离Command，将子节点从父节点上分离&lt;br/&gt;
 * 
 * 注：返回的节点支持放置在Drawing之外使用&lt;br/&gt;
 * 注：通过findDescendNodes或者点选获取的Drawing下的节点仅限于放在Drawing之下使用&lt;br/&gt;
 * 注：&lt;b&gt;如果是OGF文件，该接口必须和transformNodes接口一起使用&lt;/b&gt;&lt;br/&gt;
 * 注：&lt;b&gt;该接口会把新的节点作为parentsNode的孩子，目的是防止之后再添加回场景中时发生闪烁&lt;/b&gt; &lt;br/&gt;
 * 
 * @param {object} 		options					需要分离的节点和分离后节点的父节点
 * @param {Node[]}     	options.nodes 			需要分离的节点
 * @param {Node[]}     	[options.parentNodes] 	分离后节点的父节点
 * @param {Function} 	callback				返回分离结果
 * @param {Error}       callback.error 			返回错误
 * @param {Node[]}		callback.nodes  		返回分离后的节点
 *
 * @return {View3D.Command}
 */
DrawingRootNode.prototype.createDetachToCommand = function(options, callback) {
    ParamValidation.checkIsTypeArray(options.nodes, Node, &quot;The  type of nodes must be 'Node Array' and valid!&quot;);
    Condicio.checkIsFunction(callback, &quot;The type of callback must be 'Function'!&quot;);

    var callbackWrapper = function(error, result) {
        if (!error) {
            var nodes = result.nodes;

            var tempNodes = [];
            for (var i = 0; i &lt; nodes.length; ++i) {
                if (nodes[i].nodeID !== &quot;&quot;)
                    tempNodes.push(new Node(this.session, nodes[i].nodeID, nodes[i].name));
                else
                    tempNodes.push(null); // 没找到给一个空
            }

            callback(error, tempNodes);
		} else {
            callback(error, null);
        }
    };

    var commandID = Utility.genGUID();
    var callbackID = Utility.genGUID();
    this.session.registerCallback(callbackID, callbackWrapper);

    var params = {
        commandID: commandID,
        nodeIDs: CvtForJson.cvtNodeArray(options.nodes),
        callbackID: callbackID
    };

    if (options.parentNodes !== undefined) {
        ParamValidation.checkIsTypeArray(options.parentNodes, Node, &quot;The  type of nodes must be 'Node Array' and valid!&quot;);
        params.parentNodeIDs = CvtForJson.cvtNodeArray(options.parentNodes);
	}else {
        params.parentNodeIDs = [];
    }

    this.session.request(this.objectID, &quot;createDetachCommand&quot;, params);

    return new Command(commandID);
};

<span id='View3D-DrawingRootNode-method-createUndetachCommand'>/**
</span> * 创建分离恢复Command，将分离后的子节点重新加入到父节点下
 * 
 * @param {Node[]}     	nodes 			已从Drawing下分离的节点
 * @param {Function} 	callback		返回恢复结果
 * @param {Error}       callback.error 	返回错误
 *
 * @return {View3D.Command}
 */
DrawingRootNode.prototype.createUndetachCommand = function(nodes, callback) {
    ParamValidation.checkIsTypeArray(nodes, Node, &quot;The  type of nodes must be 'Node Array' and valid!&quot;);

    var callbackWrapper = function(error, result) {
        callback(error);
    }

    var commandID = Utility.genGUID();
    var callbackID = Utility.genGUID();
    this.session.registerCallback(callbackID, callbackWrapper);

    var params = {
        commandID: commandID,
        nodeIDs: CvtForJson.cvtNodeArray(nodes),
        callbackID: callbackID
    };
    this.session.request(this.objectID, &quot;createUndetachCommand&quot;, params);

    return new Command(commandID);
};

module.exports = DrawingRootNode;</pre>
</body>
</html>
