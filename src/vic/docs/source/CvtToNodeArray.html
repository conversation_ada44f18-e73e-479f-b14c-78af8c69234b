<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">&quot;use strict&quot;;

var Node = require(&quot;./Node.js&quot;);

<span id='global-method-cvtToNodeArray'>/**
</span> * @ignore
 * 用于Node类型在Json.Stringify之前的一些转化
 */
var cvtToNodeArray = function (nodePath, session) {
	var nodeArray = [];
	for (var j = 0; j &lt; nodePath.length; ++j) {
		nodeArray.push(new Node(session, nodePath[j].nodeID, nodePath[j].name));
	}
	return nodeArray;
};

module.exports = cvtToNodeArray;</pre>
</body>
</html>
