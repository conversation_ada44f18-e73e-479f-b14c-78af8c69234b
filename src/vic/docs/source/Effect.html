<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">&quot;use strict&quot;;

var _ = require(&quot;lodash&quot;);

<span id='View3D-Effect'>/**
</span> * @class View3D.Effect
 * 三维效果
 */
function Effect(objectID) {
    this.objectID = objectID;
};

Effect.prototype = _.create(Effect.prototype, {
	constructor: Effect
});

<span id='View3D-Effect-method-on'>/**
</span> * @hide
 * 开启效果
 *
 * @param {Session} session	连接实例
 */
Effect.prototype.on = function(session){ };

<span id='View3D-Effect-method-off'>/**
</span> * @hide
 * 结束效果
 *
 * @param {Session} session	连接实例
 */
Effect.prototype.off = function(session){ };

module.exports = Effect;</pre>
</body>
</html>
