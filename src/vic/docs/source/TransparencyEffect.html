<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">&quot;use strict&quot;;

var _ = require(&quot;lodash&quot;),
	Condicio = require(&quot;condicio&quot;),
	Effect = require(&quot;./Effect.js&quot;),
	Command = require(&quot;./Command.js&quot;),
	Utility = require(&quot;../Utility.js&quot;);

<span id='View3D-TransparencyEffect'>/**
</span> * @class View3D.TransparencyEffect
 * @extend View3D.Effect
 * 透明效果
 *
 * 创建修改透明度的命令
 */
function TransparencyEffect(session, objectID) {
	this.session = session;
    this.objectID = objectID;
};

TransparencyEffect.prototype = _.create(Effect.prototype, {
	constructor: TransparencyEffect
});

<span id='View3D-TransparencyEffect-method-createChangeTransparencyCommand'>/**
</span>* 创建改变透明度的命令
* 注意：6.0.0起修正了透明度逻辑，修正后透明度参数（取值范围0~1）值越大，越透明
* @param {Number} value  设置透明度参数（取值范围0~1）0为不透明，1为全透明(值：0 &lt;= value &lt;= 1）
 *
 * @return {View3D.Command}
 */
TransparencyEffect.prototype.createChangeTransparencyCommand = function(value){
	Condicio.checkIsNumber(value, &quot;The type of value must be 'number'!&quot;);
    Condicio.checkArgument(value &gt;= 0 &amp;&amp; value &lt;= 1, &quot;The Value must: &gt;= 0 &amp;&amp; &lt;= 1 !&quot;);
	
    var commandID = Utility.genGUID();
	var params = {
		commandID: commandID,
		value: value
	};
    this.session.request(this.objectID, &quot;createChangeTransparencyCommand&quot;, params);
	
	return new Command(commandID);
	
};

module.exports = TransparencyEffect;</pre>
</body>
</html>
