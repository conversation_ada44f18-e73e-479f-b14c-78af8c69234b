<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">
&quot;use strict&quot;;

var _ = require(&quot;lodash&quot;);

var Interaction = require(&quot;./Interaction.js&quot;);
	

<span id='CompositeInteraction'>/**
</span> * @ignore
 * @class
 * 复合交互
 */
function CompositeInteraction(objectID, interactions) {
	this.objectID = objectID;
	this.subInteractions = interactions;
};

CompositeInteraction.prototype = _.create(Interaction.prototype, {
    constructor: CompositeInteraction
});

CompositeInteraction.prototype.on = function (session) {
    for (var i = 0; i &lt; this.subInteractions.length; ++i) {
        this.subInteractions[i].on(session);
    }
};

CompositeInteraction.prototype.off = function (session) {
    for (var i = 0; i &lt; this.subInteractions.length; ++i) {
        this.subInteractions[i].off(session);
    }
};
	

module.exports = CompositeInteraction;
</pre>
</body>
</html>
