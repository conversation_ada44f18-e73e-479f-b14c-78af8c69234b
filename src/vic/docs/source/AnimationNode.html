<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">&quot;use strict&quot;;

var _ = require(&quot;lodash&quot;),
	Condicio = require(&quot;condicio&quot;),
	Node = require(&quot;./Node.js&quot;),
	Command = require(&quot;./Command.js&quot;),
	Utility = require(&quot;../Utility.js&quot;),
	jQuery = require(&quot;jquery&quot;);

<span id='View3D-AnimationNode'>/**
</span> * @class View3D.AnimationNode
 * @extend Node
 * 动画节点
 *
 */
function AnimationNode(session, objectID) {
	this.session = session;
    this.objectID = objectID;
};

AnimationNode.prototype = _.create(Node.prototype, {
	constructor: AnimationNode
});

<span id='View3D-AnimationNode-method-createPlayAnimationCommand'>/**
</span> * 创建播放动画命令
 *
 * @param {String} animationName  				需要播放的动画名称
 * @param {Object} [playOption]  				播放选项
 * @param {Number} [playOption.priority = 0]  	播放选项，优先级（取值范围0~7）
 * @param {Number} [playOption.weight = 1.0]  			播放选项，动画重量感（取值范围&gt;0）
 *
 * @return {View3D.Command}
 */
AnimationNode.prototype.createPlayAnimationCommand = function(animationName, playOption){

	var defaultOption = {
		priority: 0,
		weight: 1.0
	};
	var newOption = jQuery.extend(true, {}, defaultOption, playOption);
	
	Condicio.checkIsString(animationName, &quot;The type of animationName must be 'String'!&quot;);
	Condicio.checkIsObject(newOption, &quot;The type of playOption must be 'Object'!&quot;);
	Condicio.checkIsNumber(newOption.priority, &quot;The type of playOption-priority must be 'Number'!&quot;);
    Condicio.checkArgument(newOption.priority &gt;= 0 &amp;&amp; newOption.priority &lt;= 7, &quot;playOption-priority must: &gt;= 0 &amp;&amp; &lt;= 7 !&quot;);
	Condicio.checkIsNumber(newOption.weight, &quot;The type of playOption-weight must be 'Number'!&quot;);
	Condicio.checkArgument(newOption.weight &gt; 0, &quot;playOption-weight must: &gt; 0!&quot;);
	
    var commandID = Utility.genGUID();
	var params = {
		commandID: commandID,
		animationName: animationName,
		priority: newOption.priority,
		weight: newOption.weight
	};
    this.session.request(this.objectID, &quot;createPlayAnimationCommand&quot;, params);
	
	return new Command(commandID);
};

<span id='View3D-AnimationNode-method-createStopAnimationCommand'>/**
</span> * 创建停止动画命令
 *
 * @param {String} animationName  			需要停止的动画名称
 *
 * @return {View3D.Command}
 */
AnimationNode.prototype.createStopAnimationCommand = function(animationName){
	Condicio.checkIsString(animationName, &quot;The type of animationName must be 'String'!&quot;);
	
    var commandID = Utility.genGUID();
	var params = {
		commandID: commandID,
		animationName: animationName
	};
    this.session.request(this.objectID, &quot;createStopAnimationCommand&quot;, params);
	
	return new Command(commandID);
};

<span id='View3D-AnimationNode-method-createSetPlayModeCommand'>/**
</span> * 创建设置动画播放模式命令
 *
 * @param {String} animationName  			需要修改播放模式的动画名称
 * @param {String} playMode  				动画播放模式
 * - &quot;once&quot; 	播放一次，完成之后恢复原状
 * - &quot;stay&quot; 	播放一次，完成之后保持最后的状态
 * - &quot;loop&quot;   	循环播放
 * - &quot;ppong&quot;   	正反依次循环播放
 *
 * @return {View3D.Command}
 */
AnimationNode.prototype.createSetPlayModeCommand = function(animationName, playMode){
	Condicio.checkIsString(animationName, &quot;The type of animationName must be 'String'!&quot;);
	Condicio.checkIsString(playMode, &quot;The type of playMode must be 'String'!&quot;);
	
    var commandID = Utility.genGUID();
	var params = {
		commandID: commandID,
		animationName: animationName,
		playMode: playMode
	};
    this.session.request(this.objectID, &quot;createSetPlayModeCommand&quot;, params);
	
	return new Command(commandID);
};

module.exports = AnimationNode;</pre>
</body>
</html>
