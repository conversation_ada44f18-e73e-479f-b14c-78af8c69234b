<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">
&quot;use strict&quot;;

var _ = require(&quot;lodash&quot;);

<span id='View3D-Node'>/**
</span> * @class View3D.Node
 * 节点操作
 *
 * 不应该直接创建，应该由{@link NodeFactory}创建出来
 */
function Node(session, objectID, name) {
    this.objectID = objectID;
    this.session = session;
    this.name = name;
};


<span id='View3D-Node-method-getName'>/**
</span> * 获取节点名字
 *
 * @return {String} 
 */
Node.prototype.getName = function () {
    return this.name;
};

module.exports = Node;
</pre>
</body>
</html>
