<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">&quot;use strict&quot;;

var ImageDisplay = require(&quot;../ImageDisplay.js&quot;),
	Color = require(&quot;color&quot;),
	Condicio = require(&quot;condicio&quot;),
	ParamValidation = require(&quot;../ParamValidation.js&quot;),
	Matrix = require(&quot;gl-matrix-double&quot;),
	Utility = require(&quot;../Utility.js&quot;),
	Effect = require(&quot;./Effect.js&quot;),
	jQuery = require(&quot;jquery&quot;),
	CvtForJson = require(&quot;../CvtForJson.js&quot;),
	Style = require(&quot;./Style.js&quot;),
	_ = require(&quot;lodash&quot;);
	
<span id='ViewPID-ViewPID'>/**
</span> * @class ViewPID.ViewPID
 * PID渲染场景
 * 
 * 提供加载图纸，显示及对图纸操作的功能
 *
 * 不应该直接创建，应该通过{@link Factory}提供的createViewPID得到
 */
function ViewPID(session, elem, objectID, enableSSL, proxyUri) {
	var _this = this;

    this.session = session;
    this.objectID = objectID;
	this.pickHandlerID = null;
	this.hoverHandlerID = null;
	this.isLoadFile = false;
	
    this.display = new ImageDisplay(session, objectID, elem, enableSSL, proxyUri);
};

ViewPID.prototype = _.create(ViewPID.prototype, {
	constructor: ViewPID
});

ViewPID.prototype.release = function(){
	if(this.pickHandlerID)
	{
		this.session.unregisterCallback(this.pickHandlerID);
	}
	
	if(this.hoverHandlerID)
	{
		this.session.unregisterCallback(this.hoverHandlerID);
	}
	
	this.isLoadFile = false;
	this.display.release();
};

<span id='ViewPID-ViewPID-method-loadFile'>/**
</span> * 加载dwg图纸
 *
 * @param {String} 		url 			所加载图纸的地址，支持&quot;http://...../x.dwg&quot;和&quot;file:///.../x.dwg&quot;格式
 * @param {Function} 	callback		加载图纸回调
 * @param {Error}		callback.err	加载失败，指示错误信息(system_error、NetworkError、DataError)，加载成功返回空
 */
ViewPID.prototype.loadFile = function(url, callback) {
	Condicio.checkIsString(url, &quot;The type of url must be 'string'&quot;);
	Condicio.checkIsFunction(callback, &quot;The type of callback must be 'function'&quot;);
	
	var _this = this;
	var callbackWrapper = function (error, result) {
        if (error) {
            callback(error, null);
        }
        else{
			_this.isLoadFile = true;
			callback(null, null);	
		}
    };
	
	var callbackID = Utility.genGUID();
	var params = {
		url: url,
		callbackID: callbackID
	};
	
	this.session.registerCallback(callbackID, callbackWrapper,&quot;loadFile&quot;);
	
	this.session.request(this.objectID, &quot;loadFile&quot;, params);
};

<span id='ViewPID-ViewPID-method-zoomExtents'>/**
</span> * 适应窗口大小
 * @param {Number} 	[zoomMultiplier = 0.98]		缩放倍数((0,1)表示缩小的倍数，(1,+∞)表示放大倍数，1表示整张图纸刚好充满显示区)
 */
ViewPID.prototype.zoomExtents = function(zoomMultiplier) {
	var multiplier = 0.98;
	if (!Condicio.isUndefined(zoomMultiplier))
        multiplier = zoomMultiplier;
	Condicio.checkArgument(multiplier &gt; 0, &quot;param zoomMultiplier must greater than 0!&quot;);
	Condicio.checkArgument(this.isLoadFile === true, &quot;The drawing must be loaded first &quot;);
	this.session.request(this.objectID, &quot;zoomExtents&quot;, {zoomMultiplier: multiplier});
};

<span id='ViewPID-ViewPID-method-setPickHandler'>/**
</span> * 设置选中回调
 * 需要注意的点是：单击pickHandler只会回复一次交互结果；双击的情况下会先回两次单击交互结果再回一次双击交互结果 
 *
 * @param {Function} 	pickHandler    						当有选择操作（例如点选）发生时，回调
 * @param {Error}    	pickHandler.err						选择过程中发生错误
 * @param {Object}  	pickHandler.msg						选中的结果
 * @param {String[]} 	pickHandler.msg.handles    			选中的实体集合
 * @param {string}		pickHandler.msg.pickMethod			标识鼠标操作的类型（pointPick/boxPick/RButtonPick/LButtonDBLCLK）
 * @param {double}  	pickHandler.msg.x					选中点的x坐标（世界坐标系下的x坐标）
 * @param {double}  	pickHandler.msg.y					选中点的y坐标（世界坐标系下的y坐标）
 * @param {Number}		[hoverTime = 300]					框选时鼠标悬停多久(毫秒)返回选中的结果
 */
ViewPID.prototype.setPickHandler = function(pickHandler, hoverTime) {
	Condicio.checkIsFunction(pickHandler, &quot;The type of pickHandler must be 'function'&quot;);
	
	var time = 300;
	if (!Condicio.isUndefined(hoverTime))
        time = hoverTime;
	Condicio.checkArgument(time &gt; 0, &quot;param hoverTime must greater than 0!&quot;);
	
	if (this.pickHandlerID)
		this.session.unregisterCallback(this.pickHandlerID);
	
	this.pickHandlerID = Utility.genGUID();
	var params = {
		pickHandlerID: this.pickHandlerID,
		hoverTime: time
	};
	
	this.session.registerCallback(this.pickHandlerID, pickHandler, &quot;setPickHandler&quot;, true);
	
    this.session.request(this.objectID, &quot;setPickHandler&quot;, params);
};


<span id='ViewPID-ViewPID-method-highlight'>/**
</span> * 高亮
 *
 * @param {String[]} 		handles		需要高亮的实体标识集合
 * @param {boolean} 		light		是否高亮
 */
ViewPID.prototype.highlight = function(handles, light) {
	Condicio.checkArgument(this.isLoadFile === true, &quot;The drawing must be loaded first &quot;);
	ParamValidation.checkIsTypeArray(handles, String, &quot;The type of handles must be 'String Array' and valid&quot;);
	Condicio.checkIsBoolean(light, &quot;The light must be 'boolean'!&quot;);
		
	var param = {
		handles: handles,
		light: light
	};
	this.session.request(this.objectID, &quot;highlight&quot;, param);
};

<span id='ViewPID-ViewPID-method-centerEntity'>/**
</span> * 模型定位
 *
 * scale的值不给时只将实体居中，scale给值时将实体放大居中
 *
 * @param {String[]/String} 	handles				需要定位的实体标识集合/需要定位的实体标识
 * @param {double}				[scale]				当scale=1时，元件在保证完整的情况下最大化显示在屏幕上；
 *													scale：元件在屏幕上显示的长宽分别占元件最大化显示时长宽的百分比（取值范围：(0.0~1.0]）
 */
ViewPID.prototype.centerEntity = function(handles, scale) {
	Condicio.checkArgument(this.isLoadFile === true, &quot;The drawing must be loaded first &quot;);
	if(Condicio.isString(handles))
	{
		handles = [handles];
	}
	ParamValidation.checkIsTypeArray(handles, String, &quot;The type of handles must be 'String Array' and valid&quot;);
	Condicio.checkArgument(handles.length !== 0, &quot;The handles-Array must`t be NULL!&quot;);
	
	var params = {
		handles: handles
	};
	
	if(!Condicio.isUndefined(scale))
	{
		Condicio.checkArgument(scale &gt; 0.0 &amp;&amp; scale &lt;= 1.0, &quot;The scale must: &gt; 0.0 &amp;&amp; &lt;= 1.0 !&quot;);
		params.scale = scale;
	}	
	
	this.session.request(this.objectID, &quot;centerEntity&quot;, params);
};

<span id='ViewPID-ViewPID-method-calcAABB'>/**
</span> * 计算一组元件的AABB包围盒
 * 
 * 注意:对于传入的handle数组,会返回对应位置的左下角和右上角坐标,如果其中某些handle不正确,返回的坐标值为(0.0, 0.0, 0.0)&lt;br/&gt;
 * 注意:坐标值是世界坐标系下的一个三维坐标,包含X, Y, Z 坐标,对于一般的图纸,Z坐标为0,剔除即可&lt;br/&gt;
 * 注意:接口返回值为一个对象数组,每个对象包含左下角和右上角坐标值(Vec3类型),取值可参考如下方法:&lt;br/&gt; 
 * aabbs[0].minPoint[0], aabbs[0].minPoint[1], aabbs[0].minPoint[2]&lt;br/&gt;
 * aabbs[0].maxPoint[0], aabbs[0].maxPoint[1], aabbs[0].maxPoint[2]&lt;br/&gt;
 * &lt;a href=&quot;https://baike.baidu.com/item/AABB%E7%9B%92/10087682?fr=aladdin&quot;&gt;AABB相关解释请参考&lt;/a&gt;
 * 
 * @param {String[]}     			handles 				需要包围的实体标识结合 
 * @param {Function} 				callback                计算完成后的回调 
 * @param {Error} 					callback.Error		    返回错误
 * @param {Object[]}    			callback.aabbs	        计算结果
 * @param {vec3} 					callback.aabbs.minPoint 左下角坐标(世界坐标系)
 * @param {vec3} 					callback.aabbs.maxPoint	右上角坐标(世界坐标系)
 *
 */
ViewPID.prototype.calcAABB = function(handles, callback) {

	Condicio.checkArgument(this.isLoadFile === true, &quot;The drawing must be loaded first &quot;);
	ParamValidation.checkIsTypeArray(handles, String, &quot;The type of handles must be 'String Array' and valid&quot;);
	Condicio.checkArgument(handles.length !== 0, &quot;The handles-Array must`t be NULL!&quot;);
	Condicio.checkIsFunction(callback, &quot;The type of callback must be 'function'&quot;);

	var callbackWrapper = function(error, result) {
		if(!error) {
			var aabbs = result.aabbs;
			
			for(var i = 0; i&lt;aabbs.length; ++i)
			{
				aabbs[i].minPoint = CvtForJson.cvtToVec3(aabbs[i].minPoint);
				aabbs[i].maxPoint = CvtForJson.cvtToVec3(aabbs[i].maxPoint);
			}
			callback(null, aabbs);

		}else {
			callback(error, null);
		}
	}

	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callbackWrapper, &quot;calcAABB&quot;);

	var params = {
		handles: handles,
		callbackID: callbackID
	};

	this.session.request(this.objectID, &quot;calcAABB&quot;, params);
};



<span id='ViewPID-ViewPID-method-loadLabelTemplate'>/**
</span> * 加载标签
 *
 * @param {String} 		url 					标签的下载地址，支持&quot;http://...../x.dwg&quot;和&quot;file:///.../x.dwg&quot;格式
 * @param {Function} 	callback				加载标签回调
 * @param {Error}		callback.err			加载失败，指示错误信息
 *						err {NetworkError}      标签下载失败
 *                		err {DataError}         标签数据出错
 * @param {string}		callback.handle			加载成功，返回标签
 */
ViewPID.prototype.loadLabelTemplate = function(url, callback) {
	Condicio.checkArgument(this.isLoadFile === true, &quot;The drawing must be loaded first &quot;);
	Condicio.checkIsString(url, &quot;The type of url must be 'string'&quot;);
	Condicio.checkIsFunction(callback, &quot;The type of callback must be 'function'&quot;);
	
	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callback, &quot;loadLabelTemplate&quot;);
	
	var param = {
		url: url,
		callbackID: callbackID
	};
	
	this.session.request(this.objectID, &quot;loadLabelTemplate&quot;, param);
};

<span id='ViewPID-ViewPID-method-addLabel'>/**
</span> * 添加标签
 *
 * @param {Object}      option	
 * @param {vec2} 		option.position    					添加标签的位置
 * @param {object} 		option.textOption					标签的文字(key:文字的属性,value:标签的文字，需自带换行（插入'\n'）)
 * @param {String}		option.handle						添加标签
 * @param {Function} 	callback							添加标签的回调
 * @param {Error}		callback.err						添加失败，指示错误信息
 * 						err {DataError}      				数据出错
 * @param {string}		callback.label						成功返回标签
 */
ViewPID.prototype.addLabel = function(option, callback) {
	Condicio.checkArgument(this.isLoadFile === true, &quot;The drawing must be loaded first &quot;);
	Condicio.checkIsObject(option, &quot;The type of option must be 'Object'!&quot;);
	Condicio.checkIsString(option.handle, &quot;The type of handle must be 'string'&quot;);
	Condicio.checkIsObject(option.textOption, &quot;The type of textOption must be 'Object'!&quot;);
	ParamValidation.checkIsVec2(option.position, &quot;option.position must be a 'vec2'&quot;);
	Condicio.checkIsFunction(callback, &quot;The type of callback must be 'function'&quot;);
	
	for(var i in option.textOption){
		Condicio.checkIsString(i, &quot;The type of key must be 'string'&quot;);
		Condicio.checkIsString(option.textOption[i], &quot;The type of value must be 'string'&quot;);
		}
	
	var numberArray = [];
	for (var j = 0; j &lt; 2; ++j) {
		numberArray.push(option.position[j]);
	}
	option.position = numberArray;
	
	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callback, &quot;addLabel&quot;);
	
	var param = {
		option: option,
		callbackID: callbackID
	};
	
	this.session.request(this.objectID, &quot;addLabel&quot;, param);
};

<span id='ViewPID-ViewPID-method-removeLabel'>/**
</span> * 删除标签
 *
 * @param {string}		label			删除标签
 */
ViewPID.prototype.removeLabel = function(label) {
	Condicio.checkArgument(this.isLoadFile === true, &quot;The drawing must be loaded first &quot;);
	Condicio.checkIsString(label, &quot;The type of label must be 'string'&quot;);
	var entities = [];
	entities.push(label);
	this.session.request(this.objectID, &quot;removeEntities&quot;, {entities: entities});
};

<span id='ViewPID-ViewPID-method-removeEntities'>/**
</span> * 删除实体
 *
 * @param {string[]}		entities			删除实体的集合
 */
ViewPID.prototype.removeEntities = function(entities) {
	Condicio.checkArgument(this.isLoadFile === true, &quot;The drawing must be loaded first &quot;);
	ParamValidation.checkIsTypeArray(entities, String, &quot;The type of entities must be 'String Array' and valid&quot;);
	
	this.session.request(this.objectID, &quot;removeEntities&quot;, {entities: entities});
};

<span id='ViewPID-ViewPID-method-changeVisibility'>/**
</span> * 显隐实体
 *
 * @param {String[]} 	handles		 					需要显隐的实体标识集合 
 * @param {boolean} 	visible							显隐状态（true显示，false隐藏）
 */
ViewPID.prototype.changeVisibility = function(handles, visible){
	Condicio.checkArgument(this.isLoadFile === true, &quot;The drawing must be loaded first &quot;);
	ParamValidation.checkIsTypeArray(handles, String, &quot;The type of handles must be 'String Array' and valid&quot;);
	Condicio.checkArgument(handles.length != 0, &quot;The handles-Array must`t be NULL!&quot;);
	Condicio.checkIsBoolean(visible, &quot;The visible must be 'boolean'!&quot;);
	
	var param = {
		handles: handles,
		visible: visible
	};
	
	this.session.request(this.objectID, &quot;changeVisibility&quot;, param);
};

<span id='ViewPID-ViewPID-method-createColorChangeEffect'> /**
</span> * 创建变色效果
 *
 * 注意:要求传入的数据不能有重复,如果有重复数据,该接口无法正常调用&lt;br/&gt;
 *
 * @param {String[]} 	handles			需要改变颜色的实体标识集合				
 * @param {Color}		color			目标颜色
 *
 * @return {ViewPID.Effect}
 */
ViewPID.prototype.createColorChangeEffect = function (handles, color) {
	Condicio.checkArgument(this.isLoadFile === true, &quot;The drawing must be loaded first &quot;);
	ParamValidation.checkIsTypeArray(handles, String,  &quot;The  type of handles must be 'String Array' and valid!&quot;);
	Condicio.checkArgument(handles.length !== 0, &quot;The number of handles must`t be NULL!&quot;);
	//Condicio.checkIsType(color, Color, &quot;The color must be 'Color type'!&quot;);
	
	var errMessage = &quot;Duplicate handle detected, the interface does not support. &quot;;
	
	var tempSet = new Set(handles);

	if(handles.length != tempSet.size){
		alert(errMessage);
		return ;
	}
	
	var effectID = Utility.genGUID();
	var params = {
		effectID: effectID,
		handles: handles,
		color: color.rgbaArray()
	};
	
    this.session.request(this.objectID, &quot;createColorChangeEffect&quot;, params);

    return new Effect(effectID);
};

<span id='ViewPID-ViewPID-method-releaseEffect'>/**
</span> * 释放效果资源
 *
 * @param {ViewPID.Effect[]} effects 待释放的效果资源集合
 */
ViewPID.prototype.releaseEffect = function (effects) {
	Condicio.checkArgument(this.isLoadFile === true, &quot;The drawing must be loaded first &quot;);
    ParamValidation.checkIsTypeArray(effects, Effect, &quot;The type of effects must be 'Effect Array' and valid&quot;);

    var objectIDs = [];
    for (var i = 0; i &lt; effects.length; ++i) {
		objectIDs.push(effects[i].objectID);
    }
	
    this.session.request(this.objectID, &quot;releaseEffect&quot;, { objectIDs: objectIDs });
};

<span id='ViewPID-ViewPID-method-addEffect'>/**
</span> * 增加效果
 *
 * @param {ViewPID.Effect} effect 	效果
 */
ViewPID.prototype.addEffect = function (effect) {
	Condicio.checkArgument(this.isLoadFile === true, &quot;The drawing must be loaded first &quot;);
    Condicio.checkNotNull(effect, &quot;Effect must`t be NULL!&quot;);
    Condicio.checkNotUndefined(effect, &quot;Effect is undefined!&quot;);
	Condicio.checkIsType(effect, Effect, &quot;The type of effect must be PidEffect&quot;);

    this.session.request(this.objectID, &quot;addEffect&quot;, { effectID: effect.objectID });
	
	effect.on(this.session);
};

<span id='ViewPID-ViewPID-method-removeEffect'>/**
</span> * 删除效果
 *
 * @param {ViewPID.Effect} effect 	效果
 */
ViewPID.prototype.removeEffect = function (effect) {
	Condicio.checkArgument(this.isLoadFile === true, &quot;The drawing must be loaded first &quot;);
    Condicio.checkNotNull(effect, &quot;Effect must`t be NULL!&quot;);
    Condicio.checkNotUndefined(effect, &quot;Effect is undefined!&quot;);
	Condicio.checkIsType(effect, Effect, &quot;The type of effect must be PidEffect&quot;);

    this.session.request(this.objectID, &quot;removeEffect&quot;, { effectID: effect.objectID });
	
	effect.off(this.session);
};

<span id='ViewPID-ViewPID-method-captureScreenShot'>/**
</span> * 屏幕截图
 *
 * @param {Function}   callback     		获取屏幕截图完成后的回调
 * @param {Error}      callback.err 		获取屏幕截图失败返回错误
 * @param {String}     callback.screenShot  获取屏幕截图成功返回截图数据
 */
 ViewPID.prototype.captureScreenShot = function(callback){
	 Condicio.checkArgument(this.isLoadFile === true, &quot;The drawing must be loaded first &quot;);
	 Condicio.checkIsFunction(callback, Function, &quot;The type of 'callback' must be 'Function'!&quot;);
	 
	 var callbackWrapper = function(error, result){
		 if(error){
			 callback(error, null);
		 }
		 else
			 callback(null, result.imageData);
	 };
	 
	 var callbackID = Utility.genGUID();
	 this.session.registerCallback(callbackID, callbackWrapper, &quot;captureScreenShot&quot;);
	 
	 this.session.request(this.objectID, &quot;captureScreenShot&quot;, {callbackID: callbackID});
 };

<span id='ViewPID-ViewPID-method-setBackgroundColor'> /**
</span> * 设置图纸背景色
 * 需要注意的点是：本次图纸浏览的背景色需要在加载图纸之前设置
 *			
 * @param {Color}		color			目标颜色
 */
ViewPID.prototype.setBackgroundColor = function (color) {
	//Condicio.checkIsType(color, Color, &quot;The color must be 'Color type'!&quot;);
	
	var params = {
		color: color.rgbaArray()
	};
	
    this.session.request(this.objectID, &quot;setBackgroundColor&quot;, params);
};

<span id='ViewPID-ViewPID-method-addArcs'>/**
</span> * 添加弧线
 * @param {object} 		option
 * @param {object}  	[option.style]  	    					添加弧线属性，参照ViewPID.Style.ArcStyle的定义
 * @param {vec2[]} 		option.pointPairs 						    弧线点集合，每两个点确定一条弧线，点的个数为偶数个。（pointPairs需是世界坐标下的点）
 * @param {Function} 	callback							      	
 * @param {Error} 	    callback.error								添加弧线返回错误
 * @param {string[]}    callback.arcs								返回弧线
 */
ViewPID.prototype.addArcs = function(option,callback)
{	
	var defaultStyle = {
		style: Style.ArcStyle
	};
	var newOption = jQuery.extend(true,{}, defaultStyle, option);
	
	Condicio.checkArgument(this.isLoadFile === true, &quot;The drawing must be loaded first &quot;);
	ParamValidation.checkIsArcsStyle(newOption.style);
	Condicio.checkIsObject(newOption, &quot;The type of option must be 'Object'!&quot;);
	Condicio.checkIsFunction(callback,&quot;The type of callback must be 'Function'!&quot;);
	Condicio.checkArgument(newOption.pointPairs.length !== 0, &quot;The pointPairs can`t add arc! &quot;);
	ParamValidation.checkIsVec2Array(newOption.pointPairs, &quot;the type of pointPairs must be a 'vec2 Array'&quot;)
	newOption.pointPairs = CvtForJson.cvtVec2Array(newOption.pointPairs);
	newOption.style.color = newOption.style.color.rgbaArray();
	
	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callback, &quot;addArcs&quot;);
    var params = {
		option: newOption,
		callbackID: callbackID
    };
    this.session.request(this.objectID, &quot;addArcs&quot;, params);
};

<span id='ViewPID-ViewPID-method-addTextFrame'>/**
</span> * 添加文本框
 *
 * @param {Object}      option	
 * @param {vec2} 		option.position   				添加文本框的位置（表示文字的左上角）（position需是世界坐标下的点）
 * @param {String} 		option.text			   			文本框内文字，需自带换行（插入'\n'）
 * @param {Object} 	    [option.textFrameStyle] 		绘制属性，参照ViewPID.Style.TextFrameStyle的定义
 * @param {Function} 	callback						添加文本框的回调
 * @param {Error} 	    callback.Error					错误信息
 * @param {string}		callback.textFrame				返回文本框
 */
ViewPID.prototype.addTextFrame = function(option, callback) {
	var defaultOption = {
		textFrameStyle: Style.TextFrameStyle
	};
	var newOption = jQuery.extend(true, {}, defaultOption, option);
	
	Condicio.checkArgument(this.isLoadFile === true, &quot;The drawing must be loaded first &quot;);
	Condicio.checkIsObject(newOption, &quot;The type of option must be 'Object'!&quot;);
	Condicio.checkIsString(newOption.text, &quot;param text must be a string&quot;);
	ParamValidation.checkIsVec2(newOption.position, &quot;option.position must be a 'vec2'&quot;);
	Condicio.checkIsFunction(callback, &quot;The type of callback must be 'function'&quot;);
	ParamValidation.checkIsPidTextFrameStyle(newOption.textFrameStyle);
	
	newOption.textFrameStyle.text.color = newOption.textFrameStyle.text.color.rgbaArray();
	newOption.textFrameStyle.backgroundFillColor = newOption.textFrameStyle.backgroundFillColor.rgbaArray();
	newOption.position = CvtForJson.cvtVec2(newOption.position);
	
	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callback, &quot;addTextFrame&quot;);
	
	var param = {
		option: newOption,
		callbackID: callbackID
	};
	
	this.session.request(this.objectID, &quot;addTextFrame&quot;, param);
};

<span id='ViewPID-ViewPID-method-addLines'>/**
</span> * 添加线
 *
 * @param {Object}      option	
 * @param {vec2[]}		option.pointPairs					连线点集合，每两点（世界坐标下的点）确定一条线，点的个数为偶数
 * 															pointPairs[0]与pointPairs[1]确定一条线，pointPairs[2]与pointPairs[3]确定一条线
 *															pointPairs需是世界坐标下的点
 * @param {Object} 	    [option.lineStyle] 		    		绘制属性，参照ViewPID.Style.LineStyle的定义
 * @param {Function} 	callback							添加标签的回调
 * @param {Error} 	    callback.Error						错误信息
 * @param {string[]}	callback.lines						返回线集合
 */
ViewPID.prototype.addLines = function(option, callback) {
	var defaultOption = {
		lineStyle: Style.LineStyle
	};
	var newOption = jQuery.extend(true, {}, defaultOption, option);
	
	Condicio.checkArgument(this.isLoadFile === true, &quot;The drawing must be loaded first &quot;);
	Condicio.checkIsObject(newOption, &quot;The type of option must be 'Object'!&quot;);
	Condicio.checkIsFunction(callback, &quot;The type of callback must be 'function'&quot;);
	ParamValidation.checkIsVec2Array(newOption.pointPairs, &quot;the type of pointPairs must be a 'vec2 Array'&quot;)
	Condicio.checkArgument(newOption.pointPairs.length !== 0, &quot;The number of pointPairs must`t be NULL!&quot;);
	Condicio.checkArgument(newOption.lineStyle.width &gt;= 0 &amp;&amp; newOption.lineStyle.width &lt;= 2.11, &quot;The number of lineStyle.width must &gt;= 0 &amp;&amp; &lt;=2.11!&quot;);
	ParamValidation.checkIsLineStyle(newOption.lineStyle);
		
	newOption.lineStyle.color = newOption.lineStyle.color.rgbaArray();
	newOption.pointPairs = CvtForJson.cvtVec2Array(newOption.pointPairs);
	
	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callback, &quot;addLines&quot;);
	
	var param = {
		option: newOption,
		callbackID: callbackID
	};
	
	this.session.request(this.objectID, &quot;addLines&quot;, param);
};

<span id='ViewPID-ViewPID-method-worldToScreen'>/**
</span> * 世界坐标转二维显示窗口的窗口坐标
 * 
 * @param {vec3[]}		worldPoints 					世界坐标
 * @param {Function} 	callback 						回调
 * @param {Error} 	    callback.Error					错误信息
 * @param {vec2[]}		callback.screenPoints			返回世界坐标对应的窗口屏幕坐标
 */
 ViewPID.prototype.worldToScreen = function(worldPoints, callback) {
	Condicio.checkArgument(this.isLoadFile === true, &quot;The drawing must be loaded first &quot;);
	ParamValidation.checkIsVec3Array(worldPoints, &quot;the type of screenPoints must be a 'vec3 Array'&quot;)
	Condicio.checkArgument(worldPoints.length !== 0, &quot;The number of screenPoints must`t be NULL!&quot;);
	Condicio.checkIsFunction(callback, &quot;The type of callback must be 'function'&quot;);
	
	worldPoints = CvtForJson.cvtVec3Array(worldPoints);
	
	var callbackWrapper = function (error, result) {
        if (error) {
            callback(error, null);
        }
        else{
			var screenPoints = [];
			for(var i=0; i&lt;result.screenPoints.length; ++i){
				screenPoints.push(Matrix.vec2.fromValues(result.screenPoints[i].x, result.screenPoints[i].y));
			}
			callback(null, screenPoints);
		}
    };
	
	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callbackWrapper, &quot;worldToScreen&quot;);
	
	var param = {
		worldPoints: worldPoints,
		callbackID: callbackID
	};
	
	this.session.request(this.objectID, &quot;worldToScreen&quot;, param);
};

<span id='ViewPID-ViewPID-method-screenToWorld'>/**
</span> * 二维显示窗口的窗口坐标转世界坐标
 *
 * @param {vec2[]}      screenPoints					窗口屏幕坐标
 * @param {Function}  	callback						回调
 * @param {Error} 	    callback.Error					错误信息
 * @param {vec2[]}		callback.worldPoints			返回窗口屏幕坐标对应的世界坐标
 */
ViewPID.prototype.screenToWorld = function(screenPoints, callback) {
	Condicio.checkArgument(this.isLoadFile === true, &quot;The drawing must be loaded first &quot;);
	ParamValidation.checkIsVec2Array(screenPoints, &quot;the type of screenPoints must be a 'vec2 Array'&quot;)
	Condicio.checkArgument(screenPoints.length !== 0, &quot;The number of screenPoints must`t be NULL!&quot;);
	Condicio.checkIsFunction(callback, &quot;The type of callback must be 'function'&quot;);
	
	screenPoints = CvtForJson.cvtVec2Array(screenPoints);
	
	var callbackWrapper = function (error, result) {
        if (error) {
            callback(error, null);
        }
        else{
			var worldPoints = [];
			for(var i=0; i&lt;result.worldPoints.length; ++i){
				worldPoints.push(Matrix.vec2.fromValues(result.worldPoints[i].x, result.worldPoints[i].y));
			}
			callback(null, worldPoints);
		}
    };
	
	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callbackWrapper, &quot;screenToWorld&quot;);
	
	var param = {
		screenPoints: screenPoints,
		callbackID: callbackID
	};
	
	this.session.request(this.objectID, &quot;screenToWorld&quot;, param);
};

<span id='ViewPID-ViewPID-method-getAllEntities'>/**
</span> * 得到图纸上的所有entity
 *
 * @param {Function} 	callback							回调函数
 * @param {Error} 	    callback.Error						错误信息
 * @param {string[]}	callback.entities					返回实体集合
 */
ViewPID.prototype.getAllEntities = function(callback) {
	Condicio.checkArgument(this.isLoadFile === true, &quot;The drawing must be loaded first&quot;);
	Condicio.checkIsFunction(callback, &quot;The type of callback must be 'function'&quot;);
	
	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callback, &quot;getAllEntities&quot;);
	
	this.session.request(this.objectID, &quot;getAllEntities&quot;, {callbackID: callbackID});
};

<span id='ViewPID-ViewPID-method-updateArcs'>/**
</span> * 更新弧线
 *
 * @param {object} 			option
 * @param {string[]}		option.arcs					想要更新的arcs
 * @param {vec2[]} 			[option.pointPairs]			更新的弧线的点的个数为arcs个数的2倍，每两个对应arcs数组中一个的弧
 * @param {object}  		[option.style]				style的更新，同时传三个参数，不给值的参还原默认值。参照ViewPID.Style.ArcStyle的定义
 */
ViewPID.prototype.updateArcs = function(option) {
	Condicio.checkArgument(this.isLoadFile === true, &quot;The drawing must be loaded first&quot;);
	Condicio.checkIsObject(option, &quot;param option must be a Object&quot;);
	ParamValidation.checkIsTypeArray(option.arcs, String, &quot;The type of arcs must be 'String Array' and valid&quot;);
	Condicio.checkArgument(option.arcs.length !== 0, &quot;The arcs is empty&quot;);

	if(!Condicio.isUndefined(option.pointPairs)){
		Condicio.checkArgument(option.pointPairs.length === option.arcs.length * 2, &quot;The pointPairs Number can't updateArcs&quot;);
		ParamValidation.checkIsVec2Array(option.pointPairs, &quot;the type of pointPairs must be 'vec2 Array'&quot;)
		option.pointPairs = CvtForJson.cvtVec2Array(option.pointPairs);	
	}
	
	if(!Condicio.isUndefined(option.style)){
		option.style = jQuery.extend(true,{}, Style.ArcStyle, option.style);
		ParamValidation.checkIsArcsStyle(option.style);
		option.style.color = option.style.color.rgbaArray();
	}
	
	this.session.request(this.objectID, &quot;updateArcs&quot;, {option : option});
};

<span id='ViewPID-ViewPID-method-addPolygon'>/**
</span> * 添加多边形
 * 注意：6.0.0起修正了透明度逻辑，修正后透明度参数（取值范围0~1）值越大，越透明
 * 注意事项: &lt;a href=&quot;https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/2362835035/PID&quot;&gt;PID添加多边形使用注意事项&lt;/a&gt;
 * 
 * @param {vec2[]}		pointsArray							创建多边形的点集合，所有点（世界坐标下的点）,点数不少于3个
 * @param {Object}      options	
 * @param {Object} 	    [options.polygonStyle] 		    	绘制属性(包括颜色和透明度)，参照ViewPID.Style.PolygonStyle的定义
 * @param {Function} 	callback							添加多边形的回调
 * @param {Error} 	    callback.Error						错误信息
 * @param {string}		callback.polygon					返回多边形
 */
ViewPID.prototype.addPolygon = function(pointsArray, options, callback) {
	var defaultOptions = {
		polygonStyle: Style.PolygonStyle
	};
	var extendOptions = jQuery.extend(true, {}, defaultOptions, options);
	
	ParamValidation.checkIsVec2Array(pointsArray, &quot;the type of pointsArray must be a 'vec2 Array'&quot;);
	Condicio.checkArgument(pointsArray.length &gt;= 3, &quot;The points can`t draw polygon! &quot;);
	Condicio.checkArgument(this.isLoadFile === true, &quot;The drawing must be loaded first &quot;);
	Condicio.checkArgument(extendOptions.polygonStyle.transparencyValue &gt;= 0 &amp;&amp; extendOptions.polygonStyle.transparencyValue &lt;= 1, &quot;The transparencyValue must: &gt;= 0 &amp;&amp; &lt;= 1 !&quot;);
	Condicio.checkIsObject(extendOptions, &quot;The type of options must be 'Object'!&quot;);
	Condicio.checkIsFunction(callback, &quot;The type of callback must be 'function'&quot;);	
	Condicio.checkIsObject(extendOptions.polygonStyle, &quot;The type of polygonStyle must be 'Object'!&quot;);
	//Condicio.checkIsType(extendOptions.polygonStyle.color, Color, &quot;The type of PolygonStyle.color must be 'COLOR'!&quot;);
		
	extendOptions.polygonStyle.color = extendOptions.polygonStyle.color.rgbaArray();
	pointsArray = CvtForJson.cvtVec2Array(pointsArray);
	
	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callback, &quot;addPolygon&quot;);
	
	var param = {
		pointsArray: pointsArray,
		options: extendOptions,
		callbackID: callbackID
	};
	
	this.session.request(this.objectID, &quot;addPolygon&quot;, param);
};

<span id='ViewPID-ViewPID-method-bindLeftButtonOperation'>/**
</span> * 鼠标左键绑定对应操作
 * 若左键不想绑定任何操作，则可以给空字符串；例如：leftButtonOperation = &quot;&quot;
 *
 * @param {String}		leftButtonOperation				鼠标左键要绑定的操作（鼠标左键拖动可绑定平移(Pan)或框选(BoxSelection)）
 */
ViewPID.prototype.bindLeftButtonOperation = function(leftButtonOperation) {
	Condicio.checkArgument(this.isLoadFile === true, &quot;The drawing must be loaded first &quot;);
	Condicio.checkIsString(leftButtonOperation, &quot;The type of leftButtonOperation must be 'string'&quot;);
	Condicio.checkArgument(leftButtonOperation === &quot;&quot; || leftButtonOperation === &quot;Pan&quot; || leftButtonOperation === &quot;BoxSelection&quot;, &quot;leftButtonOperation must valid!&quot;);
	
	var params = {
		leftButtonOperation: leftButtonOperation
	};
	
    this.session.request(this.objectID, &quot;bindLeftButtonOperation&quot;, params);
};

<span id='ViewPID-ViewPID-method-getAllTextInfos'>/**
</span> * 得到图纸上的所有的文字实体
 *
 * @param {Function} 	callback    						回调函数
 * @param {Error}    	callback.err						错误信息
 * @param {Object[]}  	callback.infos						返回的所有结果信息
 * @param {String} 		callback.infos.handle    			文字实体的handle
 * @param {string}		callback.infos.text					文字实体中存放的文字
 * @param {vec2}  		callback.infos.minPoint				文字实体包围盒的最小值(世界坐标系)
 * @param {vec2}  		callback.infos.maxPoint				文字实体包围盒的最大值(世界坐标系)
 */
ViewPID.prototype.getAllTextInfos = function(callback) {
	Condicio.checkArgument(this.isLoadFile === true, &quot;The drawing must be loaded first&quot;);
	Condicio.checkIsFunction(callback, &quot;The type of callback must be 'function'&quot;);
	
	var callbackWrapper = function (error, result) {
		if (!error){
			var infos = result.infos;

			for(var i = 0; i&lt;infos.length; ++i)
			{
				infos[i].minPoint = CvtForJson.cvtToVec2(infos[i].minPoint);
				infos[i].maxPoint = CvtForJson.cvtToVec2(infos[i].maxPoint);
			}

			callback(null, infos);
		}
		else {
			callback(error, null);
		}
	};
	
	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID, callbackWrapper, &quot;getAllTextInfos&quot;);
	
	this.session.request(this.objectID, &quot;getAllTextInfos&quot;, {callbackID: callbackID});
};



<span id='ViewPID-ViewPID-method-downloadDWG'>/**
</span> * 下载DWG文件
 *
 * @param {Function} 	callback    						回调函数
 * @param {Error}    	callback.err						错误信息
 * @param {String}  	callback.DwgData					返回DWG文件的二进制流
 */
ViewPID.prototype.downloadDWG = function(callback) {
	Condicio.checkIsFunction(callback, &quot;The type of callback must be 'function'&quot;);

	var callbackWrapper = function(error,result) {
		if(!error)
		{
			callback(null,result.DwgData);
		}else{
			callback(error,null);
		}
	}

	var callbackID = Utility.genGUID();
	this.session.registerCallback(callbackID,callbackWrapper,&quot;downloadDWG&quot;);

	this.session.request(this.objectID,&quot;downloadDWG&quot;,{callbackID:callbackID});
};


<span id='ViewPID-ViewPID-method-setMouseHoverHandler'>/**
</span> * 设置鼠标悬停操作交互
 *
 * @param {Function} 	hoverHandler    						当鼠标悬停或悬停结束都会触发此回调
 * @param {Error}    	hoverHandler.err						交互过程中发生错误
 * @param {Object}  	hoverHandler.msg						鼠标悬停交互结果信息
 * @param {String[]} 	hoverHandler.msg.handles    			选中的实体集合
 * @param {Number} 		hoverHandler.msg.mouseHoverState 		0：标识鼠标开始悬停，1：标识鼠标结束悬停
 * @param {Vec2} 		hoverHandler.msg.worldPosition      	鼠标悬停时世界坐标系下的坐标，结束悬停时坐标值无效
 * @param {Object}      [option]                        		创建鼠标悬浮交互的参数
 * @param {Number}		[option.hoverTime = 300]				鼠标悬停需要的时间(毫秒)
 */
ViewPID.prototype.setMouseHoverHandler = function(hoverHandler, option) {
	Condicio.checkIsFunction(hoverHandler, &quot;The type of hoverHandler must be 'function'&quot;);
	
	var hoverTime = 300;
	if (!Condicio.isUndefined(option) &amp;&amp; !Condicio.isUndefined(option.hoverTime))
        hoverTime = option.hoverTime;
	Condicio.checkArgument(hoverTime &gt; 0, &quot;param hoverTime must greater than 0!&quot;);
	
	if (this.hoverHandlerID)
		this.session.unregisterCallback(this.hoverHandlerID);
	
	this.hoverHandlerID = Utility.genGUID();
	var params = {
		hoverHandlerID: this.hoverHandlerID,
		hoverTime: hoverTime
	};
	
    var hoverHandlerWrapper = function (err, result) {
        var worldPosVec2 = CvtForJson.cvtToVec2(result.worldPosition);
        var msg = {
			handles         : result.handles,
            worldPosition   : worldPosVec2,
            mouseHoverState : result.mouseHoverState
        }
        hoverHandler(err, msg);
    };
	
	this.session.registerCallback(this.hoverHandlerID, hoverHandlerWrapper, &quot;setMouseHoverHandler&quot;, true);
	
    this.session.request(this.objectID, &quot;setMouseHoverHandler&quot;, params);
};

module.exports = ViewPID;</pre>
</body>
</html>
