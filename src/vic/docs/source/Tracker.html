<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">&quot;use strict&quot;;

<span id='View3D-Tracker'>/**
</span> * @class View3D.Tracker
 * 交互Tracker
 * 
 * 应用不应该构建，应该通过InteractionFactory.createXXTracker创建得到
 */	
var Tracker = function(objectID){
	this.objectID = objectID;
};

module.exports = Tracker;</pre>
</body>
</html>
