<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">&quot;use strict&quot;;

var jQuery = require(&quot;jquery&quot;);
var MouseEvent = require(&quot;./MouseEvent.js&quot;);

<span id='ImageDisplay'>/**
</span> * @ignore
 * 画布
 */
function ImageDisplay(session, viewID, parentElem, enableSSL, proxyUri) {
    var _this = this;
	
	this.parentElem = parentElem;
	this.displayElem = document.createElement('div');
	this.parentElem.appendChild(this.displayElem);
	jQuery(this.displayElem).attr(&quot;id&quot;,&quot;bcd&quot;);
	this.displayElem.style.height = &quot;100%&quot;;
	
	_this.continueRequest = true;
	_this.continueRefresh = false;
	
	this.width = 0;
	this.height = 0;
	// 处理大小变化
    var resizeListener = function () {
		if(!_this.continueRequest){
			return;
		}
		
        var w = jQuery(parentElem).width();
        var h = jQuery(parentElem).height();

        if (_this.width !== w || _this.height !== h) {
            _this.width = w;
            _this.height = h;
			_this.continueRefresh = true;
		}
		else{
			if(_this.continueRefresh){
				_this.continueRefresh = false;
				session.request(viewID, &quot;resize&quot;, { width: w, height: h });
			}
		}

        setTimeout(resizeListener, 20);
    }
    resizeListener();
	
	MouseEvent(session, viewID, this.displayElem);
	
	var defaultProtocol = &quot;http://&quot;;
	if(enableSSL)
		defaultProtocol = &quot;https://&quot;;
	
	var hasProxy = function(){
		if(proxyUri.length !== 0){
			return true;
		}
		
		return false;
	}
	
	var uri = &quot;&quot;;
	if(hasProxy()){
		uri = &quot;/brs/image/&quot; + proxyUri;
	}
	
    var url = defaultProtocol + session.ip + &quot;:&quot; + session.port + uri + &quot;?rendererID=&quot; + session.rendererID + &quot;&amp;viewID=&quot; + viewID;
	
	// 处理图片不让选中，避免引起闪屏
	this.displayElem.onselectstart = function(){ return false; };
	
	// 创建两张图片，同一时间一个图片请求新图片，另外一个图片显示
	var backStageImage = new Image;
	var forgroundStageImage = new Image;
	session.addNetworkCloseListener(function(){
		_this.continueRequest = false;
	});
	
	this.displayElem.appendChild(backStageImage);
	this.displayElem.appendChild(forgroundStageImage);
	
	//阻止默认事件，保证不影响上层事件，目的是保证最上层能正确获取鼠标消息
	function mouseEvent(e){
		e.preventDefault();	
	};
		
	jQuery(backStageImage).on('mousedown', mouseEvent);
	jQuery(forgroundStageImage).on('mousedown', mouseEvent);
	
	forgroundStageImage.style.width = &quot;100%&quot;;
	forgroundStageImage.style.height = &quot;100%&quot;;	
	backStageImage.style.width = &quot;100%&quot;;
	backStageImage.style.height = &quot;100%&quot;;
	
	forgroundStageImage.style.display = &quot;block&quot;;
	backStageImage.style.display = &quot;none&quot;;
	
	// 处理图片显示
	var swapImageShow = function(){
		var temp = backStageImage;
		backStageImage = forgroundStageImage;
		forgroundStageImage = temp;
		
		backStageImage.style.display = &quot;none&quot;;
		forgroundStageImage.style.display = &quot;block&quot;;
	};
	var imageLoadEventHandler = function () {
		swapImageShow();
		if(_this.continueRequest)
			loadImage();
	};
	var imageErrorEventHandler = function () {
		if(_this.continueRequest)
			loadImage();
	};
	jQuery(forgroundStageImage)
		.on('abort', loadImage) //因img在响应Esc按键之后，会断开Http连接，所以在abort的时候直接加载img就不会出现卡住img的现象
		.on('load', imageLoadEventHandler)
		.on('error',  imageErrorEventHandler);
	jQuery(backStageImage)
		.on('abort', loadImage)
		.on('load', imageLoadEventHandler)
		.on('error',  imageErrorEventHandler);
	function loadImage() {
        var d = new Date();
        backStageImage.src = url + '&amp;t=' + d.getTime();
    }
	backStageImage.src = &quot;data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQImWNgYGBgAAAABQABh6FO1AAAAABJRU5ErkJggg==&quot;;
	
};

ImageDisplay.prototype.release = function(){
	this.continueRequest = false;
	this.parentElem.removeChild(this.displayElem);
};

module.exports = ImageDisplay;</pre>
</body>
</html>
