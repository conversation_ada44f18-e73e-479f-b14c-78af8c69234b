<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="X-UA-Compatible" content="chrome=1">
    <title>Sencha Examples</title>

    <script type="text/javascript" src="extjs-build/ext-all.js"></script>
    <link rel="stylesheet" type="text/css" href="extjs-build/resources/css/ext-all.css">

    <script type="text/javascript">
        function loadInlineExample(code, options, callback) {
            try {
                document.body.innerHTML = '';
                eval(code);
                callback && callback(true);
            } catch (e) {
                document.body.innerHTML = e;
                callback && callback(false, e);
            }
        }
    </script>
</head>
<body>
</body>
</html>
