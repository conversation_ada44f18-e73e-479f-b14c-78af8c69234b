Ext.data.JsonP.Global_releaseFactory({"tagname":"class","name":"Global.releaseFactory","alternateClassNames":[],"members":[{"name":"releaseFactory","tagname":"method","owner":"Global.releaseFactory","id":"method-releaseFactory","meta":{}}],"aliases":{},"files":[{"filename":"","href":""}],"component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><div class='doc-contents'>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div id='method-releaseFactory' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Global.releaseFactory'>Global.releaseFactory</span><br/><a href='source/Factory.html#Global-releaseFactory-method-releaseFactory' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Global.releaseFactory-method-releaseFactory' class='name expandable'>releaseFactory</a>( <span class='pre'>factorys</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>释放服务器组件工厂 ...</div><div class='long'><p>释放服务器组件工厂</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>factorys</span> : <a href=\"#!/api/Factory\" rel=\"Factory\" class=\"docClass\">Factory</a>[]<div class='sub-desc'><p>需要释放的工厂集合</p>\n</div></li></ul></div></div></div></div></div></div></div>","meta":{}});