Ext.data.JsonP.ViewPID_Style_TextFrameStyle({"tagname":"class","name":"ViewPID.Style.TextFrameStyle","autodetected":{},"files":[{"filename":"Style.js","href":"Style2.html#ViewPID-Style-TextFrameStyle"}],"members":[{"name":"backgroundFill","tagname":"property","owner":"ViewPID.Style.TextFrameStyle","id":"property-backgroundFill","meta":{}},{"name":"backgroundFillColor","tagname":"property","owner":"ViewPID.Style.TextFrameStyle","id":"property-backgroundFillColor","meta":{}},{"name":"padding","tagname":"property","owner":"ViewPID.Style.TextFrameStyle","id":"property-padding","meta":{}},{"name":"showBorders","tagname":"property","owner":"ViewPID.Style.TextFrameStyle","id":"property-showBorders","meta":{}},{"name":"text","tagname":"property","owner":"ViewPID.Style.TextFrameStyle","id":"property-text","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-ViewPID.Style.TextFrameStyle","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Style2.html#ViewPID-Style-TextFrameStyle' target='_blank'>Style.js</a></div></pre><div class='doc-contents'><p>标准文本框属性定义</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-backgroundFill' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.Style.TextFrameStyle'>ViewPID.Style.TextFrameStyle</span><br/><a href='source/Style2.html#ViewPID-Style-TextFrameStyle-property-backgroundFill' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.Style.TextFrameStyle-property-backgroundFill' class='name expandable'>backgroundFill</a> : boolean<span class=\"signature\"></span></div><div class='description'><div class='short'>控制是否填充背景色 ...</div><div class='long'><p>控制是否填充背景色</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-backgroundFillColor' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.Style.TextFrameStyle'>ViewPID.Style.TextFrameStyle</span><br/><a href='source/Style2.html#ViewPID-Style-TextFrameStyle-property-backgroundFillColor' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.Style.TextFrameStyle-property-backgroundFillColor' class='name expandable'>backgroundFillColor</a> : Color<span class=\"signature\"></span></div><div class='description'><div class='short'>文字背景填充色，颜色中的alpha值无效 ...</div><div class='long'><p>文字背景填充色，颜色中的alpha值无效</p>\n<p>Defaults to: <code>black</code></p></div></div></div><div id='property-padding' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.Style.TextFrameStyle'>ViewPID.Style.TextFrameStyle</span><br/><a href='source/Style2.html#ViewPID-Style-TextFrameStyle-property-padding' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.Style.TextFrameStyle-property-padding' class='name expandable'>padding</a> : Number<span class=\"signature\"></span></div><div class='description'><div class='short'>文字离边框距离（毫米） ...</div><div class='long'><p>文字离边框距离（毫米）</p>\n<p>Defaults to: <code>2</code></p></div></div></div><div id='property-showBorders' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.Style.TextFrameStyle'>ViewPID.Style.TextFrameStyle</span><br/><a href='source/Style2.html#ViewPID-Style-TextFrameStyle-property-showBorders' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.Style.TextFrameStyle-property-showBorders' class='name expandable'>showBorders</a> : boolean<span class=\"signature\"></span></div><div class='description'><div class='short'>控制文本边框的可见性（边框的颜色随字体的颜色） ...</div><div class='long'><p>控制文本边框的可见性（边框的颜色随字体的颜色）</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-text' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.Style.TextFrameStyle'>ViewPID.Style.TextFrameStyle</span><br/><a href='source/Style2.html#ViewPID-Style-TextFrameStyle-property-text' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.Style.TextFrameStyle-property-text' class='name expandable'>text</a> : Object<span class=\"signature\"></span></div><div class='description'><div class='short'><p>文本属性，参照<a href=\"#!/api/ViewPID.Style.TextStyle\" rel=\"ViewPID.Style.TextStyle\" class=\"docClass\">ViewPID.Style.TextStyle</a>定义</p>\n</div><div class='long'><p>文本属性，参照<a href=\"#!/api/ViewPID.Style.TextStyle\" rel=\"ViewPID.Style.TextStyle\" class=\"docClass\">ViewPID.Style.TextStyle</a>定义</p>\n</div></div></div></div></div></div></div>","meta":{}});