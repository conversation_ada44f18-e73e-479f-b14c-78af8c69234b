Ext.data.JsonP.View3D_Interaction({"tagname":"class","name":"View3D.Interaction","autodetected":{},"files":[{"filename":"Interaction.js","href":"Interaction.html#View3D-Interaction"}],"abstract":true,"members":[],"alternateClassNames":[],"aliases":{},"id":"class-View3D.Interaction","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Interaction.html#View3D-Interaction' target='_blank'>Interaction.js</a></div></pre><div class='doc-contents'><p>交互基接口</p>\n\n<p>应用不应该构建，应该通过InteractionFactory.createXXInteraction创建得到</p>\n</div><div class='members'></div></div>","meta":{"abstract":true}});