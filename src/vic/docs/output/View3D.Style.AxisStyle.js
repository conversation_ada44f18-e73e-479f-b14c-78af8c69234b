Ext.data.JsonP.View3D_Style_AxisStyle({"tagname":"class","name":"View3D.Style.AxisStyle","autodetected":{},"files":[{"filename":"Style.js","href":"Style.html#View3D-Style-AxisStyle"}],"members":[{"name":"color","tagname":"property","owner":"View3D.Style.AxisStyle","id":"property-color","meta":{}},{"name":"length","tagname":"property","owner":"View3D.Style.AxisStyle","id":"property-length","meta":{}},{"name":"pickedColor","tagname":"property","owner":"View3D.Style.AxisStyle","id":"property-pickedColor","meta":{}},{"name":"pickedRadius","tagname":"property","owner":"View3D.Style.AxisStyle","id":"property-pickedRadius","meta":{}},{"name":"width","tagname":"property","owner":"View3D.Style.AxisStyle","id":"property-width","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-View3D.Style.AxisStyle","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Style.html#View3D-Style-AxisStyle' target='_blank'>Style.js</a></div></pre><div class='doc-contents'><p>轴属性定义</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-color' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.AxisStyle'>View3D.Style.AxisStyle</span><br/><a href='source/Style.html#View3D-Style-AxisStyle-property-color' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.AxisStyle-property-color' class='name expandable'>color</a> : Color<span class=\"signature\"></span></div><div class='description'><div class='short'>颜色，颜色中的alpha值无效 ...</div><div class='long'><p>颜色，颜色中的alpha值无效</p>\n<p>Defaults to: <code>red</code></p></div></div></div><div id='property-length' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.AxisStyle'>View3D.Style.AxisStyle</span><br/><a href='source/Style.html#View3D-Style-AxisStyle-property-length' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.AxisStyle-property-length' class='name expandable'>length</a> : Number<span class=\"signature\"></span></div><div class='description'><div class='short'>长（像素） ...</div><div class='long'><p>长（像素）</p>\n<p>Defaults to: <code>150</code></p></div></div></div><div id='property-pickedColor' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.AxisStyle'>View3D.Style.AxisStyle</span><br/><a href='source/Style.html#View3D-Style-AxisStyle-property-pickedColor' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.AxisStyle-property-pickedColor' class='name expandable'>pickedColor</a> : Color<span class=\"signature\"></span></div><div class='description'><div class='short'>轴被选中后的颜色，颜色中的alpha值无效 ...</div><div class='long'><p>轴被选中后的颜色，颜色中的alpha值无效</p>\n<p>Defaults to: <code>yellow</code></p></div></div></div><div id='property-pickedRadius' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.AxisStyle'>View3D.Style.AxisStyle</span><br/><a href='source/Style.html#View3D-Style-AxisStyle-property-pickedRadius' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.AxisStyle-property-pickedRadius' class='name expandable'>pickedRadius</a> : Number<span class=\"signature\"></span></div><div class='description'><div class='short'>轴可被选中半径（像素） ...</div><div class='long'><p>轴可被选中半径（像素）</p>\n<p>Defaults to: <code>6</code></p></div></div></div><div id='property-width' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.AxisStyle'>View3D.Style.AxisStyle</span><br/><a href='source/Style.html#View3D-Style-AxisStyle-property-width' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.AxisStyle-property-width' class='name expandable'>width</a> : Number<span class=\"signature\"></span></div><div class='description'><div class='short'>轴宽（像素） ...</div><div class='long'><p>轴宽（像素）</p>\n<p>Defaults to: <code>3</code></p></div></div></div></div></div></div></div>","meta":{}});