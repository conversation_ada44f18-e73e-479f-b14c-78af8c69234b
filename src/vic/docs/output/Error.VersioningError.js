Ext.data.JsonP.Error_VersioningError({"tagname":"class","name":"Error.VersioningError","autodetected":{},"files":[{"filename":"Error.js","href":"Error.html#Error-VersioningError"}],"members":[{"name":"message","tagname":"property","owner":"Error.VersioningError","id":"property-message","meta":{}},{"name":"name","tagname":"property","owner":"Error.VersioningError","id":"property-name","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-Error.VersioningError","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Error.html#Error-VersioningError' target='_blank'>Error.js</a></div></pre><div class='doc-contents'><p>版本管理异常，VIC与BRS进行版本协商不通过</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-message' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Error.VersioningError'>Error.VersioningError</span><br/><a href='source/Error.html#Error-VersioningError-property-message' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Error.VersioningError-property-message' class='name expandable'>message</a> : String<span class=\"signature\"></span></div><div class='description'><div class='short'><p>错误信息</p>\n</div><div class='long'><p>错误信息</p>\n</div></div></div><div id='property-name' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Error.VersioningError'>Error.VersioningError</span><br/><a href='source/Error.html#Error-VersioningError-property-name' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Error.VersioningError-property-name' class='name expandable'>name</a> : String<span class=\"signature\"></span></div><div class='description'><div class='short'>错误名 ...</div><div class='long'><p>错误名</p>\n<p>Defaults to: <code>&quot;VersioningError&quot;</code></p></div></div></div></div></div></div></div>","meta":{}});