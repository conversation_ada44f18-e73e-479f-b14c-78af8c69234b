Ext.data.JsonP.View3D_Node({"tagname":"class","name":"View3D.Node","autodetected":{},"files":[{"filename":"Node.js","href":"Node.html#View3D-Node"}],"members":[{"name":"getName","tagname":"method","owner":"View3D.Node","id":"method-getName","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-View3D.Node","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Node.html#View3D-Node' target='_blank'>Node.js</a></div></pre><div class='doc-contents'><p>节点操作</p>\n\n<p>不应该直接创建，应该由NodeFactory创建出来</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div id='method-getName' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Node'>View3D.Node</span><br/><a href='source/Node.html#View3D-Node-method-getName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Node-method-getName' class='name expandable'>getName</a>( <span class='pre'></span> ) : String<span class=\"signature\"></span></div><div class='description'><div class='short'>获取节点名字 ...</div><div class='long'><p>获取节点名字</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'>String</span><div class='sub-desc'>\n</div></li></ul></div></div></div></div></div></div></div>","meta":{}});