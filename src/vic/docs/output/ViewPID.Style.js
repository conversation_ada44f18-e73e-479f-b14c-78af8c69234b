Ext.data.JsonP.ViewPID_Style({"tagname":"class","name":"ViewPID.Style","autodetected":{},"files":[{"filename":"Style.js","href":"Style2.html#ViewPID-Style"}],"members":[],"alternateClassNames":[],"aliases":{},"id":"class-ViewPID.Style","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Style2.html#ViewPID-Style' target='_blank'>Style.js</a></div></pre><div class='doc-contents'><p>标准属性定义</p>\n</div><div class='members'></div></div>","meta":{}});