Ext.data.JsonP.View3D_LightEffect({"tagname":"class","name":"View3D.LightEffect","autodetected":{},"files":[{"filename":"LightEffect.js","href":"LightEffect.html#View3D-LightEffect"}],"extends":"View3D.Effect","members":[{"name":"createChangeLightCommand","tagname":"method","owner":"View3D.LightEffect","id":"method-createChangeLightCommand","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-View3D.LightEffect","component":false,"superclasses":["View3D.Effect"],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Hierarchy</h4><div class='subclass first-child'><a href='#!/api/View3D.Effect' rel='View3D.Effect' class='docClass'>View3D.Effect</a><div class='subclass '><strong>View3D.LightEffect</strong></div></div><h4>Files</h4><div class='dependency'><a href='source/LightEffect.html#View3D-LightEffect' target='_blank'>LightEffect.js</a></div></pre><div class='doc-contents'><p>光照效果</p>\n\n<p>创建修改光照的命令</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div id='method-createChangeLightCommand' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.LightEffect'>View3D.LightEffect</span><br/><a href='source/LightEffect.html#View3D-LightEffect-method-createChangeLightCommand' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.LightEffect-method-createChangeLightCommand' class='name expandable'>createChangeLightCommand</a>( <span class='pre'>lightStyle</span> ) : <a href=\"#!/api/View3D.Command\" rel=\"View3D.Command\" class=\"docClass\">View3D.Command</a><span class=\"signature\"></span></div><div class='description'><div class='short'>参数相关详情请参考 ...</div><div class='long'><p><a href=\"https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/75956914\">参数相关详情请参考</a></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>lightStyle</span> : Object<div class='sub-desc'><p>光照基本属性，参照<a href=\"#!/api/View3D.Style.LightStyle\" rel=\"View3D.Style.LightStyle\" class=\"docClass\">View3D.Style.LightStyle</a>的定义</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Command\" rel=\"View3D.Command\" class=\"docClass\">View3D.Command</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div></div></div></div></div>","meta":{}});