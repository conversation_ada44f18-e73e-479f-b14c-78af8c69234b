Ext.data.JsonP.View3D_Dragger({"tagname":"class","name":"View3D.Dragger","autodetected":{},"files":[{"filename":"EffectFactory.js","href":"EffectFactory.html#View3D-<PERSON>agger"}],"members":[],"alternateClassNames":[],"aliases":{},"id":"class-View3D.Dragger","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/EffectFactory.html#View3D-Dragger' target='_blank'>EffectFactory.js</a></div></pre><div class='doc-contents'><p>拖拽器\n拖拽器可用于创建可拖拽效果接口从而实现拖拽模型的效果</p>\n</div><div class='members'></div></div>","meta":{}});