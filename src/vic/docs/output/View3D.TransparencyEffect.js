Ext.data.JsonP.View3D_TransparencyEffect({"tagname":"class","name":"View3D.TransparencyEffect","autodetected":{},"files":[{"filename":"TransparencyEffect.js","href":"TransparencyEffect.html#View3D-TransparencyEffect"}],"extends":"View3D.Effect","members":[{"name":"createChangeTransparencyCommand","tagname":"method","owner":"View3D.TransparencyEffect","id":"method-createChangeTransparencyCommand","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-View3D.TransparencyEffect","component":false,"superclasses":["View3D.Effect"],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Hierarchy</h4><div class='subclass first-child'><a href='#!/api/View3D.Effect' rel='View3D.Effect' class='docClass'>View3D.Effect</a><div class='subclass '><strong>View3D.TransparencyEffect</strong></div></div><h4>Files</h4><div class='dependency'><a href='source/TransparencyEffect.html#View3D-TransparencyEffect' target='_blank'>TransparencyEffect.js</a></div></pre><div class='doc-contents'><p>透明效果</p>\n\n<p>创建修改透明度的命令</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div id='method-createChangeTransparencyCommand' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.TransparencyEffect'>View3D.TransparencyEffect</span><br/><a href='source/TransparencyEffect.html#View3D-TransparencyEffect-method-createChangeTransparencyCommand' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.TransparencyEffect-method-createChangeTransparencyCommand' class='name expandable'>createChangeTransparencyCommand</a>( <span class='pre'>value</span> ) : <a href=\"#!/api/View3D.Command\" rel=\"View3D.Command\" class=\"docClass\">View3D.Command</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建改变透明度的命令\n注意：6.0.0起修正了透明度逻辑，修正后透明度参数（取值范围0~1）值越大，越透明 ...</div><div class='long'><p>创建改变透明度的命令\n注意：6.0.0起修正了透明度逻辑，修正后透明度参数（取值范围0~1）值越大，越透明</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : Number<div class='sub-desc'><p>设置透明度参数（取值范围0~1）0为不透明，1为全透明(值：0 &lt;= value &lt;= 1）</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Command\" rel=\"View3D.Command\" class=\"docClass\">View3D.Command</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div></div></div></div></div>","meta":{}});