Ext.data.JsonP.ViewPID_Style_ArcStyle({"tagname":"class","name":"ViewPID.Style.ArcStyle","autodetected":{},"files":[{"filename":"Style.js","href":"Style2.html#ViewPID-Style-ArcStyle"}],"members":[{"name":"angle","tagname":"property","owner":"ViewPID.Style.ArcStyle","id":"property-angle","meta":{}},{"name":"color","tagname":"property","owner":"ViewPID.Style.ArcStyle","id":"property-color","meta":{}},{"name":"width","tagname":"property","owner":"ViewPID.Style.ArcStyle","id":"property-width","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-ViewPID.Style.ArcStyle","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Style2.html#ViewPID-Style-ArcStyle' target='_blank'>Style.js</a></div></pre><div class='doc-contents'><p>标准画弧属性定义</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-angle' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.Style.ArcStyle'>ViewPID.Style.ArcStyle</span><br/><a href='source/Style2.html#ViewPID-Style-ArcStyle-property-angle' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.Style.ArcStyle-property-angle' class='name expandable'>angle</a> : Number<span class=\"signature\"></span></div><div class='description'><div class='short'>弧线所对应的圆心角：设圆弧所包含的圆心角为A(弧度表示)，凸度=tan(1/4*A)。 ...</div><div class='long'><p>弧线所对应的圆心角：设圆弧所包含的圆心角为A(弧度表示)，凸度=tan(1/4*A)。</p>\n<p>Defaults to: <code>120</code></p></div></div></div><div id='property-color' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.Style.ArcStyle'>ViewPID.Style.ArcStyle</span><br/><a href='source/Style2.html#ViewPID-Style-ArcStyle-property-color' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.Style.ArcStyle-property-color' class='name expandable'>color</a> : Color<span class=\"signature\"></span></div><div class='description'><div class='short'>弧线颜色，颜色中的alpha值无效 ...</div><div class='long'><p>弧线颜色，颜色中的alpha值无效</p>\n<p>Defaults to: <code>red</code></p></div></div></div><div id='property-width' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.Style.ArcStyle'>ViewPID.Style.ArcStyle</span><br/><a href='source/Style2.html#ViewPID-Style-ArcStyle-property-width' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.Style.ArcStyle-property-width' class='name expandable'>width</a> : Number<span class=\"signature\"></span></div><div class='description'><div class='short'>弧线宽度：单位：毫米；范围：大于0 ...</div><div class='long'><p>弧线宽度：单位：毫米；范围：大于0</p>\n<p>Defaults to: <code>0.5</code></p></div></div></div></div></div></div></div>","meta":{}});