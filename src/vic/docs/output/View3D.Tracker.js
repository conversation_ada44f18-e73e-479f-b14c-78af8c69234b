Ext.data.JsonP.View3D_Tracker({"tagname":"class","name":"View3D.Tracker","autodetected":{},"files":[{"filename":"Tracker.js","href":"Tracker.html#View3D-Tracker"}],"members":[],"alternateClassNames":[],"aliases":{},"id":"class-View3D.Tracker","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Tracker.html#View3D-Tracker' target='_blank'>Tracker.js</a></div></pre><div class='doc-contents'><p>交互Tracker</p>\n\n<p>应用不应该构建，应该通过InteractionFactory.createXXTracker创建得到</p>\n</div><div class='members'></div></div>","meta":{}});