Ext.data.JsonP.ViewPID_ViewPID({"tagname":"class","name":"ViewPID.ViewPID","autodetected":{},"files":[{"filename":"ViewPID.js","href":"ViewPID.html#ViewPID-ViewPID"}],"members":[{"name":"addArcs","tagname":"method","owner":"ViewPID.ViewPID","id":"method-addArcs","meta":{}},{"name":"addEffect","tagname":"method","owner":"ViewPID.ViewPID","id":"method-addEffect","meta":{}},{"name":"addLabel","tagname":"method","owner":"ViewPID.ViewPID","id":"method-addLabel","meta":{}},{"name":"addLines","tagname":"method","owner":"ViewPID.ViewPID","id":"method-addLines","meta":{}},{"name":"addPolygon","tagname":"method","owner":"ViewPID.ViewPID","id":"method-addPolygon","meta":{}},{"name":"addTextFrame","tagname":"method","owner":"ViewPID.ViewPID","id":"method-addTextFrame","meta":{}},{"name":"bindLeftButtonOperation","tagname":"method","owner":"ViewPID.ViewPID","id":"method-bindLeftButtonOperation","meta":{}},{"name":"calcAABB","tagname":"method","owner":"ViewPID.ViewPID","id":"method-calcAABB","meta":{}},{"name":"captureScreenShot","tagname":"method","owner":"ViewPID.ViewPID","id":"method-captureScreenShot","meta":{}},{"name":"centerEntity","tagname":"method","owner":"ViewPID.ViewPID","id":"method-centerEntity","meta":{}},{"name":"changeVisibility","tagname":"method","owner":"ViewPID.ViewPID","id":"method-changeVisibility","meta":{}},{"name":"createColorChangeEffect","tagname":"method","owner":"ViewPID.ViewPID","id":"method-createColorChangeEffect","meta":{}},{"name":"downloadDWG","tagname":"method","owner":"ViewPID.ViewPID","id":"method-downloadDWG","meta":{}},{"name":"getAllEntities","tagname":"method","owner":"ViewPID.ViewPID","id":"method-getAllEntities","meta":{}},{"name":"getAllTextInfos","tagname":"method","owner":"ViewPID.ViewPID","id":"method-getAllTextInfos","meta":{}},{"name":"highlight","tagname":"method","owner":"ViewPID.ViewPID","id":"method-highlight","meta":{}},{"name":"loadFile","tagname":"method","owner":"ViewPID.ViewPID","id":"method-loadFile","meta":{}},{"name":"loadLabelTemplate","tagname":"method","owner":"ViewPID.ViewPID","id":"method-loadLabelTemplate","meta":{}},{"name":"releaseEffect","tagname":"method","owner":"ViewPID.ViewPID","id":"method-releaseEffect","meta":{}},{"name":"removeEffect","tagname":"method","owner":"ViewPID.ViewPID","id":"method-removeEffect","meta":{}},{"name":"removeEntities","tagname":"method","owner":"ViewPID.ViewPID","id":"method-removeEntities","meta":{}},{"name":"removeLabel","tagname":"method","owner":"ViewPID.ViewPID","id":"method-removeLabel","meta":{}},{"name":"screenToWorld","tagname":"method","owner":"ViewPID.ViewPID","id":"method-screenToWorld","meta":{}},{"name":"setBackgroundColor","tagname":"method","owner":"ViewPID.ViewPID","id":"method-setBackgroundColor","meta":{}},{"name":"setMouseHoverHandler","tagname":"method","owner":"ViewPID.ViewPID","id":"method-setMouseHoverHandler","meta":{}},{"name":"setPickHandler","tagname":"method","owner":"ViewPID.ViewPID","id":"method-setPickHandler","meta":{}},{"name":"updateArcs","tagname":"method","owner":"ViewPID.ViewPID","id":"method-updateArcs","meta":{}},{"name":"worldToScreen","tagname":"method","owner":"ViewPID.ViewPID","id":"method-worldToScreen","meta":{}},{"name":"zoomExtents","tagname":"method","owner":"ViewPID.ViewPID","id":"method-zoomExtents","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-ViewPID.ViewPID","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/ViewPID.html#ViewPID-ViewPID' target='_blank'>ViewPID.js</a></div></pre><div class='doc-contents'><p>PID渲染场景</p>\n\n<p>提供加载图纸，显示及对图纸操作的功能</p>\n\n<p>不应该直接创建，应该通过<a href=\"#!/api/Factory\" rel=\"Factory\" class=\"docClass\">Factory</a>提供的createViewPID得到</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div id='method-addArcs' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-addArcs' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-addArcs' class='name expandable'>addArcs</a>( <span class='pre'>option, callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>添加弧线 ...</div><div class='long'><p>添加弧线</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : object<div class='sub-desc'>\n<ul><li><span class='pre'>style</span> : object (optional)<div class='sub-desc'><p>添加弧线属性，参照<a href=\"#!/api/ViewPID.Style.ArcStyle\" rel=\"ViewPID.Style.ArcStyle\" class=\"docClass\">ViewPID.Style.ArcStyle</a>的定义</p>\n</div></li><li><span class='pre'>pointPairs</span> : vec2[]<div class='sub-desc'><p>弧线点集合，每两个点确定一条弧线，点的个数为偶数个。（pointPairs需是世界坐标下的点）</p>\n</div></li></ul></div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>error</span> : Error<div class='sub-desc'><p>添加弧线返回错误</p>\n</div></li><li><span class='pre'>arcs</span> : string[]<div class='sub-desc'><p>返回弧线</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-addEffect' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-addEffect' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-addEffect' class='name expandable'>addEffect</a>( <span class='pre'>effect</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>增加效果 ...</div><div class='long'><p>增加效果</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>effect</span> : <a href=\"#!/api/ViewPID.Effect\" rel=\"ViewPID.Effect\" class=\"docClass\">ViewPID.Effect</a><div class='sub-desc'><p>效果</p>\n</div></li></ul></div></div></div><div id='method-addLabel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-addLabel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-addLabel' class='name expandable'>addLabel</a>( <span class='pre'>option, callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>添加标签 ...</div><div class='long'><p>添加标签</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'>\n<ul><li><span class='pre'>position</span> : vec2<div class='sub-desc'><p>添加标签的位置</p>\n</div></li><li><span class='pre'>textOption</span> : object<div class='sub-desc'><p>标签的文字(key:文字的属性,value:标签的文字，需自带换行（插入'\\n'）)</p>\n</div></li><li><span class='pre'>handle</span> : String<div class='sub-desc'><p>添加标签</p>\n</div></li></ul></div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>添加标签的回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>添加失败，指示错误信息\n                        err {DataError}                     数据出错</p>\n</div></li><li><span class='pre'>label</span> : string<div class='sub-desc'><p>成功返回标签</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-addLines' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-addLines' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-addLines' class='name expandable'>addLines</a>( <span class='pre'>option, callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>添加线 ...</div><div class='long'><p>添加线</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'>\n<ul><li><span class='pre'>pointPairs</span> : vec2[]<div class='sub-desc'><p>连线点集合，每两点（世界坐标下的点）确定一条线，点的个数为偶数\n                                                            pointPairs[0]与pointPairs[1]确定一条线，pointPairs[2]与pointPairs[3]确定一条线\n                                                        pointPairs需是世界坐标下的点</p>\n</div></li><li><span class='pre'>lineStyle</span> : Object (optional)<div class='sub-desc'><p>绘制属性，参照<a href=\"#!/api/ViewPID.Style.LineStyle\" rel=\"ViewPID.Style.LineStyle\" class=\"docClass\">ViewPID.Style.LineStyle</a>的定义</p>\n</div></li></ul></div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>添加标签的回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>Error</span> : Error<div class='sub-desc'><p>错误信息</p>\n</div></li><li><span class='pre'>lines</span> : string[]<div class='sub-desc'><p>返回线集合</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-addPolygon' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-addPolygon' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-addPolygon' class='name expandable'>addPolygon</a>( <span class='pre'>pointsArray, options, callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>添加多边形\n注意：6.0.0起修正了透明度逻辑，修正后透明度参数（取值范围0~1）值越大，越透明\n注意事项: PID添加多边形使用注意事项 ...</div><div class='long'><p>添加多边形\n注意：6.0.0起修正了透明度逻辑，修正后透明度参数（取值范围0~1）值越大，越透明\n注意事项: <a href=\"https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/2362835035/PID\">PID添加多边形使用注意事项</a></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>pointsArray</span> : vec2[]<div class='sub-desc'><p>创建多边形的点集合，所有点（世界坐标下的点）,点数不少于3个</p>\n</div></li><li><span class='pre'>options</span> : Object<div class='sub-desc'>\n<ul><li><span class='pre'>polygonStyle</span> : Object (optional)<div class='sub-desc'><p>绘制属性(包括颜色和透明度)，参照<a href=\"#!/api/ViewPID.Style.PolygonStyle\" rel=\"ViewPID.Style.PolygonStyle\" class=\"docClass\">ViewPID.Style.PolygonStyle</a>的定义</p>\n</div></li></ul></div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>添加多边形的回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>Error</span> : Error<div class='sub-desc'><p>错误信息</p>\n</div></li><li><span class='pre'>polygon</span> : string<div class='sub-desc'><p>返回多边形</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-addTextFrame' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-addTextFrame' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-addTextFrame' class='name expandable'>addTextFrame</a>( <span class='pre'>option, callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>添加文本框 ...</div><div class='long'><p>添加文本框</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'>\n<ul><li><span class='pre'>position</span> : vec2<div class='sub-desc'><p>添加文本框的位置（表示文字的左上角）（position需是世界坐标下的点）</p>\n</div></li><li><span class='pre'>text</span> : String<div class='sub-desc'><p>文本框内文字，需自带换行（插入'\\n'）</p>\n</div></li><li><span class='pre'>textFrameStyle</span> : Object (optional)<div class='sub-desc'><p>绘制属性，参照<a href=\"#!/api/ViewPID.Style.TextFrameStyle\" rel=\"ViewPID.Style.TextFrameStyle\" class=\"docClass\">ViewPID.Style.TextFrameStyle</a>的定义</p>\n</div></li></ul></div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>添加文本框的回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>Error</span> : Error<div class='sub-desc'><p>错误信息</p>\n</div></li><li><span class='pre'>textFrame</span> : string<div class='sub-desc'><p>返回文本框</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-bindLeftButtonOperation' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-bindLeftButtonOperation' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-bindLeftButtonOperation' class='name expandable'>bindLeftButtonOperation</a>( <span class='pre'>leftButtonOperation</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>鼠标左键绑定对应操作\n若左键不想绑定任何操作，则可以给空字符串；例如：leftButtonOperation = \"\" ...</div><div class='long'><p>鼠标左键绑定对应操作\n若左键不想绑定任何操作，则可以给空字符串；例如：leftButtonOperation = \"\"</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>leftButtonOperation</span> : String<div class='sub-desc'><p>鼠标左键要绑定的操作（鼠标左键拖动可绑定平移(Pan)或框选(BoxSelection)）</p>\n</div></li></ul></div></div></div><div id='method-calcAABB' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-calcAABB' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-calcAABB' class='name expandable'>calcAABB</a>( <span class='pre'>handles, callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>计算一组元件的AABB包围盒\n\n注意:对于传入的handle数组,会返回对应位置的左下角和右上角坐标,如果其中某些handle不正确,返回的坐标值为(0.0, 0.0, 0.0)\n注意:坐标值是世界坐标系下的一个三维坐标,包含X, Y...</div><div class='long'><p>计算一组元件的AABB包围盒</p>\n\n<p>注意:对于传入的handle数组,会返回对应位置的左下角和右上角坐标,如果其中某些handle不正确,返回的坐标值为(0.0, 0.0, 0.0)<br/>\n注意:坐标值是世界坐标系下的一个三维坐标,包含X, Y, Z 坐标,对于一般的图纸,Z坐标为0,剔除即可<br/>\n注意:接口返回值为一个对象数组,每个对象包含左下角和右上角坐标值(Vec3类型),取值可参考如下方法:<br/>\naabbs[0].minPoint[0], aabbs[0].minPoint[1], aabbs[0].minPoint[2]<br/>\naabbs[0].maxPoint[0], aabbs[0].maxPoint[1], aabbs[0].maxPoint[2]<br/>\n<a href=\"https://baike.baidu.com/item/AABB%E7%9B%92/10087682?fr=aladdin\">AABB相关解释请参考</a></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>handles</span> : String[]<div class='sub-desc'><p>需要包围的实体标识结合</p>\n</div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>计算完成后的回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>Error</span> : Error<div class='sub-desc'><p>返回错误</p>\n</div></li><li><span class='pre'>aabbs</span> : Object[]<div class='sub-desc'><p>计算结果</p>\n<ul><li><span class='pre'>minPoint</span> : vec3<div class='sub-desc'><p>左下角坐标(世界坐标系)</p>\n</div></li><li><span class='pre'>maxPoint</span> : vec3<div class='sub-desc'><p>右上角坐标(世界坐标系)</p>\n</div></li></ul></div></li></ul></div></li></ul></div></div></div><div id='method-captureScreenShot' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-captureScreenShot' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-captureScreenShot' class='name expandable'>captureScreenShot</a>( <span class='pre'>callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>屏幕截图 ...</div><div class='long'><p>屏幕截图</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>获取屏幕截图完成后的回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>获取屏幕截图失败返回错误</p>\n</div></li><li><span class='pre'>screenShot</span> : String<div class='sub-desc'><p>获取屏幕截图成功返回截图数据</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-centerEntity' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-centerEntity' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-centerEntity' class='name expandable'>centerEntity</a>( <span class='pre'>handles, [scale]</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>模型定位\n\nscale的值不给时只将实体居中，scale给值时将实体放大居中 ...</div><div class='long'><p>模型定位</p>\n\n<p>scale的值不给时只将实体居中，scale给值时将实体放大居中</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>handles</span> : String[]/String<div class='sub-desc'><p>需要定位的实体标识集合/需要定位的实体标识</p>\n</div></li><li><span class='pre'>scale</span> : double (optional)<div class='sub-desc'><p>当scale=1时，元件在保证完整的情况下最大化显示在屏幕上；\n                                                scale：元件在屏幕上显示的长宽分别占元件最大化显示时长宽的百分比（取值范围：(0.0~1.0]）</p>\n</div></li></ul></div></div></div><div id='method-changeVisibility' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-changeVisibility' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-changeVisibility' class='name expandable'>changeVisibility</a>( <span class='pre'>handles, visible</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>显隐实体 ...</div><div class='long'><p>显隐实体</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>handles</span> : String[]<div class='sub-desc'><p>需要显隐的实体标识集合</p>\n</div></li><li><span class='pre'>visible</span> : boolean<div class='sub-desc'><p>显隐状态（true显示，false隐藏）</p>\n</div></li></ul></div></div></div><div id='method-createColorChangeEffect' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-createColorChangeEffect' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-createColorChangeEffect' class='name expandable'>createColorChangeEffect</a>( <span class='pre'>handles, color</span> ) : <a href=\"#!/api/ViewPID.Effect\" rel=\"ViewPID.Effect\" class=\"docClass\">ViewPID.Effect</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建变色效果\n\n注意:要求传入的数据不能有重复,如果有重复数据,该接口无法正常调用 ...</div><div class='long'><p>创建变色效果</p>\n\n<p>注意:要求传入的数据不能有重复,如果有重复数据,该接口无法正常调用<br/></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>handles</span> : String[]<div class='sub-desc'><p>需要改变颜色的实体标识集合</p>\n</div></li><li><span class='pre'>color</span> : Color<div class='sub-desc'><p>目标颜色</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/ViewPID.Effect\" rel=\"ViewPID.Effect\" class=\"docClass\">ViewPID.Effect</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-downloadDWG' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-downloadDWG' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-downloadDWG' class='name expandable'>downloadDWG</a>( <span class='pre'>callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>下载DWG文件 ...</div><div class='long'><p>下载DWG文件</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>回调函数</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>错误信息</p>\n</div></li><li><span class='pre'>DwgData</span> : String<div class='sub-desc'><p>返回DWG文件的二进制流</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-getAllEntities' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-getAllEntities' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-getAllEntities' class='name expandable'>getAllEntities</a>( <span class='pre'>callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>得到图纸上的所有entity ...</div><div class='long'><p>得到图纸上的所有entity</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>回调函数</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>Error</span> : Error<div class='sub-desc'><p>错误信息</p>\n</div></li><li><span class='pre'>entities</span> : string[]<div class='sub-desc'><p>返回实体集合</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-getAllTextInfos' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-getAllTextInfos' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-getAllTextInfos' class='name expandable'>getAllTextInfos</a>( <span class='pre'>callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>得到图纸上的所有的文字实体 ...</div><div class='long'><p>得到图纸上的所有的文字实体</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>回调函数</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>错误信息</p>\n</div></li><li><span class='pre'>infos</span> : Object[]<div class='sub-desc'><p>返回的所有结果信息</p>\n<ul><li><span class='pre'>handle</span> : String<div class='sub-desc'><p>文字实体的handle</p>\n</div></li><li><span class='pre'>text</span> : string<div class='sub-desc'><p>文字实体中存放的文字</p>\n</div></li><li><span class='pre'>minPoint</span> : vec2<div class='sub-desc'><p>文字实体包围盒的最小值(世界坐标系)</p>\n</div></li><li><span class='pre'>maxPoint</span> : vec2<div class='sub-desc'><p>文字实体包围盒的最大值(世界坐标系)</p>\n</div></li></ul></div></li></ul></div></li></ul></div></div></div><div id='method-highlight' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-highlight' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-highlight' class='name expandable'>highlight</a>( <span class='pre'>handles, light</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>高亮 ...</div><div class='long'><p>高亮</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>handles</span> : String[]<div class='sub-desc'><p>需要高亮的实体标识集合</p>\n</div></li><li><span class='pre'>light</span> : boolean<div class='sub-desc'><p>是否高亮</p>\n</div></li></ul></div></div></div><div id='method-loadFile' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-loadFile' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-loadFile' class='name expandable'>loadFile</a>( <span class='pre'>url, callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>加载dwg图纸 ...</div><div class='long'><p>加载dwg图纸</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>url</span> : String<div class='sub-desc'><p>所加载图纸的地址，支持\"http://...../x.dwg\"和\"file:///.../x.dwg\"格式</p>\n</div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>加载图纸回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>加载失败，指示错误信息(system_error、NetworkError、DataError)，加载成功返回空</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-loadLabelTemplate' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-loadLabelTemplate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-loadLabelTemplate' class='name expandable'>loadLabelTemplate</a>( <span class='pre'>url, callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>加载标签 ...</div><div class='long'><p>加载标签</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>url</span> : String<div class='sub-desc'><p>标签的下载地址，支持\"http://...../x.dwg\"和\"file:///.../x.dwg\"格式</p>\n</div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>加载标签回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>加载失败，指示错误信息\n                    err {NetworkError}      标签下载失败\n                    err {DataError}         标签数据出错</p>\n</div></li><li><span class='pre'>handle</span> : string<div class='sub-desc'><p>加载成功，返回标签</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-releaseEffect' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-releaseEffect' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-releaseEffect' class='name expandable'>releaseEffect</a>( <span class='pre'>effects</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>释放效果资源 ...</div><div class='long'><p>释放效果资源</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>effects</span> : <a href=\"#!/api/ViewPID.Effect\" rel=\"ViewPID.Effect\" class=\"docClass\">ViewPID.Effect</a>[]<div class='sub-desc'><p>待释放的效果资源集合</p>\n</div></li></ul></div></div></div><div id='method-removeEffect' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-removeEffect' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-removeEffect' class='name expandable'>removeEffect</a>( <span class='pre'>effect</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>删除效果 ...</div><div class='long'><p>删除效果</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>effect</span> : <a href=\"#!/api/ViewPID.Effect\" rel=\"ViewPID.Effect\" class=\"docClass\">ViewPID.Effect</a><div class='sub-desc'><p>效果</p>\n</div></li></ul></div></div></div><div id='method-removeEntities' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-removeEntities' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-removeEntities' class='name expandable'>removeEntities</a>( <span class='pre'>entities</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>删除实体 ...</div><div class='long'><p>删除实体</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>entities</span> : string[]<div class='sub-desc'><p>删除实体的集合</p>\n</div></li></ul></div></div></div><div id='method-removeLabel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-removeLabel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-removeLabel' class='name expandable'>removeLabel</a>( <span class='pre'>label</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>删除标签 ...</div><div class='long'><p>删除标签</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>label</span> : string<div class='sub-desc'><p>删除标签</p>\n</div></li></ul></div></div></div><div id='method-screenToWorld' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-screenToWorld' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-screenToWorld' class='name expandable'>screenToWorld</a>( <span class='pre'>screenPoints, callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>二维显示窗口的窗口坐标转世界坐标 ...</div><div class='long'><p>二维显示窗口的窗口坐标转世界坐标</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>screenPoints</span> : vec2[]<div class='sub-desc'><p>窗口屏幕坐标</p>\n</div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>Error</span> : Error<div class='sub-desc'><p>错误信息</p>\n</div></li><li><span class='pre'>worldPoints</span> : vec2[]<div class='sub-desc'><p>返回窗口屏幕坐标对应的世界坐标</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-setBackgroundColor' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-setBackgroundColor' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-setBackgroundColor' class='name expandable'>setBackgroundColor</a>( <span class='pre'>color</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>设置图纸背景色\n需要注意的点是：本次图纸浏览的背景色需要在加载图纸之前设置 ...</div><div class='long'><p>设置图纸背景色\n需要注意的点是：本次图纸浏览的背景色需要在加载图纸之前设置</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>color</span> : Color<div class='sub-desc'><p>目标颜色</p>\n</div></li></ul></div></div></div><div id='method-setMouseHoverHandler' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-setMouseHoverHandler' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-setMouseHoverHandler' class='name expandable'>setMouseHoverHandler</a>( <span class='pre'>hoverHandler, [option]</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>设置鼠标悬停操作交互 ...</div><div class='long'><p>设置鼠标悬停操作交互</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>hoverHandler</span> : Function<div class='sub-desc'><p>当鼠标悬停或悬停结束都会触发此回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>交互过程中发生错误</p>\n</div></li><li><span class='pre'>msg</span> : Object<div class='sub-desc'><p>鼠标悬停交互结果信息</p>\n<ul><li><span class='pre'>handles</span> : String[]<div class='sub-desc'><p>选中的实体集合</p>\n</div></li><li><span class='pre'>mouseHoverState</span> : Number<div class='sub-desc'><p>0：标识鼠标开始悬停，1：标识鼠标结束悬停</p>\n</div></li><li><span class='pre'>worldPosition</span> : Vec2<div class='sub-desc'><p>鼠标悬停时世界坐标系下的坐标，结束悬停时坐标值无效</p>\n</div></li></ul></div></li></ul></div></li><li><span class='pre'>option</span> : Object (optional)<div class='sub-desc'><p>创建鼠标悬浮交互的参数</p>\n<ul><li><span class='pre'>hoverTime</span> : Number (optional)<div class='sub-desc'><p>鼠标悬停需要的时间(毫秒)</p>\n<p>Defaults to: <code>300</code></p></div></li></ul></div></li></ul></div></div></div><div id='method-setPickHandler' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-setPickHandler' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-setPickHandler' class='name expandable'>setPickHandler</a>( <span class='pre'>pickHandler, [hoverTime]</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>设置选中回调\n需要注意的点是：单击pickHandler只会回复一次交互结果；双击的情况下会先回两次单击交互结果再回一次双击交互结果 ...</div><div class='long'><p>设置选中回调\n需要注意的点是：单击pickHandler只会回复一次交互结果；双击的情况下会先回两次单击交互结果再回一次双击交互结果</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>pickHandler</span> : Function<div class='sub-desc'><p>当有选择操作（例如点选）发生时，回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>选择过程中发生错误</p>\n</div></li><li><span class='pre'>msg</span> : Object<div class='sub-desc'><p>选中的结果</p>\n<ul><li><span class='pre'>handles</span> : String[]<div class='sub-desc'><p>选中的实体集合</p>\n</div></li><li><span class='pre'>pickMethod</span> : string<div class='sub-desc'><p>标识鼠标操作的类型（pointPick/boxPick/RButtonPick/LButtonDBLCLK）</p>\n</div></li><li><span class='pre'>x</span> : double<div class='sub-desc'><p>选中点的x坐标（世界坐标系下的x坐标）</p>\n</div></li><li><span class='pre'>y</span> : double<div class='sub-desc'><p>选中点的y坐标（世界坐标系下的y坐标）</p>\n</div></li></ul></div></li></ul></div></li><li><span class='pre'>hoverTime</span> : Number (optional)<div class='sub-desc'><p>框选时鼠标悬停多久(毫秒)返回选中的结果</p>\n<p>Defaults to: <code>300</code></p></div></li></ul></div></div></div><div id='method-updateArcs' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-updateArcs' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-updateArcs' class='name expandable'>updateArcs</a>( <span class='pre'>option</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>更新弧线 ...</div><div class='long'><p>更新弧线</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : object<div class='sub-desc'>\n<ul><li><span class='pre'>arcs</span> : string[]<div class='sub-desc'><p>想要更新的arcs</p>\n</div></li><li><span class='pre'>pointPairs</span> : vec2[] (optional)<div class='sub-desc'><p>更新的弧线的点的个数为arcs个数的2倍，每两个对应arcs数组中一个的弧</p>\n</div></li><li><span class='pre'>style</span> : object (optional)<div class='sub-desc'><p>style的更新，同时传三个参数，不给值的参还原默认值。参照<a href=\"#!/api/ViewPID.Style.ArcStyle\" rel=\"ViewPID.Style.ArcStyle\" class=\"docClass\">ViewPID.Style.ArcStyle</a>的定义</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-worldToScreen' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-worldToScreen' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-worldToScreen' class='name expandable'>worldToScreen</a>( <span class='pre'>worldPoints, callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>世界坐标转二维显示窗口的窗口坐标 ...</div><div class='long'><p>世界坐标转二维显示窗口的窗口坐标</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>worldPoints</span> : vec3[]<div class='sub-desc'><p>世界坐标</p>\n</div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>Error</span> : Error<div class='sub-desc'><p>错误信息</p>\n</div></li><li><span class='pre'>screenPoints</span> : vec2[]<div class='sub-desc'><p>返回世界坐标对应的窗口屏幕坐标</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-zoomExtents' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.ViewPID'>ViewPID.ViewPID</span><br/><a href='source/ViewPID.html#ViewPID-ViewPID-method-zoomExtents' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.ViewPID-method-zoomExtents' class='name expandable'>zoomExtents</a>( <span class='pre'>[zoomMultiplier]</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>适应窗口大小 ...</div><div class='long'><p>适应窗口大小</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>zoomMultiplier</span> : Number (optional)<div class='sub-desc'><p>缩放倍数((0,1)表示缩小的倍数，(1,+∞)表示放大倍数，1表示整张图纸刚好充满显示区)</p>\n<p>Defaults to: <code>0.98</code></p></div></li></ul></div></div></div></div></div></div></div>","meta":{}});