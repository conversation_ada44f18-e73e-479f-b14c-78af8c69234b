Ext.data.JsonP.View3D_Filter({"tagname":"class","name":"View3D.Filter","autodetected":{},"files":[{"filename":"Filter.js","href":"Filter.html#View3D-Filter"}],"members":[],"alternateClassNames":[],"aliases":{},"id":"class-View3D.Filter","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Filter.html#View3D-Filter' target='_blank'>Filter.js</a></div></pre><div class='doc-contents'><p>交互Tracker</p>\n\n<p>应用不应该构建，应该通过InteractionFactory.createXXTracker创建得到</p>\n</div><div class='members'></div></div>","meta":{}});