Ext.data.JsonP.View3D_View3D({"tagname":"class","name":"View3D.View3D","autodetected":{},"files":[{"filename":"View3D.js","href":"View3D.html#View3D-View3D"}],"members":[{"name":"EffectFactory","tagname":"property","owner":"View3D.View3D","id":"property-EffectFactory","meta":{}},{"name":"InteractionFactory","tagname":"property","owner":"View3D.View3D","id":"property-InteractionFactory","meta":{}},{"name":"NodeFactory","tagname":"property","owner":"View3D.View3D","id":"property-NodeFactory","meta":{}},{"name":"RoamerFactory","tagname":"property","owner":"View3D.View3D","id":"property-RoamerFactory","meta":{}},{"name":"acquireScreenCrood","tagname":"method","owner":"View3D.View3D","id":"method-acquireScreenCrood","meta":{}},{"name":"acquireViewpoint","tagname":"method","owner":"View3D.View3D","id":"method-acquireViewpoint","meta":{}},{"name":"acquireVpMatrix","tagname":"method","owner":"View3D.View3D","id":"method-acquireVpMatrix","meta":{}},{"name":"addEffect","tagname":"method","owner":"View3D.View3D","id":"method-addEffect","meta":{}},{"name":"addNode","tagname":"method","owner":"View3D.View3D","id":"method-addNode","meta":{}},{"name":"calcAABB","tagname":"method","owner":"View3D.View3D","id":"method-calcAABB","meta":{}},{"name":"calcFocusedViewpointWithPoint","tagname":"method","owner":"View3D.View3D","id":"method-calcFocusedViewpointWithPoint","meta":{}},{"name":"calcNodesArea","tagname":"method","owner":"View3D.View3D","id":"method-calcNodesArea","meta":{}},{"name":"calcNodesFocusedViewpoint","tagname":"method","owner":"View3D.View3D","id":"method-calcNodesFocusedViewpoint","meta":{}},{"name":"calcNodesFocusedViewpointGroupByPosition","tagname":"method","owner":"View3D.View3D","id":"method-calcNodesFocusedViewpointGroupByPosition","meta":{}},{"name":"calculateCircularPoints","tagname":"method","owner":"View3D.View3D","id":"method-calculateCircularPoints","meta":{}},{"name":"captureScreenShot","tagname":"method","owner":"View3D.View3D","id":"method-captureScreenShot","meta":{}},{"name":"changeBackgroundColor","tagname":"method","owner":"View3D.View3D","id":"method-changeBackgroundColor","meta":{}},{"name":"coneCvtConvexPolyhedron","tagname":"method","owner":"View3D.View3D","id":"method-coneCvtConvexPolyhedron","meta":{}},{"name":"createAddEffectCommand","tagname":"method","owner":"View3D.View3D","id":"method-createAddEffectCommand","meta":{}},{"name":"createCompositeCommand","tagname":"method","owner":"View3D.View3D","id":"method-createCompositeCommand","meta":{}},{"name":"createRemoveEffectCommand","tagname":"method","owner":"View3D.View3D","id":"method-createRemoveEffectCommand","meta":{}},{"name":"displayNavigator","tagname":"method","owner":"View3D.View3D","id":"method-displayNavigator","meta":{}},{"name":"displaySkybox","tagname":"method","owner":"View3D.View3D","id":"method-displaySkybox","meta":{}},{"name":"executeCommand","tagname":"method","owner":"View3D.View3D","id":"method-executeCommand","meta":{}},{"name":"findChildren","tagname":"method","owner":"View3D.View3D","id":"method-findChildren","meta":{}},{"name":"findChildrenSet","tagname":"method","owner":"View3D.View3D","id":"method-findChildrenSet","meta":{}},{"name":"queryNodesAcrossPolygon","tagname":"method","owner":"View3D.View3D","id":"method-queryNodesAcrossPolygon","meta":{}},{"name":"queryNodesInBox","tagname":"method","owner":"View3D.View3D","id":"method-queryNodesInBox","meta":{}},{"name":"queryNodesInConvexPolyhedron","tagname":"method","owner":"View3D.View3D","id":"method-queryNodesInConvexPolyhedron","meta":{}},{"name":"registerFirstThirdPersonCallback","tagname":"method","owner":"View3D.View3D","id":"method-registerFirstThirdPersonCallback","meta":{}},{"name":"registerTrackLongCallback","tagname":"method","owner":"View3D.View3D","id":"method-registerTrackLongCallback","meta":{}},{"name":"registerVpMatrixChangedCallback","tagname":"method","owner":"View3D.View3D","id":"method-registerVpMatrixChangedCallback","meta":{}},{"name":"removeEffect","tagname":"method","owner":"View3D.View3D","id":"method-removeEffect","meta":{}},{"name":"removeNode","tagname":"method","owner":"View3D.View3D","id":"method-removeNode","meta":{}},{"name":"setFirstThirdPersonState","tagname":"method","owner":"View3D.View3D","id":"method-setFirstThirdPersonState","meta":{}},{"name":"setNearFarPlane","tagname":"method","owner":"View3D.View3D","id":"method-setNearFarPlane","meta":{}},{"name":"setPerformanceListener","tagname":"method","owner":"View3D.View3D","id":"method-setPerformanceListener","meta":{}},{"name":"setPictureQuality","tagname":"method","owner":"View3D.View3D","id":"method-setPictureQuality","meta":{}},{"name":"setTrackLongState","tagname":"method","owner":"View3D.View3D","id":"method-setTrackLongState","meta":{}},{"name":"swapInteraction","tagname":"method","owner":"View3D.View3D","id":"method-swapInteraction","meta":{}},{"name":"swapRoamer","tagname":"method","owner":"View3D.View3D","id":"method-swapRoamer","meta":{}},{"name":"swapRootNode","tagname":"method","owner":"View3D.View3D","id":"method-swapRootNode","meta":{}},{"name":"transformNodes","tagname":"method","owner":"View3D.View3D","id":"method-transformNodes","meta":{}},{"name":"translateViewpoint","tagname":"method","owner":"View3D.View3D","id":"method-translateViewpoint","meta":{}},{"name":"worldCoordToScreenCoord","tagname":"method","owner":"View3D.View3D","id":"method-worldCoordToScreenCoord","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-View3D.View3D","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/View3D.html#View3D-View3D' target='_blank'>View3D.js</a></div></pre><div class='doc-contents'><p>三维渲染场景</p>\n\n<p>创建节点工厂、创建效果工厂、创建交互工厂</p>\n\n<p>提供HTML与三维显示窗口的通信接口</p>\n\n<p>不应该直接创建，应该通过<a href=\"#!/api/Factory\" rel=\"Factory\" class=\"docClass\">Factory</a>提供的createView3D得到</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-EffectFactory' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-property-EffectFactory' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-property-EffectFactory' class='name expandable'>EffectFactory</a> : EffectFactory<span class=\"signature\"></span></div><div class='description'><div class='short'><p>效果创建工厂</p>\n</div><div class='long'><p>效果创建工厂</p>\n</div></div></div><div id='property-InteractionFactory' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-property-InteractionFactory' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-property-InteractionFactory' class='name expandable'>InteractionFactory</a> : InteractionFactory<span class=\"signature\"></span></div><div class='description'><div class='short'><p>交互创建工厂</p>\n</div><div class='long'><p>交互创建工厂</p>\n</div></div></div><div id='property-NodeFactory' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-property-NodeFactory' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-property-NodeFactory' class='name expandable'>NodeFactory</a> : NodeFactory<span class=\"signature\"></span></div><div class='description'><div class='short'><p>节点创建工厂</p>\n</div><div class='long'><p>节点创建工厂</p>\n</div></div></div><div id='property-RoamerFactory' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-property-RoamerFactory' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-property-RoamerFactory' class='name expandable'>RoamerFactory</a> : RoamerFactory<span class=\"signature\"></span></div><div class='description'><div class='short'><p>漫游创建工厂</p>\n</div><div class='long'><p>漫游创建工厂</p>\n</div></div></div></div></div><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div id='method-acquireScreenCrood' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-acquireScreenCrood' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-acquireScreenCrood' class='name expandable'>acquireScreenCrood</a>( <span class='pre'>worldCrood, callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>获取世界坐标对应的屏幕坐标 ...</div><div class='long'><p>获取世界坐标对应的屏幕坐标</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>worldCrood</span> : vec3<div class='sub-desc'><p>世界坐标</p>\n</div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>获取屏幕坐标完成候的回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>获取屏幕坐标失败返回错误</p>\n</div></li><li><span class='pre'>screencrood</span> : vec3<div class='sub-desc'><p>获取屏幕坐标成功返回屏幕坐标</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-acquireViewpoint' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-acquireViewpoint' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-acquireViewpoint' class='name expandable'>acquireViewpoint</a>( <span class='pre'>callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>获取当前视角 ...</div><div class='long'><p>获取当前视角</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>获取视角完成后的回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>获取视角失败返回错误</p>\n</div></li><li><span class='pre'>viewpoint</span> : Viewpoint<div class='sub-desc'><p>获取视角成功返回视角</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-acquireVpMatrix' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-acquireVpMatrix' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-acquireVpMatrix' class='name expandable'>acquireVpMatrix</a>( <span class='pre'>callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>获取VpMatrix三个视图相关的矩阵\n回调返回三个矩阵,再配合worldCoordToScreenCoord函数计算世界坐标对应的屏幕坐标 ...</div><div class='long'><p>获取VpMatrix三个视图相关的矩阵\n回调返回三个矩阵,再配合worldCoordToScreenCoord函数计算世界坐标对应的屏幕坐标</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>获取VPWMatrix完成后的回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>获取VPWMatrix失败返回错误</p>\n</div></li><li><span class='pre'>viewMatrix</span> : matrix<div class='sub-desc'><p>获取VPWMatrix成功返回viewMatrix</p>\n</div></li><li><span class='pre'>projectionMatrix</span> : matrix<div class='sub-desc'><p>获取VPWMatrix成功返回projectionMatrix</p>\n</div></li><li><span class='pre'>windowsMatrix</span> : matrix<div class='sub-desc'><p>获取VPWMatrix成功返回windowsMatrix</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-addEffect' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-addEffect' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-addEffect' class='name expandable'>addEffect</a>( <span class='pre'>effect</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>增加效果\n\n注意：只能对场景树中的节点调用此接口\n注意：要移除添加了Effect的节点前，应该先调用removeEffect接口，再调用removeNode接口 ...</div><div class='long'><p>增加效果</p>\n\n<p>注意：只能对场景树中的节点调用此接口\n注意：要移除添加了Effect的节点前，应该先调用removeEffect接口，再调用removeNode接口</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>effect</span> : <a href=\"#!/api/View3D.Effect\" rel=\"View3D.Effect\" class=\"docClass\">View3D.Effect</a><div class='sub-desc'><p>效果</p>\n</div></li></ul></div></div></div><div id='method-addNode' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-addNode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-addNode' class='name expandable'>addNode</a>( <span class='pre'>node, parentNode</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>增加节点 ...</div><div class='long'><p>增加节点</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>node</span> : Node<div class='sub-desc'><p>待增加的节点</p>\n</div></li><li><span class='pre'>parentNode</span> : Node<div class='sub-desc'><p>父节点</p>\n</div></li></ul></div></div></div><div id='method-calcAABB' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-calcAABB' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-calcAABB' class='name expandable'>calcAABB</a>( <span class='pre'>nodes, callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>计算一组节点的AABB包围盒\n注：只支持对当前加载的模型节点进行计算, 目前不支持对装置根节点计算AABB（就是nodes给一个drawingRoot）\n计算的是能够包围所有这组节点的包围盒，而不是分别求出各自的包围盒\nAABB相关解...</div><div class='long'><p>计算一组节点的AABB包围盒\n注：只支持对当前加载的模型节点进行计算, 目前不支持对装置根节点计算AABB（就是nodes给一个drawingRoot）\n计算的是能够包围所有这组节点的包围盒，而不是分别求出各自的包围盒\n<a href=\"https://baike.baidu.com/item/AABB%E7%9B%92/10087682?fr=aladdin\">AABB相关解释请参考</a></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>nodes</span> : Node[]<div class='sub-desc'><p>需要包围的一组节点</p>\n</div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>计算完成后的回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>返回错误\n                    err {InvalidArgumentError}          节点不支持计算包围盒（如有两个父节点）</p>\n</div></li><li><span class='pre'>aabb</span> : Object<div class='sub-desc'><p>计算结果</p>\n<ul><li><span class='pre'>min</span> : vec3<div class='sub-desc'><p>左下角坐标</p>\n</div></li><li><span class='pre'>max</span> : vec3<div class='sub-desc'><p>右上角坐标</p>\n</div></li></ul></div></li></ul></div></li></ul></div></div></div><div id='method-calcFocusedViewpointWithPoint' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-calcFocusedViewpointWithPoint' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-calcFocusedViewpointWithPoint' class='name expandable'>calcFocusedViewpointWithPoint</a>( <span class='pre'>option</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>计算定位到指定点的视角 ...</div><div class='long'><p>计算定位到指定点的视角</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'><p>计算定位视角参数</p>\n<ul><li><span class='pre'>center</span> : vec3<div class='sub-desc'><p>要求定位的中心点</p>\n</div></li><li><span class='pre'>node</span> : Node<div class='sub-desc'><p>需要计算视角的节点(出现为空的节点返回错误)</p>\n</div></li><li><span class='pre'>direction</span> : vec3 (optional)<div class='sub-desc'><p>要求视角的方向，若指定该参数将会按照指定的视角方向计算视角，否则选择最适合的视角方向计算视角</p>\n</div></li><li><span class='pre'>daRatio</span> : Number (optional)<div class='sub-desc'><p>以屏幕宽度或者高度的较小值为基数，乘以该百分比作为显示区域大小的边长（值：0&lt;daRate&lt;=1）传入0时会是默认值</p>\n<p>Defaults to: <code>0.6</code></p></div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>计算完成后回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : InvalidArgumentError<div class='sub-desc'><p>计算失败返回错误:节点集合中的节点存在一个节点有多个父节点，引发错误</p>\n</div></li><li><span class='pre'>viewpoint</span> : Viewpoint<div class='sub-desc'><p>返回计算的视角</p>\n</div></li></ul></div></li></ul></div></li></ul></div></div></div><div id='method-calcNodesArea' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-calcNodesArea' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-calcNodesArea' class='name expandable'>calcNodesArea</a>( <span class='pre'>nodes, callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>计算一组节点的面片面积和\n注：只支持对当前加载的模型节点进行计算, 目前不支持对装置根节点计算面积\n计算的是所有节点的面片面积和，不是表面积 ...</div><div class='long'><p>计算一组节点的面片面积和\n注：只支持对当前加载的模型节点进行计算, 目前不支持对装置根节点计算面积\n计算的是所有节点的面片面积和，不是表面积</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>nodes</span> : Node[]<div class='sub-desc'><p>需要计算的一组节点</p>\n</div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>计算完成后的回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>返回错误\n                    err {InvalidArgumentError}          节点不支持计算面积（如有两个父节点、DrawingRoot节点等）</p>\n</div></li><li><span class='pre'>area</span> : Number<div class='sub-desc'><p>计算结果，单位平方毫米</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-calcNodesFocusedViewpoint' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-calcNodesFocusedViewpoint' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-calcNodesFocusedViewpoint' class='name expandable'>calcNodesFocusedViewpoint</a>( <span class='pre'>option</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>计算定位到一组节点的视角 ...</div><div class='long'><p>计算定位到一组节点的视角</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'><p>计算定位视角参数</p>\n<ul><li><span class='pre'>nodes</span> : Node[]<div class='sub-desc'><p>需要计算视角的节点集合(出现为空的节点返回错误)</p>\n</div></li><li><span class='pre'>direction</span> : vec3 (optional)<div class='sub-desc'><p>要求视角的方向，若指定该参数将会按照指定的视角方向计算视角，否则选择最适合的视角方向计算视角</p>\n</div></li><li><span class='pre'>daRatio</span> : Number (optional)<div class='sub-desc'><p>以屏幕宽度或者高度的较小值为基数，乘以该百分比作为显示区域大小的边长（值：0&lt;daRate&lt;=1）传入0时会是默认值</p>\n<p>Defaults to: <code>0.6</code></p></div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>计算完成后回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : InvalidArgumentError<div class='sub-desc'><p>计算失败返回错误:节点集合中的节点存在一个节点有多个父节点，引发错误</p>\n</div></li><li><span class='pre'>viewpoint</span> : Viewpoint<div class='sub-desc'><p>返回计算的视角</p>\n</div></li></ul></div></li></ul></div></li></ul></div></div></div><div id='method-calcNodesFocusedViewpointGroupByPosition' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-calcNodesFocusedViewpointGroupByPosition' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-calcNodesFocusedViewpointGroupByPosition' class='name expandable'>calcNodesFocusedViewpointGroupByPosition</a>( <span class='pre'>option</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>通过空间位置将节点分组，并计算每一组节点的视点，返回的结果按照每一组的节点个数从多到少排序（当传入的drawingRootNode节点时，则对它包含的所有子节点进行分组计算视角） ...</div><div class='long'><p>通过空间位置将节点分组，并计算每一组节点的视点，返回的结果按照每一组的节点个数从多到少排序（当传入的drawingRootNode节点时，则对它包含的所有子节点进行分组计算视角）</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'><p>计算定位视角参数</p>\n<ul><li><span class='pre'>nodes</span> : Node[]<div class='sub-desc'><p>需要计算视角的节点集合(出现为空的节点返回错误)，节点类型必须相同，当为drawingRootNode节点时，则对它包含的所有子节点进行视角计算</p>\n</div></li><li><span class='pre'>direction</span> : vec3 (optional)<div class='sub-desc'><p>要求视角的方向，若指定该参数将会按照指定的视角方向计算视角，否则选择最适合的视角方向计算视角</p>\n</div></li><li><span class='pre'>daRatio</span> : Number (optional)<div class='sub-desc'><p>以屏幕宽度或者高度的较小值为基数，乘以该百分比作为显示区域大小的边长（值：0&lt;daRate&lt;=1）传入0时会是默认值</p>\n<p>Defaults to: <code>0.6</code></p></div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>计算完成后回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : InvalidArgumentError<div class='sub-desc'><p>计算失败返回错误:节点集合中的节点存在一个节点有多个父节点，引发错误</p>\n</div></li><li><span class='pre'>viewgroups</span> : Object[]<div class='sub-desc'><p>返回所有节点分组的视角</p>\n<ul><li><span class='pre'>viewpoint</span> : Viewpoint<div class='sub-desc'><p>返回计算的视角</p>\n</div></li><li><span class='pre'>weight</span> : Number<div class='sub-desc'><p>视角节点个数所占的权重</p>\n</div></li></ul></div></li></ul></div></li></ul></div></li></ul></div></div></div><div id='method-calculateCircularPoints' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-calculateCircularPoints' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-calculateCircularPoints' class='name expandable'>calculateCircularPoints</a>( <span class='pre'>option, callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>计算组成圆形的点的集合（圆形tracker和该接口组合使用时，二者pointNum需要一致） ...</div><div class='long'><p>计算组成圆形的点的集合（圆形tracker和该接口组合使用时，二者pointNum需要一致）</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'><p>需要的参数</p>\n<ul><li><span class='pre'>startPt</span> : vec3<div class='sub-desc'><p>起始点</p>\n</div></li><li><span class='pre'>endPt</span> : vec3<div class='sub-desc'><p>终点</p>\n</div></li><li><span class='pre'>pointNum</span> : Number<div class='sub-desc'><p>画圆的点数量（整数 3-65536）</p>\n</div></li></ul></div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>计算完成后的回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>求交失败返回错误</p>\n</div></li><li><span class='pre'>circularPointsArray</span> : vec3[]<div class='sub-desc'><p>计算结果数组</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-captureScreenShot' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-captureScreenShot' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-captureScreenShot' class='name expandable'>captureScreenShot</a>( <span class='pre'>callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>屏幕截图 ...</div><div class='long'><p>屏幕截图</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>获取屏幕截图完成后的回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>获取屏幕截图失败返回错误</p>\n</div></li><li><span class='pre'>screenShot</span> : String<div class='sub-desc'><p>获取屏幕截图成功返回截图数据</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-changeBackgroundColor' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-changeBackgroundColor' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-changeBackgroundColor' class='name expandable'>changeBackgroundColor</a>( <span class='pre'>color</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>改变场景的背景色 ...</div><div class='long'><p>改变场景的背景色</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>color</span> : Color<div class='sub-desc'><p>背景颜色，颜色中的alpha值无效</p>\n</div></li></ul></div></div></div><div id='method-coneCvtConvexPolyhedron' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-coneCvtConvexPolyhedron' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-coneCvtConvexPolyhedron' class='name expandable'>coneCvtConvexPolyhedron</a>( <span class='pre'>topPts, bottomPts</span> ) : Object[]<span class=\"signature\"></span></div><div class='description'><div class='short'>此接口用于圆坑点集合向凸多面体求交接口的对接 ...</div><div class='long'><p>此接口用于圆坑点集合向凸多面体求交接口的对接</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>topPts</span> : vec3[]<div class='sub-desc'><p>圆柱或圆台的上底点数组</p>\n</div></li><li><span class='pre'>bottomPts</span> : vec3[]<div class='sub-desc'><p>圆柱或圆台的下底点数组</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>Object[]</span><div class='sub-desc'><p>planesArray     返还用于queryNodesInConvexPolyhedron接口的数组</p>\n</div></li></ul></div></div></div><div id='method-createAddEffectCommand' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-createAddEffectCommand' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-createAddEffectCommand' class='name expandable'>createAddEffectCommand</a>( <span class='pre'>effect</span> ) : Command<span class=\"signature\"></span></div><div class='description'><div class='short'>创建增加效果命令 ...</div><div class='long'><p>创建增加效果命令</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>effect</span> : Effect<div class='sub-desc'><p>想要创建增加效果的命令。  备注：此接口创建的command，当移除Effect时，应使用createRemoveEffectCommand接口，不支持使用removeEffect操作</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>Command</span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createCompositeCommand' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-createCompositeCommand' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-createCompositeCommand' class='name expandable'>createCompositeCommand</a>( <span class='pre'>commands</span> ) : Command<span class=\"signature\"></span></div><div class='description'><div class='short'>创建组合命令 ...</div><div class='long'><p>创建组合命令</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>commands</span> : Command[]<div class='sub-desc'><p>创建组合命令 commands是按顺序执行的。</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>Command</span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createRemoveEffectCommand' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-createRemoveEffectCommand' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-createRemoveEffectCommand' class='name expandable'>createRemoveEffectCommand</a>( <span class='pre'>effect</span> ) : Command<span class=\"signature\"></span></div><div class='description'><div class='short'>创建删除效果命令 ...</div><div class='long'><p>创建删除效果命令</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>effect</span> : Effect<div class='sub-desc'><p>想要创建删除效果的命令。   备注：此接口和createAddEffectCommand接口对应使用,\n                           createAddEffectCommand创建的command被应用后，若要移除Effect，则必须用此接口，不支持使用removeEffect操作！</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>Command</span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-displayNavigator' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-displayNavigator' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-displayNavigator' class='name expandable'>displayNavigator</a>( <span class='pre'>[option]</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>显示导航立方体，该接口直接调用就可以，不需要调用addNode接口挂在指定节点下\n详细请参考 ...</div><div class='long'><p>显示导航立方体，该接口直接调用就可以，不需要调用addNode接口挂在指定节点下\n<a href=\"https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/136446059/2019-06-25+BRS\">详细请参考</a></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object (optional)<div class='sub-desc'><p>显示导航立方体参数</p>\n<ul><li><span class='pre'>offset</span> : vec2 (optional)<div class='sub-desc'><p>导航立方体的位置参数，第一个值为正（负）表示模型中央与窗口左侧（右侧）的像素偏移量，第二个值为正（负）表示模型中央与窗口上（下）侧的像素偏移量</p>\n<p>Defaults to: <code>vec2(-90, 80)</code></p></div></li><li><span class='pre'>scale</span> : double (optional)<div class='sub-desc'><p>自定义导航立方体的大小，默认值为立方体边长为150像素</p>\n<p>Defaults to: <code>150</code></p></div></li><li><span class='pre'>isDisplay</span> : Boolean (optional)<div class='sub-desc'><p>是否显示导航立方体，默认值为显示</p>\n<p>Defaults to: <code>true</code></p></div></li></ul></div></li></ul></div></div></div><div id='method-displaySkybox' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-displaySkybox' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-displaySkybox' class='name expandable'>displaySkybox</a>( <span class='pre'>[isDisplay]</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>显示天空盒，该接口直接调用就可以，不需要调用addNode接口 ...</div><div class='long'><p>显示天空盒，该接口直接调用就可以，不需要调用addNode接口</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>isDisplay</span> : Boolean (optional)<div class='sub-desc'><p>是否显示天空盒，默认值为显示</p>\n<p>Defaults to: <code>true</code></p></div></li></ul></div></div></div><div id='method-executeCommand' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-executeCommand' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-executeCommand' class='name expandable'>executeCommand</a>( <span class='pre'>command</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>执行Command命令 ...</div><div class='long'><p>执行Command命令</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>command</span> : Command<div class='sub-desc'><p>需要执行的Command</p>\n</div></li></ul></div></div></div><div id='method-findChildren' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-findChildren' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-findChildren' class='name expandable'>findChildren</a>( <span class='pre'>node, names, callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>查询指定名称的子孙节点\n\n注：当存在多个DrawingRoot时，为了实现最佳性能，建议依次传入每个DrawingRoot及其names进行查找\n注：当找不到node下所查询的名字时(名字错误或传入二维handle)，该接口会返回空值。 ...</div><div class='long'><p>查询指定名称的子孙节点</p>\n\n<p>注：当存在多个DrawingRoot时，为了实现最佳性能，建议依次传入每个DrawingRoot及其names进行查找<br/>\n注：当找不到node下所查询的名字时(名字错误或传入二维handle)，该接口会返回空值。<br/></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>node</span> : Node<div class='sub-desc'><p>父节点（该节点必须是DrawingRootNode）</p>\n</div></li><li><span class='pre'>names</span> : String[]<div class='sub-desc'><p>待查找节点的名字，要求名字不能重复，重复的名字在外部事先过滤</p>\n</div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>返回查询结果</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>error</span> : Error<div class='sub-desc'><p>错误</p>\n</div></li><li><span class='pre'>nodes</span> : Node[]<div class='sub-desc'><p>查找到的节点集合，如果某个名字对应的节点没有找到，对应位置返回null</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-findChildrenSet' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-findChildrenSet' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-findChildrenSet' class='name expandable'>findChildrenSet</a>( <span class='pre'>node, option, callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>查询指定名称的子孙节点，并返回节点集合\n\n注：得到的集合节点可以施加透明、隐藏、变色效果，可以用作排除节点、计算视角的传入参数，除以上应用外其余效果不支持\n注：当存在多个DrawingRoot时，为了实现最佳性能，建议依次传入每个Dr...</div><div class='long'><p>查询指定名称的子孙节点，并返回节点集合</p>\n\n<p>注：得到的集合节点可以施加透明、隐藏、变色效果，可以用作排除节点、计算视角的传入参数，除以上应用外其余效果不支持<br/>\n注：当存在多个DrawingRoot时，为了实现最佳性能，建议依次传入每个DrawingRoot及其names进行查找<br/>\n注：当找不到node下所查询的名字时(名字错误或传入二维handle)，该接口会返回空值。<br/></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>node</span> : Node<div class='sub-desc'><p>父节点（该节点必须是DrawingRootNode）</p>\n</div></li><li><span class='pre'>option</span> : Object<div class='sub-desc'><p>传入参数</p>\n<ul><li><span class='pre'>useHandleSign</span> : Boolean (optional)<div class='sub-desc'><p>是否使用handle标记，若为true使用带handle标记的url查询需要传入url、header；若为false使用节点名字集合查询，需要传入names</p>\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>names</span> : String[] (optional)<div class='sub-desc'><p>待查找节点的名字，要求名字不能重复，重复的名字在外部事先过滤</p>\n</div></li><li><span class='pre'>url</span> : String (optional)<div class='sub-desc'><p>带handle标记的url，例如：http://10.1.1.151:443/AIMS/handle/signVerify/38kmh25lp25ug</p>\n</div></li><li><span class='pre'>header</span> : String (optional)<div class='sub-desc'><p>头信息，如果使用Cookie则传入例如：Cookie: FULONGTECH_SESSION=XXX\n                                                        如果使用token则传入例如：Authorization-UAMS: XXX</p>\n</div></li></ul></div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>返回查询结果</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>error</span> : Error<div class='sub-desc'><p>错误</p>\n</div></li><li><span class='pre'>nodesSet</span> : Node<div class='sub-desc'><p>查找到的节点集合</p>\n</div></li><li><span class='pre'>warning</span> : String<div class='sub-desc'><p>警告</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-queryNodesAcrossPolygon' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-queryNodesAcrossPolygon' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-queryNodesAcrossPolygon' class='name expandable'>queryNodesAcrossPolygon</a>( <span class='pre'>option</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>计算一个有边界面与指定节点的相交结果\n只会对可见节点求交，可见节点是已加载并且没有被隐藏的节点 ...</div><div class='long'><p>计算一个有边界面与指定节点的相交结果\n只会对可见节点求交，可见节点是已加载并且没有被隐藏的节点</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'><p>求交需要的参数</p>\n<ul><li><span class='pre'>polygon</span> : vec3[]<div class='sub-desc'><p>一个平面上的闭合边界点组（必须是凸多边形的边界点）</p>\n</div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>计算完成后的回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>返回错误\n                    err {InvalidArgumentError}          polygon不满足要求</p>\n</div></li><li><span class='pre'>results</span> : Object[]<div class='sub-desc'><p>求交结果</p>\n<ul><li><span class='pre'>nodePath</span> : Node[]<div class='sub-desc'><p>求交到的节点路径，即从被选中的节点到根节点的所有节点按序排列</p>\n</div></li><li><span class='pre'>polyline</span> : vec3[]<div class='sub-desc'><p>交到的点集合</p>\n</div></li></ul></div></li></ul></div></li></ul></div></li></ul></div></div></div><div id='method-queryNodesInBox' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-queryNodesInBox' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-queryNodesInBox' class='name expandable'>queryNodesInBox</a>( <span class='pre'>option, callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>得到box范围内的节点以及节点在其中所占比例及面积 ...</div><div class='long'><p>得到box范围内的节点以及节点在其中所占比例及面积</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'><p>box求交参数</p>\n<ul><li><span class='pre'>boxPoints</span> : vec3[]<div class='sub-desc'><p>Box八个顶点（按照先底面后顶面，每个面的点为连续（顺时针或逆时针），上下底面对应）</p>\n</div></li></ul></div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>计算完成后的回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>求交失败返回错误</p>\n</div></li><li><span class='pre'>picked</span> : Object[]<div class='sub-desc'><p>计算结果</p>\n<ul><li><span class='pre'>scale</span> : Number<div class='sub-desc'><p>节点在所选区域中的部分占整体的比例</p>\n</div></li><li><span class='pre'>area</span> : Number<div class='sub-desc'><p>节点在所选区域中的部分占的面积</p>\n</div></li><li><span class='pre'>nodePath</span> : Node[]<div class='sub-desc'><p>选中的节点路径，即从被选中的节点到根节点的所有节点按序排列</p>\n</div></li></ul></div></li></ul></div></li></ul></div></div></div><div id='method-queryNodesInConvexPolyhedron' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-queryNodesInConvexPolyhedron' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-queryNodesInConvexPolyhedron' class='name expandable'>queryNodesInConvexPolyhedron</a>( <span class='pre'>planeArray, callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>计算凸多面体范围内的节点以及节点在其中所占比例及面积（最大支持32个面数量的求交） ...</div><div class='long'><p>计算凸多面体范围内的节点以及节点在其中所占比例及面积（最大支持32个面数量的求交）</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>planeArray</span> : Object[]<div class='sub-desc'><p>面存储面的数组</p>\n<ul><li><span class='pre'>plane</span> : Object<div class='sub-desc'><p>面对象</p>\n<ul><li><span class='pre'>points</span> : vec3[]<div class='sub-desc'><p>组成面的点</p>\n</div></li><li><span class='pre'>normal</span> : vec3[]<div class='sub-desc'><p>该面的法线（取该面三个点，三个点的存放顺序决定这个面的法线，顺序遵循右手定理）</p>\n</div></li></ul></div></li></ul></div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>计算完成后的回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>求交失败返回错误</p>\n</div></li><li><span class='pre'>picked</span> : Object[]<div class='sub-desc'><p>计算结果</p>\n<ul><li><span class='pre'>scale</span> : Number<div class='sub-desc'><p>节点在所选区域中的部分占整体的比例</p>\n</div></li><li><span class='pre'>area</span> : Number<div class='sub-desc'><p>节点在所选区域中的部分占的面积</p>\n</div></li><li><span class='pre'>nodePath</span> : Node[]<div class='sub-desc'><p>选中的节点路径，即从被选中的节点到根节点的所有节点按序排列</p>\n</div></li></ul></div></li></ul></div></li></ul></div></div></div><div id='method-registerFirstThirdPersonCallback' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-registerFirstThirdPersonCallback' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-registerFirstThirdPersonCallback' class='name expandable'>registerFirstThirdPersonCallback</a>( <span class='pre'>callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>注册一三人称漫游回调函数 ...</div><div class='long'><p>注册一三人称漫游回调函数</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>获取一三人称漫游的回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>获取回调失败返回的错误</p>\n</div></li><li><span class='pre'>isFirstPersonMode</span> : boolean<div class='sub-desc'><p>获取回调成功返回是否是第一人称漫游模式</p>\n</div></li><li><span class='pre'>moveVelocity</span> : Number<div class='sub-desc'><p>获取回调成功返回水平移动速度，单位：毫米/秒</p>\n</div></li><li><span class='pre'>canCollision</span> : boolean<div class='sub-desc'><p>获取回调成功返回是否允许水平碰撞</p>\n</div></li><li><span class='pre'>position</span> : vec3<div class='sub-desc'><p>获取回调成功返回人物脚下位置，世界坐标系，单位：毫米</p>\n</div></li><li><span class='pre'>direction</span> : vec3<div class='sub-desc'><p>获取回调成功返回视口朝向，世界坐标系</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-registerTrackLongCallback' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-registerTrackLongCallback' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-registerTrackLongCallback' class='name expandable'>registerTrackLongCallback</a>( <span class='pre'>callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>注册循线状态改变后回调函数 ...</div><div class='long'><p>注册循线状态改变后回调函数</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>获取循线状态改变完成后的回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>获取回调失败返回的错误</p>\n</div></li><li><span class='pre'>moveState</span> : Number<div class='sub-desc'><p>获取回调成功返回运动状态（0:无效，1:暂停，2:前进，3:后退）</p>\n</div></li><li><span class='pre'>forwardDir</span> : vec3<div class='sub-desc'><p>获取回调成功返回前进方向向量</p>\n</div></li><li><span class='pre'>windowPoint</span> : vec2<div class='sub-desc'><p>获取回调成功返回运动参考点窗口坐标</p>\n</div></li><li><span class='pre'>movePoint</span> : vec3<div class='sub-desc'><p>获取回调成功返回运动参考点三维坐标</p>\n</div></li><li><span class='pre'>speed</span> : Number<div class='sub-desc'><p>获取回调成功返回运动速度（mm/帧）</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-registerVpMatrixChangedCallback' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-registerVpMatrixChangedCallback' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-registerVpMatrixChangedCallback' class='name expandable'>registerVpMatrixChangedCallback</a>( <span class='pre'>callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>注册VpMatrix改变后回调函数\n回调返回三个矩阵,再配合worldCoordToScreenCoord函数计算世界坐标对应的屏幕坐标 ...</div><div class='long'><p>注册VpMatrix改变后回调函数\n回调返回三个矩阵,再配合worldCoordToScreenCoord函数计算世界坐标对应的屏幕坐标</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>获取VPWMatrix完成后的回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>获取VPWMatrix失败返回错误</p>\n</div></li><li><span class='pre'>viewMatrix</span> : matrix<div class='sub-desc'><p>获取VPWMatrix成功返回viewMatrix</p>\n</div></li><li><span class='pre'>projectionMatrix</span> : matrix<div class='sub-desc'><p>获取VPWMatrix成功返回projectionMatrix</p>\n</div></li><li><span class='pre'>windowsMatrix</span> : matrix<div class='sub-desc'><p>获取VPWMatrix成功返回windowsMatrix</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-removeEffect' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-removeEffect' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-removeEffect' class='name expandable'>removeEffect</a>( <span class='pre'>effect</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>删除效果 ...</div><div class='long'><p>删除效果</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>effect</span> : <a href=\"#!/api/View3D.Effect\" rel=\"View3D.Effect\" class=\"docClass\">View3D.Effect</a><div class='sub-desc'><p>效果</p>\n</div></li></ul></div></div></div><div id='method-removeNode' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-removeNode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-removeNode' class='name expandable'>removeNode</a>( <span class='pre'>node, parentNode</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>删除节点\n\n只能对没有effect的节点生效 ...</div><div class='long'><p>删除节点</p>\n\n<p>只能对没有effect的节点生效</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>node</span> : Node<div class='sub-desc'><p>待删除的节点</p>\n</div></li><li><span class='pre'>parentNode</span> : Node<div class='sub-desc'><p>父节点</p>\n</div></li></ul></div></div></div><div id='method-setFirstThirdPersonState' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-setFirstThirdPersonState' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-setFirstThirdPersonState' class='name expandable'>setFirstThirdPersonState</a>( <span class='pre'>option</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>设置一三人称漫游状态 ...</div><div class='long'><p>设置一三人称漫游状态</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'><p>需要的参数</p>\n<ul><li><span class='pre'>mode</span> : String (optional)<div class='sub-desc'><p>设置漫游器人称模式：\"firstPerson\" 第一人称、\"thirdPerson\" 第三人称</p>\n</div></li><li><span class='pre'>moveVelocity</span> : Number (optional)<div class='sub-desc'><p>设置水平移动速度，单位：毫米/秒，需要大于零，推荐小于20000毫米/秒，超出可能出现穿模现象</p>\n</div></li><li><span class='pre'>canCollision</span> : boolean (optional)<div class='sub-desc'><p>设置是否允许水平碰撞</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-setNearFarPlane' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-setNearFarPlane' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-setNearFarPlane' class='name expandable'>setNearFarPlane</a>( <span class='pre'>option</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>设置远近裁剪面 ...</div><div class='long'><p>设置远近裁剪面</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'><p>设置远近裁剪面参数</p>\n<ul><li><span class='pre'>computeNearFarMode</span> : Number (optional)<div class='sub-desc'><p>设置远近裁剪面模式\n包括ComputeNearFarMode.PrecisionMode=0 精确模式, ComputeNearFarMode.FastMode=1 快速模式,\nComputeNearFarMode.FixedMode=2 固定模式, 2 可以指定远近裁剪面\nFastMode可以解决某些视角下漫游会卡的问题，但可能导致模型闪面</p>\n<p>Defaults to: <code>2</code></p></div></li><li><span class='pre'>nearPlane</span> : Number<div class='sub-desc'><p>近裁剪面值(单位：毫米)</p>\n</div></li><li><span class='pre'>farPlane</span> : Number<div class='sub-desc'><p>远裁剪面值(单位：毫米)</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-setPerformanceListener' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-setPerformanceListener' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-setPerformanceListener' class='name expandable'>setPerformanceListener</a>( <span class='pre'>performanceListener</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>设置性能监听器\n当用户漫游浏览三维区域的时候按照固定频率通知当前传输性能 ...</div><div class='long'><p>设置性能监听器\n当用户漫游浏览三维区域的时候按照固定频率通知当前传输性能</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>performanceListener</span> : Object<div class='sub-desc'><p>性能监听器</p>\n<ul><li><span class='pre'>interval</span> : Number (optional)<div class='sub-desc'><p>通知的频率（x秒/次），默认为5.0，可以带小数，值要求大于0，推荐不低于1，不高于10</p>\n</div></li><li><span class='pre'>notifier</span> : Functcion<div class='sub-desc'><p>性能情况通知回调</p>\n<ul><li><span class='pre'>info</span> : Object<div class='sub-desc'><p>性能信息</p>\n<ul><li><span class='pre'>imageTransferDelay</span> : Number<div class='sub-desc'><p>图片传输延时(单位：ms)\n                            --图片传输延时低于20ms认为是流畅的\n                            --图片传输延时低于60ms认为可用\n                            --图片传输延时大于60ms认为不可用为true，代表当前用户正在漫游浏览三维，反之则否</p>\n</div></li></ul></div></li></ul></div></li></ul></div></li></ul></div></div></div><div id='method-setPictureQuality' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-setPictureQuality' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-setPictureQuality' class='name expandable'>setPictureQuality</a>( <span class='pre'>[option]</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>设置画质清晰度 ...</div><div class='long'><p>设置画质清晰度</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object (optional)<div class='sub-desc'>\n<ul><li><span class='pre'>staticQuality</span> : number (optional)<div class='sub-desc'><p>客户端不进行三维场景漫游操作（拖动，放大等操作）时画质的清晰度(0,100]\n                                            值越大画质清晰度越高占用带宽越高</p>\n<p>Defaults to: <code>100</code></p></div></li><li><span class='pre'>dynamicQuality</span> : number (optional)<div class='sub-desc'><p>客户端进行三维场景漫游操作（拖动，放大等操作）时画质的清晰度(0,100]\n                                            值越小画质清晰度越低占用带宽越低</p>\n<p>Defaults to: <code>80</code></p></div></li></ul></div></li></ul></div></div></div><div id='method-setTrackLongState' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-setTrackLongState' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-setTrackLongState' class='name expandable'>setTrackLongState</a>( <span class='pre'>option</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>设置循线状态 ...</div><div class='long'><p>设置循线状态</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'><p>需要的参数</p>\n<ul><li><span class='pre'>stateType</span> : Number<div class='sub-desc'><p>设置状态类型（\"1\":暂停，\"2\":前进，\"3\":后退，\"4\":设置速度）</p>\n</div></li><li><span class='pre'>speed</span> : Number<div class='sub-desc'><p>设置移动速度（当状态类型为\"4\"时起效，实数[0.0,500.0]，单位 mm/帧）</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-swapInteraction' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-swapInteraction' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-swapInteraction' class='name expandable'>swapInteraction</a>( <span class='pre'>interaction</span> ) : Interaction<span class=\"signature\"></span></div><div class='description'><div class='short'>交换交互操作 ...</div><div class='long'><p>交换交互操作</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>interaction</span> : Interaction<div class='sub-desc'><p>新的交互</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>Interaction</span><div class='sub-desc'><p>前一个交互结果</p>\n</div></li></ul></div></div></div><div id='method-swapRoamer' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-swapRoamer' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-swapRoamer' class='name expandable'>swapRoamer</a>( <span class='pre'>roamer, [KeepOldRoamerView]</span> ) : Roamer<span class=\"signature\"></span></div><div class='description'><div class='short'>设置漫游器 ...</div><div class='long'><p>设置漫游器</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>roamer</span> : Roamer<div class='sub-desc'><p>要设置的漫游器</p>\n</div></li><li><span class='pre'>KeepOldRoamerView</span> : boolean (optional)<div class='sub-desc'><p>是否保留原视角</p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>Roamer</span><div class='sub-desc'><p>返回之前的漫游器</p>\n</div></li></ul></div></div></div><div id='method-swapRootNode' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-swapRootNode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-swapRootNode' class='name expandable'>swapRootNode</a>( <span class='pre'>node</span> ) : Node<span class=\"signature\"></span></div><div class='description'><div class='short'>切换场景根节点 ...</div><div class='long'><p>切换场景根节点</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>node</span> : Node<div class='sub-desc'><p>新场景根节点，可以为空</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>Node</span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-transformNodes' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-transformNodes' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-transformNodes' class='name expandable'>transformNodes</a>( <span class='pre'>option</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>移动节点（该节点是MatrixTransform节点才支持移动）\n支持平移、旋转\n进行移动的所有点坐标信息，必须统一为世界坐标系坐标或局部坐标系坐标\n注意：当使用matrix进行移动节点的时候，如果此时的matrix是一个复合的mat...</div><div class='long'><p>移动节点（该节点是MatrixTransform节点才支持移动）\n支持平移、旋转\n进行移动的所有点坐标信息，必须统一为世界坐标系坐标或局部坐标系坐标\n注意：当使用matrix进行移动节点的时候，如果此时的matrix是一个复合的mat（有多个旋转、移动），且有移动时间时，\n此时的动画效果中间过程是无法预估的运动轨迹，但是始末位置都是对的。\n移动方式有两种：1.使用matrix进行节点移动， 2.使用translations或者rotation进行节点移动，二者必用其一。</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'><p>移动节点参数</p>\n<ul><li><span class='pre'>transformations</span> : Object[]<div class='sub-desc'><p>需要做的移动变换集合(同一组集合中要移动的节点不能重复)</p>\n<ul><li><span class='pre'>node</span> : Node<div class='sub-desc'><p>需要移动的节点</p>\n</div></li><li><span class='pre'>matrix</span> : mat4 (optional)<div class='sub-desc'><p>需要进行变换的矩阵信息（<a href=\"https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/75957738/VIC\">参考文档详见</a>）</p>\n</div></li><li><span class='pre'>isLocalCoordinate</span> : Boolean (optional)<div class='sub-desc'><p>移动信息坐标是否为局部坐标（默认处理方式为世界坐标）</p>\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>translation</span> : vec3 (optional)<div class='sub-desc'><p>需要将目标节点向某个方向移动的位置</p>\n</div></li><li><span class='pre'>rotation</span> : Object (optional)<div class='sub-desc'><p>目标节点旋转参数</p>\n<ul><li><span class='pre'>position</span> : vec3<div class='sub-desc'><p>旋转点</p>\n</div></li><li><span class='pre'>direction</span> : vec3<div class='sub-desc'><p>旋转轴方向</p>\n</div></li><li><span class='pre'>angle</span> : Number<div class='sub-desc'><p>旋转角度（角度大于零度，根据其旋转轴方向，使用右手法则，可确定模型旋转方向，负角度则相反）</p>\n</div></li></ul></div></li></ul></div></li><li><span class='pre'>duration</span> : Number (optional)<div class='sub-desc'><p>移动节点经过的时间（单位：毫秒）</p>\n<p>Defaults to: <code>0</code></p></div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>节点移动完成时回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : InvalidArgumentError<div class='sub-desc'><p>错误:某个节点是不可移动的</p>\n</div></li></ul></div></li></ul></div></li></ul></div></div></div><div id='method-translateViewpoint' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-translateViewpoint' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-translateViewpoint' class='name expandable'>translateViewpoint</a>( <span class='pre'>viewpoint, [time], [callback]</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>定位到某个视角 ...</div><div class='long'><p>定位到某个视角</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>viewpoint</span> : Viewpoint<div class='sub-desc'><p>需要定位到的视角</p>\n</div></li><li><span class='pre'>time</span> : Number (optional)<div class='sub-desc'><p>定位视角时移动的时间(单位：毫秒)，会形成动画效果（不给时间默认为0，会直接定位过去）</p>\n<p>Defaults to: <code>0.0</code></p></div></li><li><span class='pre'>callback</span> : Function (optional)<div class='sub-desc'><p>定位视角完成之后回调,不给回调会有默认值,如果在定位视角后还要进行切换漫游器的操作，必须写在回调里面，确保执行完定位视角再执行后续操作</p>\n</div></li></ul></div></div></div><div id='method-worldCoordToScreenCoord' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.View3D'>View3D.View3D</span><br/><a href='source/View3D.html#View3D-View3D-method-worldCoordToScreenCoord' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.View3D-method-worldCoordToScreenCoord' class='name expandable'>worldCoordToScreenCoord</a>( <span class='pre'>worldCrood, viewMatrix, projectionMatrix, windowsMatrix</span> ) : vec3<span class=\"signature\"></span></div><div class='description'><div class='short'>世界坐标转屏幕坐标 ...</div><div class='long'><p>世界坐标转屏幕坐标</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>worldCrood</span> : vec3<div class='sub-desc'><p>需要转换成屏幕坐标的世界坐标</p>\n</div></li><li><span class='pre'>viewMatrix</span> : mat4<div class='sub-desc'><p>视图变换矩阵,利用registerVpMatrixChangedCallback获取</p>\n</div></li><li><span class='pre'>projectionMatrix</span> : mat4<div class='sub-desc'><p>投影变换矩阵,利用registerVpMatrixChangedCallback获取</p>\n</div></li><li><span class='pre'>windowsMatrix</span> : mat4<div class='sub-desc'><p>窗口变换矩阵,利用registerVpMatrixChangedCallback获取</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>vec3</span><div class='sub-desc'><p>计算后的屏幕坐标,vec3[0] 为X坐标,vec3[1]为y坐标,vec3[2]为深度.深度值介于0~1之间,可以用于判断遮挡关系,深度值越小,离观察者越近,越大则越远</p>\n</div></li></ul></div></div></div></div></div></div></div>","meta":{}});