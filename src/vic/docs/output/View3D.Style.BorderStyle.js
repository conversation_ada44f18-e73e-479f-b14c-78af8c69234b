Ext.data.JsonP.View3D_Style_BorderStyle({"tagname":"class","name":"View3D.Style.BorderStyle","autodetected":{},"files":[{"filename":"Style.js","href":"Style.html#View3D-Style-BorderStyle"}],"members":[{"name":"color","tagname":"property","owner":"View3D.Style.BorderStyle","id":"property-color","meta":{}},{"name":"width","tagname":"property","owner":"View3D.Style.BorderStyle","id":"property-width","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-View3D.Style.BorderStyle","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Style.html#View3D-Style-BorderStyle' target='_blank'>Style.js</a></div></pre><div class='doc-contents'><p>标准边框属性定义</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-color' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.BorderStyle'>View3D.Style.BorderStyle</span><br/><a href='source/Style.html#View3D-Style-BorderStyle-property-color' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.BorderStyle-property-color' class='name expandable'>color</a> : Color<span class=\"signature\"></span></div><div class='description'><div class='short'>边框色，颜色中的alpha值有效 ...</div><div class='long'><p>边框色，颜色中的alpha值有效</p>\n<p>Defaults to: <code>black</code></p></div></div></div><div id='property-width' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.BorderStyle'>View3D.Style.BorderStyle</span><br/><a href='source/Style.html#View3D-Style-BorderStyle-property-width' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.BorderStyle-property-width' class='name expandable'>width</a> : Number<span class=\"signature\"></span></div><div class='description'><div class='short'>边框宽（像素）范围（1 ~ 10） ...</div><div class='long'><p>边框宽（像素）范围（1 ~ 10）</p>\n<p>Defaults to: <code>3</code></p></div></div></div></div></div></div></div>","meta":{}});