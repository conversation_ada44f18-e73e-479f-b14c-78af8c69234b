Ext.data.JsonP.View3D_NodeFactory({"tagname":"class","name":"View3D.NodeFactory","autodetected":{},"files":[{"filename":"NodeFactory.js","href":"NodeFactory.html#View3D-NodeFactory"}],"members":[{"name":"createAxis","tagname":"method","owner":"View3D.NodeFactory","id":"method-createAxis","meta":{}},{"name":"createBox","tagname":"method","owner":"View3D.NodeFactory","id":"method-createBox","meta":{}},{"name":"createCylinder","tagname":"method","owner":"View3D.NodeFactory","id":"method-createCylinder","meta":{}},{"name":"createExclusionNode","tagname":"method","owner":"View3D.NodeFactory","id":"method-createExclusionNode","meta":{}},{"name":"createExclusionNodeByNodesMark","tagname":"method","owner":"View3D.NodeFactory","id":"method-createExclusionNodeByNodesMark","meta":{}},{"name":"createFire","tagname":"method","owner":"View3D.NodeFactory","id":"method-createFire","meta":{}},{"name":"createFlowingModel","tagname":"method","owner":"View3D.NodeFactory","id":"method-createFlowingModel","meta":{}},{"name":"createGroup","tagname":"method","owner":"View3D.NodeFactory","id":"method-createGroup","meta":{}},{"name":"createLine","tagname":"method","owner":"View3D.NodeFactory","id":"method-createLine","meta":{}},{"name":"createParticalSystem","tagname":"method","owner":"View3D.NodeFactory","id":"method-createParticalSystem","meta":{}},{"name":"createPictureLabel","tagname":"method","owner":"View3D.NodeFactory","id":"method-createPictureLabel","meta":{}},{"name":"createPoint","tagname":"method","owner":"View3D.NodeFactory","id":"method-createPoint","meta":{}},{"name":"createPolygon","tagname":"method","owner":"View3D.NodeFactory","id":"method-createPolygon","meta":{}},{"name":"createSectorRevolution","tagname":"method","owner":"View3D.NodeFactory","id":"method-createSectorRevolution","meta":{}},{"name":"createSphere","tagname":"method","owner":"View3D.NodeFactory","id":"method-createSphere","meta":{}},{"name":"createSprayingWater","tagname":"method","owner":"View3D.NodeFactory","id":"method-createSprayingWater","meta":{}},{"name":"createTextLabel","tagname":"method","owner":"View3D.NodeFactory","id":"method-createTextLabel","meta":{}},{"name":"createTorus","tagname":"method","owner":"View3D.NodeFactory","id":"method-createTorus","meta":{}},{"name":"createTransformation","tagname":"method","owner":"View3D.NodeFactory","id":"method-createTransformation","meta":{}},{"name":"createUserplane","tagname":"method","owner":"View3D.NodeFactory","id":"method-createUserplane","meta":{}},{"name":"loadAnimationModel","tagname":"method","owner":"View3D.NodeFactory","id":"method-loadAnimationModel","meta":{}},{"name":"loadModel","tagname":"method","owner":"View3D.NodeFactory","id":"method-loadModel","meta":{}},{"name":"loadOGFFromROS","tagname":"method","owner":"View3D.NodeFactory","id":"method-loadOGFFromROS","meta":{}},{"name":"loadOGFModel","tagname":"method","owner":"View3D.NodeFactory","id":"method-loadOGFModel","meta":{}},{"name":"loadPGFModel","tagname":"method","owner":"View3D.NodeFactory","id":"method-loadPGFModel","meta":{}},{"name":"releaseNode","tagname":"method","owner":"View3D.NodeFactory","id":"method-releaseNode","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-View3D.NodeFactory","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/NodeFactory.html#View3D-NodeFactory' target='_blank'>NodeFactory.js</a></div></pre><div class='doc-contents'><p>三维节点工厂</p>\n\n<p>提供可创建、加载三维场景中Node的接口</p>\n\n<p>应用不应该构建，应该通过View3D直接获取</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div id='method-createAxis' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.NodeFactory'>View3D.NodeFactory</span><br/><a href='source/NodeFactory.html#View3D-NodeFactory-method-createAxis' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.NodeFactory-method-createAxis' class='name expandable'>createAxis</a>( <span class='pre'>option</span> ) : <a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建轴 ...</div><div class='long'><p>创建轴</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'><p>创建轴的参数</p>\n<ul><li><span class='pre'>position</span> : vec3<div class='sub-desc'><p>轴的创建位置</p>\n</div></li><li><span class='pre'>direction</span> : vec3<div class='sub-desc'><p>轴的创建方向</p>\n</div></li><li><span class='pre'>pickCallback</span> : Function<div class='sub-desc'><p>创建轴的回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>轴点击回调参数，内部异常，如果没有异常则为null</p>\n</div></li><li><span class='pre'>direction</span> : vec3<div class='sub-desc'><p>轴点击回调参数，方向为当前轴所代表的方向</p>\n</div></li></ul></div></li><li><span class='pre'>axisStyle</span> : Object (optional)<div class='sub-desc'><p>轴属性，参照<a href=\"#!/api/View3D.Style.AxisStyle\" rel=\"View3D.Style.AxisStyle\" class=\"docClass\">View3D.Style.AxisStyle</a>的定义</p>\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createBox' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.NodeFactory'>View3D.NodeFactory</span><br/><a href='source/NodeFactory.html#View3D-NodeFactory-method-createBox' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.NodeFactory-method-createBox' class='name expandable'>createBox</a>( <span class='pre'>option</span> ) : <a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建Box ...</div><div class='long'><p>创建Box</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'><p>创建Box参数</p>\n<ul><li><span class='pre'>points</span> : vec3[]<div class='sub-desc'><p>Box八个顶点（<a href=\"https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/83200479\">详细请参考</a>）</p>\n</div></li><li><span class='pre'>color</span> : COLOR (optional)<div class='sub-desc'><p>Box颜色(颜色中的alpha值无效)</p>\n<p>Defaults to: <code>COLOR('rgb(255,255,255)').alpha(1))</code></p></div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createCylinder' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.NodeFactory'>View3D.NodeFactory</span><br/><a href='source/NodeFactory.html#View3D-NodeFactory-method-createCylinder' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.NodeFactory-method-createCylinder' class='name expandable'>createCylinder</a>( <span class='pre'>option</span> ) : <a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建圆柱体\n详细请参考 ...</div><div class='long'><p>创建圆柱体\n<a href=\"https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/83200479\">详细请参考</a></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'><p>创建圆柱参数</p>\n<ul><li><span class='pre'>bottomCenter</span> : vec3<div class='sub-desc'><p>圆柱体底面中心</p>\n</div></li><li><span class='pre'>bottomRadius</span> : Number<div class='sub-desc'><p>圆柱体底面半径</p>\n</div></li><li><span class='pre'>bottomNormal</span> : vec3<div class='sub-desc'><p>圆柱体底面法向</p>\n</div></li><li><span class='pre'>topCenter</span> : vec3<div class='sub-desc'><p>圆柱体顶面中心</p>\n</div></li><li><span class='pre'>topRadius</span> : Number<div class='sub-desc'><p>圆柱体顶面半径</p>\n</div></li><li><span class='pre'>topNormal</span> : vec3<div class='sub-desc'><p>圆柱体顶面法向</p>\n</div></li><li><span class='pre'>color</span> : color (optional)<div class='sub-desc'><p>圆柱体颜色(颜色中的alpha值无效)</p>\n<p>Defaults to: <code>COLOR('rgb(255,255,255)').alpha(1)</code></p></div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createExclusionNode' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.NodeFactory'>View3D.NodeFactory</span><br/><a href='source/NodeFactory.html#View3D-NodeFactory-method-createExclusionNode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.NodeFactory-method-createExclusionNode' class='name expandable'>createExclusionNode</a>( <span class='pre'>root, [excludes]</span> ) : <a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建某个根节点(根节点必须是DrawingRootNode)下排除某一部分节点后的其它节点\n\n返回的node仅用于createColorChangeEffect、createHideEffect、createTransparencyE...</div><div class='long'><p>创建某个根节点(根节点必须是DrawingRootNode)下排除某一部分节点后的其它节点</p>\n\n<p>返回的node仅用于createColorChangeEffect、createHideEffect、createTransparencyEffect这三种effect中，若用于其他接口中不保证结果；</p>\n\n<p>注：该接口不支持PGF模型<br/></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>root</span> : Node<div class='sub-desc'><p>根节点</p>\n</div></li><li><span class='pre'>excludes</span> : Node[] (optional)<div class='sub-desc'><p>需要从根节点中排除的一组子节点</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createExclusionNodeByNodesMark' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.NodeFactory'>View3D.NodeFactory</span><br/><a href='source/NodeFactory.html#View3D-NodeFactory-method-createExclusionNodeByNodesMark' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.NodeFactory-method-createExclusionNodeByNodesMark' class='name expandable'>createExclusionNodeByNodesMark</a>( <span class='pre'>root, excludesMark</span> ) : <a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建某个根节点(根节点必须是DrawingRootNode)下排除某一部分节点后的其它节点\n返回的node仅用于createColorChangeEffect、createHideEffect、createTransparencyEf...</div><div class='long'><p>创建某个根节点(根节点必须是DrawingRootNode)下排除某一部分节点后的其它节点\n返回的node仅用于createColorChangeEffect、createHideEffect、createTransparencyEffect这三种effect中，若用于其他接口中不保证结果；\n注：该接口不支持PGF模型<br/></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>root</span> : Node<div class='sub-desc'><p>根节点</p>\n</div></li><li><span class='pre'>excludesMark</span> : String<div class='sub-desc'><p>需要从根节点中排除的子节点集合标记</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createFire' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.NodeFactory'>View3D.NodeFactory</span><br/><a href='source/NodeFactory.html#View3D-NodeFactory-method-createFire' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.NodeFactory-method-createFire' class='name expandable'>createFire</a>( <span class='pre'>[style]</span> ) : <a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建火 ...</div><div class='long'><p>创建火</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>style</span> : Object (optional)<div class='sub-desc'><p>创建火的参数</p>\n<ul><li><span class='pre'>size</span> : Number (optional)<div class='sub-desc'><p>火的大小</p>\n<p>Defaults to: <code>1000</code></p></div></li><li><span class='pre'>direction</span> : vec2 (optional)<div class='sub-desc'><p>风向（火、烟的偏移方向）</p>\n<p>Defaults to: <code>vec2(1, 0)</code></p></div></li><li><span class='pre'>angle</span> : Number (optional)<div class='sub-desc'><p>偏移角度（火、烟受风影响而偏移的角度）</p>\n<p>Defaults to: <code>0</code></p></div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createFlowingModel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.NodeFactory'>View3D.NodeFactory</span><br/><a href='source/NodeFactory.html#View3D-NodeFactory-method-createFlowingModel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.NodeFactory-method-createFlowingModel' class='name expandable'>createFlowingModel</a>( <span class='pre'>option</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>创建流动效果\n详细请参考 ...</div><div class='long'><p>创建流动效果\n<a href=\"https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/1504084703\">详细请参考</a></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'><p>创建流动效果参数</p>\n<ul><li><span class='pre'>vertexArray</span> : vec3[]<div class='sub-desc'><p>流动的路线，由一组点组成,流经管道的中心点</p>\n</div></li><li><span class='pre'>radius</span> : Number<div class='sub-desc'><p>水流的半径(要比管道半径小一些,推荐管道半径 * 0.8 即可)</p>\n</div></li><li><span class='pre'>speed</span> : Number<div class='sub-desc'><p>流动的速度  单位 m/s</p>\n</div></li><li><span class='pre'>textureImageName</span> : string<div class='sub-desc'><p>纹理图片的name, 放在BRS的Resources文件夹下,支持jpg\\png\\dds\\bmp格式</p>\n</div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>创建节点的回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>创建节点的回调参数，内部异常，如果没有异常则为null</p>\n</div></li><li><span class='pre'>node</span> : Node<div class='sub-desc'><p>创建节点的回调参数，返回创建的节点</p>\n</div></li></ul></div></li><li><span class='pre'>stateCallback</span> : Function<div class='sub-desc'><p>流动状态的回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>流动状态回调参数，内部异常，如果没有异常则为null</p>\n</div></li><li><span class='pre'>result</span> : Object<div class='sub-desc'><p>流动回调参数</p>\n<ul><li><span class='pre'>pos</span> : vec3<div class='sub-desc'><p>流动状态回调参数，当前流到的位置</p>\n</div></li><li><span class='pre'>flowing</span> : boolean<div class='sub-desc'><p>流动状态回调参数，是否正在流动，流动为true，流满为false</p>\n</div></li></ul></div></li></ul></div></li></ul></div></li></ul></div></div></div><div id='method-createGroup' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.NodeFactory'>View3D.NodeFactory</span><br/><a href='source/NodeFactory.html#View3D-NodeFactory-method-createGroup' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.NodeFactory-method-createGroup' class='name expandable'>createGroup</a>( <span class='pre'>[nodes], [name]</span> ) : <a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建Group节点 ...</div><div class='long'><p>创建Group节点</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>nodes</span> : Node[] (optional)<div class='sub-desc'><p>子节点集合</p>\n</div></li><li><span class='pre'>name</span> : String (optional)<div class='sub-desc'><p>节点名称</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createLine' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.NodeFactory'>View3D.NodeFactory</span><br/><a href='source/NodeFactory.html#View3D-NodeFactory-method-createLine' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.NodeFactory-method-createLine' class='name expandable'>createLine</a>( <span class='pre'>startPt, endPt, [lineStyle], [name]</span> ) : <a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建线段 ...</div><div class='long'><p>创建线段</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>startPt</span> : vec3<div class='sub-desc'><p>线段起点</p>\n</div></li><li><span class='pre'>endPt</span> : vec3<div class='sub-desc'><p>线段终点</p>\n</div></li><li><span class='pre'>lineStyle</span> : Object (optional)<div class='sub-desc'><p>线段属性，参照<a href=\"#!/api/View3D.Style.LineStyle\" rel=\"View3D.Style.LineStyle\" class=\"docClass\">View3D.Style.LineStyle</a>的定义</p>\n</div></li><li><span class='pre'>name</span> : String (optional)<div class='sub-desc'><p>节点名称</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createParticalSystem' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.NodeFactory'>View3D.NodeFactory</span><br/><a href='source/NodeFactory.html#View3D-NodeFactory-method-createParticalSystem' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.NodeFactory-method-createParticalSystem' class='name expandable'>createParticalSystem</a>( <span class='pre'>option</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>可配置粒子效果\n参考样例点这里\n参数具体信息说明 ...</div><div class='long'><p>可配置粒子效果\n<a href=\"https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/1407025332\">参考样例点这里</a>\n<a href=\"https://rzon.atlassian.net/wiki/spaces/~907676428/pages/1326481426\">参数具体信息说明</a></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'><p>创建粒子</p>\n<ul><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>创建粒子效果回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>error</span> : Error<div class='sub-desc'><p>返回错误信息\n                        err {DataError}                                         图片文件不存在</p>\n</div></li><li><span class='pre'>node</span> : Node<div class='sub-desc'><p>返回创建的粒子节点</p>\n</div></li></ul></div></li><li><span class='pre'>style</span> : Object<div class='sub-desc'><p>粒子参数</p>\n<ul><li><span class='pre'>direction</span> : vec3<div class='sub-desc'><p>发射方向速度矢量（单位：毫米/秒）</p>\n</div></li><li><span class='pre'>acceleration</span> : vec3<div class='sub-desc'><p>加速度矢量（单位：毫米/二次方秒）</p>\n</div></li><li><span class='pre'>angle</span> : Number<div class='sub-desc'><p>发射角（弧度制，范围[0,2π]），值为0时只向速度矢量方向发射，值为2π时向所有方向发射</p>\n</div></li><li><span class='pre'>size</span> : Number<div class='sub-desc'><p>单个粒子大小（单位：毫米）</p>\n</div></li><li><span class='pre'>flowRate</span> : Number<div class='sub-desc'><p>流量（单位：个/秒）</p>\n</div></li><li><span class='pre'>lifeTime</span> : Number<div class='sub-desc'><p>粒子的存活时间（单位：秒）</p>\n</div></li><li><span class='pre'>imageName</span> : String<div class='sub-desc'><p>图片文件名</p>\n</div></li><li><span class='pre'>alphaInterpolations</span> : Object[] (optional)<div class='sub-desc'><p>透明度插值</p>\n</div></li></ul></div></li></ul></div></li></ul></div></div></div><div id='method-createPictureLabel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.NodeFactory'>View3D.NodeFactory</span><br/><a href='source/NodeFactory.html#View3D-NodeFactory-method-createPictureLabel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.NodeFactory-method-createPictureLabel' class='name expandable'>createPictureLabel</a>( <span class='pre'>option</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>创建图片标签 ...</div><div class='long'><p>创建图片标签</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'><p>创建图片标签参数</p>\n<ul><li><span class='pre'>pictureUrl</span> : String<div class='sub-desc'><p>图片地址（支持“http://”地址和“file:///”格式）支持jpg、jpeg、bmp、tga、png图片格式</p>\n</div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>创建标签结束回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : NetworkError/DataError<div class='sub-desc'><p>创建过程出现错误时返回错误\n                                 err {NetworkError}                         图片下载失败\n                                err {DataError}                             图片数据出错</p>\n</div></li><li><span class='pre'>node</span> : Node<div class='sub-desc'><p>创建成功返回加载结果</p>\n</div></li></ul></div></li></ul></div></li></ul></div></div></div><div id='method-createPoint' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.NodeFactory'>View3D.NodeFactory</span><br/><a href='source/NodeFactory.html#View3D-NodeFactory-method-createPoint' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.NodeFactory-method-createPoint' class='name expandable'>createPoint</a>( <span class='pre'>point, [pointStyle], [name], [openDepthTest]</span> ) : <a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建点模型 ...</div><div class='long'><p>创建点模型</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>point</span> : vec3<div class='sub-desc'><p>要绘制的点</p>\n</div></li><li><span class='pre'>pointStyle</span> : Object (optional)<div class='sub-desc'><p>点属性，参照<a href=\"#!/api/View3D.Style.PointStyle\" rel=\"View3D.Style.PointStyle\" class=\"docClass\">View3D.Style.PointStyle</a>的定义</p>\n</div></li><li><span class='pre'>name</span> : String (optional)<div class='sub-desc'><p>节点名称</p>\n</div></li><li><span class='pre'>openDepthTest</span> : bool (optional)<div class='sub-desc'><p>开启深度测试，默认不开启</p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createPolygon' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.NodeFactory'>View3D.NodeFactory</span><br/><a href='source/NodeFactory.html#View3D-NodeFactory-method-createPolygon' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.NodeFactory-method-createPolygon' class='name expandable'>createPolygon</a>( <span class='pre'>option</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>创建多边形 ...</div><div class='long'><p>创建多边形</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'><p>创建多边形参数,支持创建凹凸多边形（创建带纹理的多边形必须要设置贴图url、pixelScale）</p>\n<ul><li><span class='pre'>points</span> : vec3[]<div class='sub-desc'><p>多边形各个点坐标（点序必须是顺时针或者逆时针）</p>\n</div></li><li><span class='pre'>color</span> : COLOR (optional)<div class='sub-desc'><p>多边形颜色(颜色中的alpha值无效)</p>\n<p>Defaults to: <code>COLOR('rgb(255,255,255)').alpha(1))</code></p></div></li><li><span class='pre'>url</span> : String (optional)<div class='sub-desc'><p>多边形贴图地址（支持“http://”地址和“file:///”格式）支持jpg、jpeg、bmp、tga、png图片格式</p>\n</div></li><li><span class='pre'>pixelScale</span> : Number (optional)<div class='sub-desc'><p>贴图像素点所占比例（贴图的一个像素点占实际图像的边长，eg：输入值为10，标识一个像素点代表实际图像边长10mm）</p>\n<p>Defaults to: <code>10</code></p></div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>创建多边形结束回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : NetworkError/DataError<div class='sub-desc'><p>加载过程出现错误时返回错误\n                                    err {NetworkError}                                      贴图文件下载失败\n                                    err {DataError}                                         贴图数据出错</p>\n</div></li><li><span class='pre'>node</span> : <a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a><div class='sub-desc'><p>创建成功返回模型节点</p>\n</div></li></ul></div></li></ul></div></li></ul></div></div></div><div id='method-createSectorRevolution' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.NodeFactory'>View3D.NodeFactory</span><br/><a href='source/NodeFactory.html#View3D-NodeFactory-method-createSectorRevolution' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.NodeFactory-method-createSectorRevolution' class='name expandable'>createSectorRevolution</a>( <span class='pre'>option</span> ) : <a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建扇形旋转体（球体的一部分或者球）\n先创建出一个xz平面的扇形，然后旋转一定角度得到旋转体 ...</div><div class='long'><p>创建扇形旋转体（球体的一部分或者球）\n先创建出一个xz平面的扇形，然后旋转一定角度得到旋转体</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'><p>创建扇形旋转体参数</p>\n<ul><li><span class='pre'>length</span> : Number<div class='sub-desc'><p>扇形边长（毫米）</p>\n</div></li><li><span class='pre'>sectorAngle</span> : Number<div class='sub-desc'><p>扇形角度</p>\n</div></li><li><span class='pre'>rotateAngle</span> : Number<div class='sub-desc'><p>扇形旋转的角度</p>\n</div></li><li><span class='pre'>direction</span> : vec3<div class='sub-desc'><p>扇形旋转体的朝向（圆心到曲面中心点的方向）</p>\n</div></li><li><span class='pre'>color</span> : COLOR (optional)<div class='sub-desc'><p>扇形旋转体颜色(颜色中的alpha值无效)</p>\n<p>Defaults to: <code>COLOR('rgb(255,255,255)').alpha(1)</code></p></div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createSphere' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.NodeFactory'>View3D.NodeFactory</span><br/><a href='source/NodeFactory.html#View3D-NodeFactory-method-createSphere' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.NodeFactory-method-createSphere' class='name expandable'>createSphere</a>( <span class='pre'>radius, color</span> ) : <a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建一个空心球体 ...</div><div class='long'><p>创建一个空心球体</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>radius</span> : Number<div class='sub-desc'><p>球体半径(单位：毫米)</p>\n</div></li><li><span class='pre'>color</span> : COLOR<div class='sub-desc'><p>球体颜色(颜色中的alpha值无效)</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createSprayingWater' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.NodeFactory'>View3D.NodeFactory</span><br/><a href='source/NodeFactory.html#View3D-NodeFactory-method-createSprayingWater' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.NodeFactory-method-createSprayingWater' class='name expandable'>createSprayingWater</a>( <span class='pre'>[style]</span> ) : <a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建消防喷水 ...</div><div class='long'><p>创建消防喷水</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>style</span> : Object (optional)<div class='sub-desc'><p>喷水参数</p>\n<ul><li><span class='pre'>size</span> : Number (optional)<div class='sub-desc'><p>水柱大小</p>\n<p>Defaults to: <code>1000</code></p></div></li><li><span class='pre'>direction</span> : vec3 (optional)<div class='sub-desc'><p>喷水方向</p>\n<p>Defaults to: <code>vec3(1, 0, 1)</code></p></div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createTextLabel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.NodeFactory'>View3D.NodeFactory</span><br/><a href='source/NodeFactory.html#View3D-NodeFactory-method-createTextLabel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.NodeFactory-method-createTextLabel' class='name expandable'>createTextLabel</a>( <span class='pre'>option</span> ) : <a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建文本批注 ...</div><div class='long'><p>创建文本批注</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'>\n<ul><li><span class='pre'>text</span> : String (optional)<div class='sub-desc'><p>批注文字，需自带换行（插入'\\n'）</p>\n<p>Defaults to: <code>&quot;&quot;</code></p></div></li><li><span class='pre'>pointerPoints</span> : vec2[] (optional)<div class='sub-desc'><p>连线点集合，为空或者未定义，则不能连线，给个点是一个vec2，表示屏幕坐标（单位：像素）</p>\n</div></li><li><span class='pre'>style</span> : Object (optional)<div class='sub-desc'><p>绘制属性，参照<a href=\"#!/api/View3D.Style.TextLabelStyle\" rel=\"View3D.Style.TextLabelStyle\" class=\"docClass\">View3D.Style.TextLabelStyle</a>的定义</p>\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createTorus' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.NodeFactory'>View3D.NodeFactory</span><br/><a href='source/NodeFactory.html#View3D-NodeFactory-method-createTorus' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.NodeFactory-method-createTorus' class='name expandable'>createTorus</a>( <span class='pre'>option</span> ) : <a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建圆形断面圆环\n详细请参考 ...</div><div class='long'><p>创建圆形断面圆环\n<a href=\"https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/83200479\">详细请参考</a></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'><p>创建圆环参数</p>\n<ul><li><span class='pre'>pt1</span> : vec3<div class='sub-desc'><p>起始圆形断面的中心</p>\n</div></li><li><span class='pre'>radius1</span> : Number<div class='sub-desc'><p>起始圆形断面的半径(>0)</p>\n</div></li><li><span class='pre'>pt2</span> : vec3<div class='sub-desc'><p>截止圆形断面的中心</p>\n</div></li><li><span class='pre'>radius2</span> : Number<div class='sub-desc'><p>截止圆形断面的半径(>0)</p>\n</div></li><li><span class='pre'>ptCenter</span> : vec3<div class='sub-desc'><p>圆环整体中心</p>\n</div></li><li><span class='pre'>direction</span> : Number<div class='sub-desc'><p>圆环的方向\n                                                                            Direction=0，表示小圆环，从P1到P2的小角度\n                                                                            Direction=1，表示大圆环，从P1到P2的大角度\n                                                                            Direction=2，表示整个圆环，此时，P2表示圆环法向量，此时R2无效，以R1为准\n                                                                            Direction=3，表示半个圆环，此时，P2表示圆环法向量，此时R2无效，以R1为准</p>\n</div></li><li><span class='pre'>color</span> : color (optional)<div class='sub-desc'><p>圆环颜色(颜色中的alpha值无效)</p>\n<p>Defaults to: <code>COLOR('rgb(255,255,255)').alpha(1)</code></p></div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createTransformation' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.NodeFactory'>View3D.NodeFactory</span><br/><a href='source/NodeFactory.html#View3D-NodeFactory-method-createTransformation' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.NodeFactory-method-createTransformation' class='name expandable'>createTransformation</a>( <span class='pre'>mat, [nodes], [name]</span> ) : <a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建MatrixTransform节点\n\n用于将其子节点进行位置的变换，如平移、旋转等 ...</div><div class='long'><p>创建MatrixTransform节点</p>\n\n<p>用于将其子节点进行位置的变换，如平移、旋转等</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>mat</span> : mat4<div class='sub-desc'><p>位置变换的矩阵表示</p>\n</div></li><li><span class='pre'>nodes</span> : Node[] (optional)<div class='sub-desc'><p>子节点的集合，集合中节点不能为空</p>\n</div></li><li><span class='pre'>name</span> : String (optional)<div class='sub-desc'><p>创建节点的名称</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createUserplane' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.NodeFactory'>View3D.NodeFactory</span><br/><a href='source/NodeFactory.html#View3D-NodeFactory-method-createUserplane' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.NodeFactory-method-createUserplane' class='name expandable'>createUserplane</a>( <span class='pre'>height, [name]</span> ) : <a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建用户平面 ...</div><div class='long'><p>创建用户平面</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>height</span> : Number<div class='sub-desc'><p>平面高度（毫米）</p>\n</div></li><li><span class='pre'>name</span> : String (optional)<div class='sub-desc'><p>平面的名称</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Node\" rel=\"View3D.Node\" class=\"docClass\">View3D.Node</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-loadAnimationModel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.NodeFactory'>View3D.NodeFactory</span><br/><a href='source/NodeFactory.html#View3D-NodeFactory-method-loadAnimationModel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.NodeFactory-method-loadAnimationModel' class='name expandable'>loadAnimationModel</a>( <span class='pre'>option</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>加载动画模型 ...</div><div class='long'><p>加载动画模型</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'><p>加载动画模型参数</p>\n<ul><li><span class='pre'>modelUrl</span> : String<div class='sub-desc'><p>模型文件地址（支持“http://”地址和“file:///”格式），模型文件支持格式：.fbx</p>\n</div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>加载动画模型结束回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : NetworkError/DataError<div class='sub-desc'><p>加载过程出现错误时返回错误\n                                 err {NetworkError}          模型文件下载失败\n                                err {DataError}             模型数据出错</p>\n</div></li><li><span class='pre'>node</span> : <a href=\"#!/api/View3D.AnimationNode\" rel=\"View3D.AnimationNode\" class=\"docClass\">View3D.AnimationNode</a><div class='sub-desc'><p>加载成功返回动画节点</p>\n</div></li></ul></div></li></ul></div></li></ul></div></div></div><div id='method-loadModel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.NodeFactory'>View3D.NodeFactory</span><br/><a href='source/NodeFactory.html#View3D-NodeFactory-method-loadModel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.NodeFactory-method-loadModel' class='name expandable'>loadModel</a>( <span class='pre'>option</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>加载自定义模型 ...</div><div class='long'><p>加载自定义模型</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'><p>加载自定义模型参数</p>\n<ul><li><span class='pre'>modelUrl</span> : String<div class='sub-desc'><p>模型文件地址（支持“http://”地址和“file:///”格式），模型文件支持格式：.osg、.ive、.3ds、.osgb,不支持倾斜摄影模型等有LOD层级结构的多个文件加载</p>\n</div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>加载自定义模型结束回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : NetworkError/DataError<div class='sub-desc'><p>加载过程出现错误时返回错误\n                                 err {NetworkError}          模型文件下载失败\n                                err {DataError}             模型数据出错</p>\n</div></li><li><span class='pre'>node</span> : Node<div class='sub-desc'><p>加载成功返回模型节点</p>\n</div></li></ul></div></li></ul></div></li></ul></div></div></div><div id='method-loadOGFFromROS' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.NodeFactory'>View3D.NodeFactory</span><br/><a href='source/NodeFactory.html#View3D-NodeFactory-method-loadOGFFromROS' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.NodeFactory-method-loadOGFFromROS' class='name expandable'>loadOGFFromROS</a>( <span class='pre'>options</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>该接口传入一个PGF模型，加载返回ROS优化后的OGF模型\n需要注意的是这个接口需要配合ROS使用，当与ROS的连接断开时，此接口是不能用的。 ...</div><div class='long'><p>该接口传入一个PGF模型，加载返回ROS优化后的OGF模型\n需要注意的是这个接口需要配合ROS使用，当与ROS的连接断开时，此接口是不能用的。</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : Object<div class='sub-desc'><p>模型加载参数</p>\n<ul><li><span class='pre'>fileName</span> : String<div class='sub-desc'><p>模型名字(PGF名字，带后缀，例：uuid.pgf)</p>\n</div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>加载结束回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : NetworkError/DataError<div class='sub-desc'><p>加载出错时候返回错误\n                                    err {NetworkError}                      模型下载失败\n                                    err {DataError}                         模型数据出错</p>\n</div></li><li><span class='pre'>node</span> : DrawingRootNode<div class='sub-desc'><p>加载成功返回加载结果</p>\n</div></li></ul></div></li><li><span class='pre'>progress</span> : Function (optional)<div class='sub-desc'><p>加载进度回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>current</span> : Number<div class='sub-desc'><p>当前加载进度 范围从0到1 0表示加载模型开始 1表示模型加载完成</p>\n</div></li></ul></div></li></ul></div></li></ul></div></div></div><div id='method-loadOGFModel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.NodeFactory'>View3D.NodeFactory</span><br/><a href='source/NodeFactory.html#View3D-NodeFactory-method-loadOGFModel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.NodeFactory-method-loadOGFModel' class='name expandable'>loadOGFModel</a>( <span class='pre'>options</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>加载OGF模型 ...</div><div class='long'><p>加载OGF模型</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : Object<div class='sub-desc'><p>模型加载参数</p>\n<ul><li><span class='pre'>url</span> : String<div class='sub-desc'><p>模型URL地址 支持“http://”地址和“file:///”格式的下载地址</p>\n</div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>加载结束回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : NetworkError/DataError<div class='sub-desc'><p>加载出错时候返回错误\n                                    err {NetworkError}                      模型下载失败\n                                    err {DataError}                         模型数据出错</p>\n</div></li><li><span class='pre'>node</span> : DrawingRootNode<div class='sub-desc'><p>加载成功返回加载结果</p>\n</div></li></ul></div></li><li><span class='pre'>progress</span> : Function (optional)<div class='sub-desc'><p>加载进度回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>current</span> : Number<div class='sub-desc'><p>当前加载进度 范围从0到1 0表示加载模型开始 1表示模型加载完成</p>\n</div></li></ul></div></li></ul></div></li></ul></div></div></div><div id='method-loadPGFModel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.NodeFactory'>View3D.NodeFactory</span><br/><a href='source/NodeFactory.html#View3D-NodeFactory-method-loadPGFModel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.NodeFactory-method-loadPGFModel' class='name expandable'>loadPGFModel</a>( <span class='pre'>options</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>加载PGF模型 ...</div><div class='long'><p>加载PGF模型</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : Object<div class='sub-desc'><p>模型加载参数</p>\n<ul><li><span class='pre'>url</span> : String<div class='sub-desc'><p>模型URL地址 支持“http://”地址和“file:///”格式的下载地址</p>\n</div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>加载结束回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : NetworkError/DataError<div class='sub-desc'><p>加载出错时候返回错误\n                                    err {NetworkError}                      模型下载失败\n                                    err {DataError}                         模型数据出错</p>\n</div></li><li><span class='pre'>node</span> : DrawingRootNode<div class='sub-desc'><p>加载成功返回加载结果</p>\n</div></li></ul></div></li><li><span class='pre'>progress</span> : Function (optional)<div class='sub-desc'><p>加载进度回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>current</span> : Number<div class='sub-desc'><p>当前加载进度 范围从0到1 0表示加载模型开始 1表示模型加载完成</p>\n</div></li></ul></div></li></ul></div></li></ul></div></div></div><div id='method-releaseNode' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.NodeFactory'>View3D.NodeFactory</span><br/><a href='source/NodeFactory.html#View3D-NodeFactory-method-releaseNode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.NodeFactory-method-releaseNode' class='name expandable'>releaseNode</a>( <span class='pre'>nodes</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>释放节点资源 ...</div><div class='long'><p>释放节点资源</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>nodes</span> : Node[]<div class='sub-desc'><p>待释放的节点资源集合</p>\n</div></li></ul></div></div></div></div></div></div></div>","meta":{}});