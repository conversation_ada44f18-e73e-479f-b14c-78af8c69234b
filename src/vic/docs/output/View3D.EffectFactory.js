Ext.data.JsonP.View3D_EffectFactory({"tagname":"class","name":"View3D.EffectFactory","autodetected":{},"files":[{"filename":"EffectFactory.js","href":"EffectFactory.html#View3D-EffectFactory"}],"members":[{"name":"create1DTranslationDragger","tagname":"method","owner":"View3D.EffectFactory","id":"method-create1DTranslationDragger","meta":{}},{"name":"createAxisTranslationDragger","tagname":"method","owner":"View3D.EffectFactory","id":"method-createAxisTranslationDragger","meta":{}},{"name":"createClipEffect","tagname":"method","owner":"View3D.EffectFactory","id":"method-createClipEffect","meta":{}},{"name":"createColorChangeEffect","tagname":"method","owner":"View3D.EffectFactory","id":"method-createColorChangeEffect","meta":{}},{"name":"createDraggableEffect","tagname":"method","owner":"View3D.EffectFactory","id":"method-createDraggableEffect","meta":{}},{"name":"createHideEffect","tagname":"method","owner":"View3D.EffectFactory","id":"method-createHideEffect","meta":{}},{"name":"createLightEffect","tagname":"method","owner":"View3D.EffectFactory","id":"method-createLightEffect","meta":{}},{"name":"createOpeningEffect","tagname":"method","owner":"View3D.EffectFactory","id":"method-createOpeningEffect","meta":{"deprecated":{"text":"<p>创建开口效果</p>\n"}}},{"name":"createOpeningPolygonEffect","tagname":"method","owner":"View3D.EffectFactory","id":"method-createOpeningPolygonEffect","meta":{}},{"name":"createOutlineEffect","tagname":"method","owner":"View3D.EffectFactory","id":"method-createOutlineEffect","meta":{}},{"name":"createPinEffect","tagname":"method","owner":"View3D.EffectFactory","id":"method-createPinEffect","meta":{}},{"name":"createRemoveIntersectionEffect","tagname":"method","owner":"View3D.EffectFactory","id":"method-createRemoveIntersectionEffect","meta":{}},{"name":"createTransparencyEffect","tagname":"method","owner":"View3D.EffectFactory","id":"method-createTransparencyEffect","meta":{}},{"name":"releaseEffect","tagname":"method","owner":"View3D.EffectFactory","id":"method-releaseEffect","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-View3D.EffectFactory","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/EffectFactory.html#View3D-EffectFactory' target='_blank'>EffectFactory.js</a></div></pre><div class='doc-contents'><p>提供创建不同<a href=\"#!/api/View3D.Effect\" rel=\"View3D.Effect\" class=\"docClass\">View3D.Effect</a>的接口</p>\n\n<p>应用不应该构建，应该通过View3D直接获取</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div id='method-create1DTranslationDragger' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.EffectFactory'>View3D.EffectFactory</span><br/><a href='source/EffectFactory.html#View3D-EffectFactory-method-create1DTranslationDragger' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.EffectFactory-method-create1DTranslationDragger' class='name expandable'>create1DTranslationDragger</a>( <span class='pre'>option</span> ) : <a href=\"#!/api/View3D.Dragger\" rel=\"View3D.Dragger\" class=\"docClass\">View3D.Dragger</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建单方向位移拖拽器，拖拽该拖拽器可以实现位置移动\n该拖拽器可用于创建可拖拽效果 ...</div><div class='long'><p>创建单方向位移拖拽器，拖拽该拖拽器可以实现位置移动\n该拖拽器可用于创建可拖拽效果</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'><p>创建拖拽器的参数</p>\n<ul><li><span class='pre'>pos</span> : vec3<div class='sub-desc'><p>拖拽控制器的初始位置</p>\n</div></li><li><span class='pre'>direction</span> : vec3<div class='sub-desc'><p>拖拽控制器的拖拽方向</p>\n</div></li><li><span class='pre'>dragDistance</span> : Object (optional)<div class='sub-desc'><p>可拖拽移动的距离:单位：毫米,取值[0, +∞)（以初始位置为起点,基于拖拽方向可向前向后拖拽的距离）</p>\n<ul><li><span class='pre'>forward</span> : Number<div class='sub-desc'><p>向前可拖拽的距离</p>\n</div></li><li><span class='pre'>backward</span> : Number<div class='sub-desc'><p>向后可拖拽的距离</p>\n</div></li></ul></div></li><li><span class='pre'>color</span> : Color<div class='sub-desc'><p>拖拽控制器的颜色（颜色中的alpha值无效）</p>\n</div></li><li><span class='pre'>dragCallback</span> : Function<div class='sub-desc'><p>拖拽回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>error</span> : Error<div class='sub-desc'><p>表示拖拽中出现的错误</p>\n</div></li><li><span class='pre'>type</span> : Number<div class='sub-desc'><p>表示当前拖拽回调的类型:1-一次拖拽完成; 2-拖拽过程中</p>\n</div></li><li><span class='pre'>pos</span> : vec3<div class='sub-desc'><p>表示当前拖拽器所在位置</p>\n</div></li></ul></div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Dragger\" rel=\"View3D.Dragger\" class=\"docClass\">View3D.Dragger</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createAxisTranslationDragger' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.EffectFactory'>View3D.EffectFactory</span><br/><a href='source/EffectFactory.html#View3D-EffectFactory-method-createAxisTranslationDragger' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.EffectFactory-method-createAxisTranslationDragger' class='name expandable'>createAxisTranslationDragger</a>( <span class='pre'>pos, dragCallback</span> ) : <a href=\"#!/api/View3D.Dragger\" rel=\"View3D.Dragger\" class=\"docClass\">View3D.Dragger</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建位移拖拽器，拖拽该拖拽器可以实现位置移动\n该拖拽器可用于创建可拖拽效果 ...</div><div class='long'><p>创建位移拖拽器，拖拽该拖拽器可以实现位置移动\n该拖拽器可用于创建可拖拽效果</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>pos</span> : vec3<div class='sub-desc'><p>拖拽控制器的初始位置</p>\n</div></li><li><span class='pre'>dragCallback</span> : Function<div class='sub-desc'><p>拖拽回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>error</span> : Error<div class='sub-desc'><p>表示拖拽中出现的错误</p>\n</div></li><li><span class='pre'>type</span> : Number<div class='sub-desc'><p>表示当前拖拽回调的类型:1-一次拖拽完成；2-拖拽过程中</p>\n</div></li><li><span class='pre'>pos</span> : vec3<div class='sub-desc'><p>表示当前拖拽器所在位置</p>\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Dragger\" rel=\"View3D.Dragger\" class=\"docClass\">View3D.Dragger</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createClipEffect' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.EffectFactory'>View3D.EffectFactory</span><br/><a href='source/EffectFactory.html#View3D-EffectFactory-method-createClipEffect' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.EffectFactory-method-createClipEffect' class='name expandable'>createClipEffect</a>( <span class='pre'>no, pos, destNodes, ctrlParam, dir, angle</span> ) : <a href=\"#!/api/View3D.Effect\" rel=\"View3D.Effect\" class=\"docClass\">View3D.Effect</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建剖切效果,有自由旋转和90°绕轴旋转两种\n\n整个场景（view）暂时仅支持六个剖切（对应的剖切号0~5） ...</div><div class='long'><p>创建剖切效果,有自由旋转和90°绕轴旋转两种</p>\n\n<p>整个场景（view）暂时仅支持六个剖切（对应的剖切号0~5）</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>no</span> : Number<div class='sub-desc'><p>剖切号（0~5），不能存在相同的剖切号</p>\n</div></li><li><span class='pre'>pos</span> : vec3<div class='sub-desc'><p>剖切面位置</p>\n</div></li><li><span class='pre'>destNodes</span> : Node[]<div class='sub-desc'><p>要剖切的节点数组（数组中的节点必须存在于场景中）</p>\n</div></li><li><span class='pre'>ctrlParam</span> : Object<div class='sub-desc'><p>剖切控制器参数</p>\n<ul><li><span class='pre'>scale</span> : Number (optional)<div class='sub-desc'><p>剖切控制器矩形大小，这是一个像素值</p>\n<p>Defaults to: <code>100.0</code></p></div></li><li><span class='pre'>showClipCtrl</span> : boolean (optional)<div class='sub-desc'><p>是否显示剖切控制器</p>\n<p>Defaults to: <code>true</code></p></div></li><li><span class='pre'>color</span> : Color (optional)<div class='sub-desc'><p>剖切控制器矩形的颜色，颜色中的alpha值有效</p>\n<p>Defaults to: <code>COLOR('rgb(0, 254, 255)').alpha(0.2)</code></p></div></li><li><span class='pre'>showBorder</span> : boolean (optional)<div class='sub-desc'><p>是否显示剖切控制器矩形的边框</p>\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>freeRotate</span> : boolean (optional)<div class='sub-desc'><p>是否可以自由角度旋转</p>\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>pickHandler</span> : Function<div class='sub-desc'><p>剖切控制器回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>error</span> : Error<div class='sub-desc'><p>剖切控制器回调参数，内部异常，如果没有异常则为null</p>\n</div></li><li><span class='pre'>position</span> : vec3<div class='sub-desc'><p>剖切控制器回调参数，当前剖切面位置</p>\n</div></li><li><span class='pre'>direction</span> : vec3<div class='sub-desc'><p>剖切控制器回调参数，当前剖切面法线方向</p>\n</div></li><li><span class='pre'>angle</span> : Number<div class='sub-desc'><p>剖切控制器回调参数，当前剖切器绕剖切面法线逆时针旋转角度</p>\n</div></li></ul></div></li></ul></div></li><li><span class='pre'>dir</span> : vec3<div class='sub-desc'><p>剖切面法线方向，以pos为点以dir为法线构造剖切面。以该剖切面为边界，dir方向一侧的模型正常显示，另一侧的模型则被剖切不显示。默认值为x轴正向。</p>\n<p>Defaults to: <code>Matrix.vec3.fromValues(1,0,0)</code></p></div></li><li><span class='pre'>angle</span> : Number<div class='sub-desc'><p>剖切控制器绕剖切面法线方向逆时针旋转的角度。默认值为0。</p>\n<p>Defaults to: <code>0</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Effect\" rel=\"View3D.Effect\" class=\"docClass\">View3D.Effect</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createColorChangeEffect' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.EffectFactory'>View3D.EffectFactory</span><br/><a href='source/EffectFactory.html#View3D-EffectFactory-method-createColorChangeEffect' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.EffectFactory-method-createColorChangeEffect' class='name expandable'>createColorChangeEffect</a>( <span class='pre'>nodes, color</span> ) : <a href=\"#!/api/View3D.Effect\" rel=\"View3D.Effect\" class=\"docClass\">View3D.Effect</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建变色效果\n\n注意:要求传入的数据不能有重复,如果有重复数据,该接口无法正常调用 ...</div><div class='long'><p>创建变色效果</p>\n\n<p>注意:要求传入的数据不能有重复,如果有重复数据,该接口无法正常调用<br/></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>nodes</span> : Node[]<div class='sub-desc'><p>要改变颜色的节点数组</p>\n</div></li><li><span class='pre'>color</span> : Color<div class='sub-desc'><p>目标颜色，颜色中的alpha值无效</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Effect\" rel=\"View3D.Effect\" class=\"docClass\">View3D.Effect</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createDraggableEffect' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.EffectFactory'>View3D.EffectFactory</span><br/><a href='source/EffectFactory.html#View3D-EffectFactory-method-createDraggableEffect' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.EffectFactory-method-createDraggableEffect' class='name expandable'>createDraggableEffect</a>( <span class='pre'>nodes, dragger</span> ) : <a href=\"#!/api/View3D.Effect\" rel=\"View3D.Effect\" class=\"docClass\">View3D.Effect</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建可拖拽效果\n创建时需要传入一个拖拽器，可以实现节点被这个拖拽的效果 ...</div><div class='long'><p>创建可拖拽效果\n创建时需要传入一个拖拽器，可以实现节点被这个拖拽的效果</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>nodes</span> : Node[]<div class='sub-desc'><p>应用可拖拽效果的节点集合,只有可变换的（transformable）节点才可以应用此效果\n                                    可变换节点的来源有：1.使用createTransformation接口创建的节点\n                                    2. 使用接口loadModel加载进来的自定义模型，用户非常确认此模型或者模型中的一部分是可变换的\n                                        3. 使用接口loadOGFModel加载进来的模型。4. 使用接口loadPGFModel加载进来的模型</p>\n</div></li><li><span class='pre'>dragger</span> : Dragger<div class='sub-desc'><p>拖拽器</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Effect\" rel=\"View3D.Effect\" class=\"docClass\">View3D.Effect</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createHideEffect' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.EffectFactory'>View3D.EffectFactory</span><br/><a href='source/EffectFactory.html#View3D-EffectFactory-method-createHideEffect' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.EffectFactory-method-createHideEffect' class='name expandable'>createHideEffect</a>( <span class='pre'>nodes</span> ) : <a href=\"#!/api/View3D.Effect\" rel=\"View3D.Effect\" class=\"docClass\">View3D.Effect</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建隐藏节点效果 ...</div><div class='long'><p>创建隐藏节点效果</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>nodes</span> : Node[]<div class='sub-desc'><p>目标节点集合</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Effect\" rel=\"View3D.Effect\" class=\"docClass\">View3D.Effect</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createLightEffect' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.EffectFactory'>View3D.EffectFactory</span><br/><a href='source/EffectFactory.html#View3D-EffectFactory-method-createLightEffect' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.EffectFactory-method-createLightEffect' class='name expandable'>createLightEffect</a>( <span class='pre'>option</span> ) : <a href=\"#!/api/View3D.LightEffect\" rel=\"View3D.LightEffect\" class=\"docClass\">View3D.LightEffect</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建光源 ...</div><div class='long'><p>创建光源</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'><p>创建光照参数</p>\n<ul><li><span class='pre'>lightStyle</span> : Object<div class='sub-desc'><p>光照基本属性，参照<a href=\"#!/api/View3D.Style.LightStyle\" rel=\"View3D.Style.LightStyle\" class=\"docClass\">View3D.Style.LightStyle</a>的定义</p>\n</div></li><li><span class='pre'>lightNo</span> : Number<div class='sub-desc'><p>光源编号</p>\n</div></li><li><span class='pre'>movingMode</span> : Object<div class='sub-desc'><p>光源移动方式</p>\n<ul><li><span class='pre'>mode</span> : Number<div class='sub-desc'><p>光源移动时，选择移动方式MOVING_MODE\n                                                                    { NoMoving: 0,FollowingCamera: 1}</p>\n</div></li><li><span class='pre'>radian</span> : Number (optional)<div class='sub-desc'><p><a href=\"https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/75956914\">参数相关详情请参考</a></p>\n<p>Defaults to: <code>π/4</code></p></div></li><li><span class='pre'>translate</span> : vec3 (optional)<div class='sub-desc'><p><a href=\"https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/75956914\">参数相关详情请参考</a></p>\n<p>Defaults to: <code>(0,0,0)</code></p></div></li></ul></div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.LightEffect\" rel=\"View3D.LightEffect\" class=\"docClass\">View3D.LightEffect</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createOpeningEffect' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.EffectFactory'>View3D.EffectFactory</span><br/><a href='source/EffectFactory.html#View3D-EffectFactory-method-createOpeningEffect' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.EffectFactory-method-createOpeningEffect' class='name expandable'>createOpeningEffect</a>( <span class='pre'>nodes, openingBoundary</span> ) : <a href=\"#!/api/View3D.Effect\" rel=\"View3D.Effect\" class=\"docClass\">View3D.Effect</a><span class=\"signature\"><span class='deprecated' >deprecated</span></span></div><div class='description'><div class='short'> ...</div><div class='long'>\n        <div class='rounded-box deprecated-box deprecated-tag-box'>\n        <p>This method has been <strong>deprected</strong> </p>\n        <p>创建开口效果</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>nodes</span> : Node[]<div class='sub-desc'><p>应用此开口效果的节点数组（目前节点只支持Drawing加载出的Node）</p>\n</div></li><li><span class='pre'>openingBoundary</span> : vec3[]<div class='sub-desc'><p>多边形各个点坐标（开口的区域依赖于传入的点序，点依次和下一个点之间构成开口区域的一条边，\n                                    最后一个点和第一个构成一条边）</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Effect\" rel=\"View3D.Effect\" class=\"docClass\">View3D.Effect</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createOpeningPolygonEffect' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.EffectFactory'>View3D.EffectFactory</span><br/><a href='source/EffectFactory.html#View3D-EffectFactory-method-createOpeningPolygonEffect' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.EffectFactory-method-createOpeningPolygonEffect' class='name expandable'>createOpeningPolygonEffect</a>( <span class='pre'>nodes, openingBoundary</span> ) : <a href=\"#!/api/View3D.Effect\" rel=\"View3D.Effect\" class=\"docClass\">View3D.Effect</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建开口效果 ...</div><div class='long'><p>创建开口效果</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>nodes</span> : Node[]<div class='sub-desc'><p>应用此开口效果的节点数组（目前节点只支持Drawing加载出的Node）</p>\n</div></li><li><span class='pre'>openingBoundary</span> : vec3[]<div class='sub-desc'><p>多边形各个点坐标（开口的区域依赖于传入的点序，点依次和下一个点之间构成开口区域的一条边，\n                                    最后一个点和第一个构成一条边）</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Effect\" rel=\"View3D.Effect\" class=\"docClass\">View3D.Effect</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createOutlineEffect' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.EffectFactory'>View3D.EffectFactory</span><br/><a href='source/EffectFactory.html#View3D-EffectFactory-method-createOutlineEffect' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.EffectFactory-method-createOutlineEffect' class='name expandable'>createOutlineEffect</a>( <span class='pre'>option</span> ) : view3D.Effect<span class=\"signature\"></span></div><div class='description'><div class='short'>创建node边框效果 ...</div><div class='long'><p>创建node边框效果</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'>\n<ul><li><span class='pre'>nodes</span> : Node[]<div class='sub-desc'><p>创建边框的目标节点（节点必须存在于场景中）</p>\n</div></li><li><span class='pre'>color</span> : Color (optional)<div class='sub-desc'><p>创建边框的颜色(备注：颜色中的alpha值无效)</p>\n<p>Defaults to: <code>(255, 0, 0)</code></p></div></li><li><span class='pre'>width</span> : Number (optional)<div class='sub-desc'><p>创建边框的宽度</p>\n<p>Defaults to: <code>5</code></p></div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>view3D.Effect</span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createPinEffect' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.EffectFactory'>View3D.EffectFactory</span><br/><a href='source/EffectFactory.html#View3D-EffectFactory-method-createPinEffect' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.EffectFactory-method-createPinEffect' class='name expandable'>createPinEffect</a>( <span class='pre'>option</span> ) : view3D.Effect<span class=\"signature\"></span></div><div class='description'><div class='short'>创建node钉住效果\n\n应用该效果，拉远视角查看模型，模型精度由高精度切换为低精度，所添加效果依然可见。 ...</div><div class='long'><p>创建node钉住效果</p>\n\n<p>应用该效果，拉远视角查看模型，模型精度由高精度切换为低精度，所添加效果依然可见。<br/></p>\n\n<p>应用场景实例：长管道模型添加高亮效果，拉远查看管道走向，会有部分模型由高精度切换为低精度，没有高亮效果，看起来存在断点。在添加高亮效果之前添加该效果，则不会出现上述问题。<br/></p>\n\n<p>注意：该接口只可传入loadOGFModel、loadOGFFromROS或loadPGFOptimizeResult接口返回的DrawingRootNode之下的节点。（调用其他接口添加节点到DrawingRootNode树结构中或从DrawingRootNode树结构中脱离节点，所操作节点都不能使用该效果）<br/>\n注意：出于对效率和内存占用考量，该接口一次传入节点数量应该控制在500之内，整个场景应用该效果的节点数不应超过10000，否则可能导致卡顿甚至程序崩溃。<br/></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'>\n<ul><li><span class='pre'>nodes</span> : Node[]<div class='sub-desc'><p>钉住的目标节点</p>\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>view3D.Effect</span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createRemoveIntersectionEffect' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.EffectFactory'>View3D.EffectFactory</span><br/><a href='source/EffectFactory.html#View3D-EffectFactory-method-createRemoveIntersectionEffect' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.EffectFactory-method-createRemoveIntersectionEffect' class='name expandable'>createRemoveIntersectionEffect</a>( <span class='pre'>nodes</span> ) : <a href=\"#!/api/View3D.Effect\" rel=\"View3D.Effect\" class=\"docClass\">View3D.Effect</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建去除交互能力 ...</div><div class='long'><p>创建去除交互能力</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>nodes</span> : Node[]<div class='sub-desc'><p>目标节点集合</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Effect\" rel=\"View3D.Effect\" class=\"docClass\">View3D.Effect</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createTransparencyEffect' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.EffectFactory'>View3D.EffectFactory</span><br/><a href='source/EffectFactory.html#View3D-EffectFactory-method-createTransparencyEffect' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.EffectFactory-method-createTransparencyEffect' class='name expandable'>createTransparencyEffect</a>( <span class='pre'>options</span> ) : <a href=\"#!/api/View3D.TransparencyEffect\" rel=\"View3D.TransparencyEffect\" class=\"docClass\">View3D.TransparencyEffect</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建透明节点效果\n注意：6.0.0起修正了透明度逻辑，修正后透明度参数（取值范围0~1）值越大，越透明 ...</div><div class='long'><p>创建透明节点效果\n注意：6.0.0起修正了透明度逻辑，修正后透明度参数（取值范围0~1）值越大，越透明</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : Object<div class='sub-desc'>\n<ul><li><span class='pre'>destNodes</span> : Node[]<div class='sub-desc'><p>目标节点集合</p>\n</div></li><li><span class='pre'>transparencyValue</span> : Number (optional)<div class='sub-desc'><p>透明度（范围0~1），0为不透明，1为全透明（值：0&lt;=transparencyValue&lt;=1）</p>\n<p>Defaults to: <code>1</code></p></div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.TransparencyEffect\" rel=\"View3D.TransparencyEffect\" class=\"docClass\">View3D.TransparencyEffect</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-releaseEffect' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.EffectFactory'>View3D.EffectFactory</span><br/><a href='source/EffectFactory.html#View3D-EffectFactory-method-releaseEffect' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.EffectFactory-method-releaseEffect' class='name expandable'>releaseEffect</a>( <span class='pre'>effects</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>释放效果资源 ...</div><div class='long'><p>释放效果资源</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>effects</span> : <a href=\"#!/api/View3D.Effect\" rel=\"View3D.Effect\" class=\"docClass\">View3D.Effect</a>[]<div class='sub-desc'><p>待释放的效果资源集合</p>\n</div></li></ul></div></div></div></div></div></div></div>","meta":{}});