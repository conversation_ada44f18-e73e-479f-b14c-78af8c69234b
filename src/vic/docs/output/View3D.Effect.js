Ext.data.JsonP.View3D_Effect({"tagname":"class","name":"View3D.Effect","autodetected":{},"files":[{"filename":"Effect.js","href":"Effect.html#View3D-Effect"}],"members":[],"alternateClassNames":[],"aliases":{},"id":"class-View3D.Effect","component":false,"superclasses":[],"subclasses":["View3D.ClipEffect","View3D.DraggableEffect","View3D.LightEffect","View3D.TransparencyEffect"],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Subclasses</h4><div class='dependency'><a href='#!/api/View3D.ClipEffect' rel='View3D.ClipEffect' class='docClass'>View3D.ClipEffect</a></div><div class='dependency'><a href='#!/api/View3D.DraggableEffect' rel='View3D.DraggableEffect' class='docClass'>View3D.DraggableEffect</a></div><div class='dependency'><a href='#!/api/View3D.LightEffect' rel='View3D.LightEffect' class='docClass'>View3D.LightEffect</a></div><div class='dependency'><a href='#!/api/View3D.TransparencyEffect' rel='View3D.TransparencyEffect' class='docClass'>View3D.TransparencyEffect</a></div><h4>Files</h4><div class='dependency'><a href='source/Effect.html#View3D-Effect' target='_blank'>Effect.js</a></div></pre><div class='doc-contents'><p>三维效果</p>\n</div><div class='members'></div></div>","meta":{}});