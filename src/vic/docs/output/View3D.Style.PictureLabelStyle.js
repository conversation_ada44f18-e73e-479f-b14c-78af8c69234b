Ext.data.JsonP.View3D_Style_PictureLabelStyle({"tagname":"class","name":"View3D.Style.PictureLabelStyle","autodetected":{},"files":[{"filename":"Style.js","href":"Style.html#View3D-Style-PictureLabelStyle"}],"members":[{"name":"fixSizeRange","tagname":"property","owner":"View3D.Style.PictureLabelStyle","id":"property-fixSizeRange","meta":{}},{"name":"width","tagname":"property","owner":"View3D.Style.PictureLabelStyle","id":"property-width","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-View3D.Style.PictureLabelStyle","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Style.html#View3D-Style-PictureLabelStyle' target='_blank'>Style.js</a></div></pre><div class='doc-contents'><p>标准图片标签属性定义</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-fixSizeRange' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.PictureLabelStyle'>View3D.Style.PictureLabelStyle</span><br/><a href='source/Style.html#View3D-Style-PictureLabelStyle-property-fixSizeRange' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.PictureLabelStyle-property-fixSizeRange' class='name expandable'>fixSizeRange</a> : Object<span class=\"signature\"></span></div><div class='description'><div class='short'>图像标签固定大小的显示范围，没有量纲，max应该大于min\n 图片在该范围内能够维持固定大小，否则不维持屏幕占比\n\n例如：fixSizeRange{0, 10}，即图像标签固定大小的显示范围为0-10 ...</div><div class='long'><p>图像标签固定大小的显示范围，没有量纲，max应该大于min\n 图片在该范围内能够维持固定大小，否则不维持屏幕占比</p>\n\n<p>例如：fixSizeRange{0, 10}，即图像标签固定大小的显示范围为0-10</p>\n<p>Defaults to: <code>{min: 0, max: 40.0}</code></p><ul><li><span class='pre'>min</span> : Number (optional)<div class='sub-desc'><p>最小值（取值范围[0,+∞)），取0代表图片会随着视点距离的靠近会无限缩小，一直维持在屏幕显示的大小不变</p>\n<p>Defaults to: <code>0</code></p></div></li><li><span class='pre'>max</span> : Number (optional)<div class='sub-desc'><p>最大值（取值范围(0,+∞)），默认值表示最大放大倍数为40倍，超过该倍数无法维持占屏幕大小不变</p>\n<p>Defaults to: <code>40.0</code></p></div></li></ul></div></div></div><div id='property-width' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.PictureLabelStyle'>View3D.Style.PictureLabelStyle</span><br/><a href='source/Style.html#View3D-Style-PictureLabelStyle-property-width' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.PictureLabelStyle-property-width' class='name expandable'>width</a> : Number<span class=\"signature\"></span></div><div class='description'><div class='short'>图像标签的宽度,高度大小会根据图像高宽比自动调整 ...</div><div class='long'><p>图像标签的宽度,高度大小会根据图像高宽比自动调整</p>\n<p>Defaults to: <code>48</code></p></div></div></div></div></div></div></div>","meta":{}});