Ext.data.JsonP.View3D_Style_PointStyle({"tagname":"class","name":"View3D.Style.PointStyle","autodetected":{},"files":[{"filename":"Style.js","href":"Style.html#View3D-Style-PointStyle"}],"members":[{"name":"color","tagname":"property","owner":"View3D.Style.PointStyle","id":"property-color","meta":{}},{"name":"size","tagname":"property","owner":"View3D.Style.PointStyle","id":"property-size","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-View3D.Style.PointStyle","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Style.html#View3D-Style-PointStyle' target='_blank'>Style.js</a></div></pre><div class='doc-contents'><p>标准点属性定义</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-color' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.PointStyle'>View3D.Style.PointStyle</span><br/><a href='source/Style.html#View3D-Style-PointStyle-property-color' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.PointStyle-property-color' class='name expandable'>color</a> : Color<span class=\"signature\"></span></div><div class='description'><div class='short'>点颜色，颜色中的alpha值无效 ...</div><div class='long'><p>点颜色，颜色中的alpha值无效</p>\n<p>Defaults to: <code>black</code></p></div></div></div><div id='property-size' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.PointStyle'>View3D.Style.PointStyle</span><br/><a href='source/Style.html#View3D-Style-PointStyle-property-size' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.PointStyle-property-size' class='name expandable'>size</a> : Number<span class=\"signature\"></span></div><div class='description'><div class='short'>点大小（像素）范围（大于0） ...</div><div class='long'><p>点大小（像素）范围（大于0）</p>\n<p>Defaults to: <code>4</code></p></div></div></div></div></div></div></div>","meta":{}});