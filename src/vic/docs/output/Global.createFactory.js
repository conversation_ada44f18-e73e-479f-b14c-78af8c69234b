Ext.data.JsonP.Global_createFactory({"tagname":"class","name":"Global.createFactory","alternateClassNames":[],"members":[{"name":"createFactory","tagname":"method","owner":"Global.createFactory","id":"method-createFactory","meta":{}}],"aliases":{},"files":[{"filename":"","href":""}],"component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><div class='doc-contents'>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div id='method-createFactory' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Global.createFactory'>Global.createFactory</span><br/><a href='source/Factory.html#Global-createFactory-method-createFactory' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Global.createFactory-method-createFactory' class='name expandable'>createFactory</a>( <span class='pre'>options, callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>创建服务器组件工厂 ...</div><div class='long'><p>创建服务器组件工厂</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : Object<div class='sub-desc'><p>创建工厂参数</p>\n<ul><li><span class='pre'>server</span> : Object<div class='sub-desc'><p>服务器连接信息</p>\n<ul><li><span class='pre'>useSSL</span> : Boolean (optional)<div class='sub-desc'><p>与服务器通信是否使用加密协议（HTTPS），默认为HTTP</p>\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>ip</span> : String<div class='sub-desc'><p>服务器IP地址</p>\n</div></li><li><span class='pre'>port</span> : String<div class='sub-desc'><p>服务器连接端口号</p>\n</div></li><li><span class='pre'>userName</span> : String (optional)<div class='sub-desc'><p>当前登录的用户名（BRS下载文件需要用户验证时需填）</p>\n</div></li><li><span class='pre'>language</span> : String (optional)<div class='sub-desc'><p>当前系统语言，默认中文\n                                                    （中文：zh_CN，英文：en_US，输入非中文默认都是英文）</p>\n</div></li></ul></div></li><li><span class='pre'>errorHandler</span> : Function<div class='sub-desc'><p>创建完成以后，服务器运行过程发生错误或者连接意外断开回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>发生错误时候传出错误\n                    err{NetworkError}               网络断开\n                        err{InvalidArgumentError}       调用方法时候，请求的某些对象不存在、请求的方法没有定义</p>\n</div></li></ul></div></li></ul></div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>创建完成回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>创建失败返回错误\n                    err{NetworkError}               网络连接失败\n                    err{VersioningError}            版本协商失败\n                    err{SystemError}                启动渲染服务失败</p>\n</div></li><li><span class='pre'>factory</span> : <a href=\"#!/api/Factory\" rel=\"Factory\" class=\"docClass\">Factory</a><div class='sub-desc'><p>创建成功返回工厂</p>\n</div></li></ul></div></li></ul></div></div></div></div></div></div></div>","meta":{}});