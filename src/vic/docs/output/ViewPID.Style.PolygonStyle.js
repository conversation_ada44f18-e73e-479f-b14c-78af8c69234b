Ext.data.JsonP.ViewPID_Style_PolygonStyle({"tagname":"class","name":"ViewPID.Style.PolygonStyle","autodetected":{},"files":[{"filename":"Style.js","href":"Style2.html#ViewPID-Style-PolygonStyle"}],"members":[{"name":"color","tagname":"property","owner":"ViewPID.Style.PolygonStyle","id":"property-color","meta":{}},{"name":"transparencyValue","tagname":"property","owner":"ViewPID.Style.PolygonStyle","id":"property-transparencyValue","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-ViewPID.Style.PolygonStyle","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Style2.html#ViewPID-Style-PolygonStyle' target='_blank'>Style.js</a></div></pre><div class='doc-contents'><p>标准画多边形属性定义</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-color' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.Style.PolygonStyle'>ViewPID.Style.PolygonStyle</span><br/><a href='source/Style2.html#ViewPID-Style-PolygonStyle-property-color' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.Style.PolygonStyle-property-color' class='name expandable'>color</a> : Color<span class=\"signature\"></span></div><div class='description'><div class='short'>多边形颜色 ...</div><div class='long'><p>多边形颜色</p>\n<p>Defaults to: <code>white</code></p></div></div></div><div id='property-transparencyValue' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.Style.PolygonStyle'>ViewPID.Style.PolygonStyle</span><br/><a href='source/Style2.html#ViewPID-Style-PolygonStyle-property-transparencyValue' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.Style.PolygonStyle-property-transparencyValue' class='name expandable'>transparencyValue</a> : Number<span class=\"signature\"></span></div><div class='description'><div class='short'>多边形透明度,透明度（范围0~1），0为不透明，1为全透明（值：0&lt;=transparencyValue&lt;=1） ...</div><div class='long'><p>多边形透明度,透明度（范围0~1），0为不透明，1为全透明（值：0&lt;=transparencyValue&lt;=1）</p>\n<p>Defaults to: <code>1.0</code></p></div></div></div></div></div></div></div>","meta":{}});