Ext.data.JsonP.View3D_Style_LightStyle({"tagname":"class","name":"View3D.Style.LightStyle","autodetected":{},"files":[{"filename":"Style.js","href":"Style.html#View3D-Style-LightStyle"}],"members":[{"name":"ambient","tagname":"property","owner":"View3D.Style.LightStyle","id":"property-ambient","meta":{}},{"name":"diffuse","tagname":"property","owner":"View3D.Style.LightStyle","id":"property-diffuse","meta":{}},{"name":"direction","tagname":"property","owner":"View3D.Style.LightStyle","id":"property-direction","meta":{}},{"name":"position","tagname":"property","owner":"View3D.Style.LightStyle","id":"property-position","meta":{}},{"name":"range","tagname":"property","owner":"View3D.Style.LightStyle","id":"property-range","meta":{}},{"name":"specular","tagname":"property","owner":"View3D.Style.LightStyle","id":"property-specular","meta":{}},{"name":"spotCutoff","tagname":"property","owner":"View3D.Style.LightStyle","id":"property-spotCutoff","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-View3D.Style.LightStyle","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Style.html#View3D-Style-LightStyle' target='_blank'>Style.js</a></div></pre><div class='doc-contents'><p>光属性定义  * <a href=\"https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/75956914\">参数相关详情请参考</a></p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-ambient' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.LightStyle'>View3D.Style.LightStyle</span><br/><a href='source/Style.html#View3D-Style-LightStyle-property-ambient' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.LightStyle-property-ambient' class='name expandable'>ambient</a> : Color<span class=\"signature\"></span></div><div class='description'><div class='short'>环境光颜色 ...</div><div class='long'><p>环境光颜色</p>\n<p>Defaults to: <code>black</code></p></div></div></div><div id='property-diffuse' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.LightStyle'>View3D.Style.LightStyle</span><br/><a href='source/Style.html#View3D-Style-LightStyle-property-diffuse' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.LightStyle-property-diffuse' class='name expandable'>diffuse</a> : Color<span class=\"signature\"></span></div><div class='description'><div class='short'>镜面光颜色 ...</div><div class='long'><p>镜面光颜色</p>\n<p>Defaults to: <code>black</code></p></div></div></div><div id='property-direction' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.LightStyle'>View3D.Style.LightStyle</span><br/><a href='source/Style.html#View3D-Style-LightStyle-property-direction' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.LightStyle-property-direction' class='name expandable'>direction</a> : vec3<span class=\"signature\"></span></div><div class='description'><div class='short'><p>光照方向</p>\n</div><div class='long'><p>光照方向</p>\n</div></div></div><div id='property-position' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.LightStyle'>View3D.Style.LightStyle</span><br/><a href='source/Style.html#View3D-Style-LightStyle-property-position' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.LightStyle-property-position' class='name expandable'>position</a> : vec4<span class=\"signature\"></span></div><div class='description'><div class='short'><p>光源位置（vec4的最后一位决定使用的是哪种光源）</p>\n</div><div class='long'><p>光源位置（vec4的最后一位决定使用的是哪种光源）</p>\n</div></div></div><div id='property-range' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.LightStyle'>View3D.Style.LightStyle</span><br/><a href='source/Style.html#View3D-Style-LightStyle-property-range' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.LightStyle-property-range' class='name expandable'>range</a> : Number<span class=\"signature\"></span></div><div class='description'><div class='short'>光照范围 ...</div><div class='long'><p>光照范围</p>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-specular' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.LightStyle'>View3D.Style.LightStyle</span><br/><a href='source/Style.html#View3D-Style-LightStyle-property-specular' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.LightStyle-property-specular' class='name expandable'>specular</a> : Color<span class=\"signature\"></span></div><div class='description'><div class='short'>漫射光颜色 ...</div><div class='long'><p>漫射光颜色</p>\n<p>Defaults to: <code>black</code></p></div></div></div><div id='property-spotCutoff' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.LightStyle'>View3D.Style.LightStyle</span><br/><a href='source/Style.html#View3D-Style-LightStyle-property-spotCutoff' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.LightStyle-property-spotCutoff' class='name expandable'>spotCutoff</a> : Number<span class=\"signature\"></span></div><div class='description'><div class='short'>聚光灯聚光角度 ...</div><div class='long'><p>聚光灯聚光角度</p>\n<p>Defaults to: <code>180</code></p></div></div></div></div></div></div></div>","meta":{}});