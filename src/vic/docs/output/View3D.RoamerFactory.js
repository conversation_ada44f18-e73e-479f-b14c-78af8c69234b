Ext.data.JsonP.View3D_RoamerFactory({"tagname":"class","name":"View3D.RoamerFactory","autodetected":{},"files":[{"filename":"RoamerFactory.js","href":"RoamerFactory.html#View3D-RoamerFactory"}],"members":[{"name":"createFirstAndThirdPersonRoamer","tagname":"method","owner":"View3D.RoamerFactory","id":"method-createFirstAndThirdPersonRoamer","meta":{}},{"name":"createFlyModeRoamer","tagname":"method","owner":"View3D.RoamerFactory","id":"method-createFlyModeRoamer","meta":{}},{"name":"createFreeModeRoamer","tagname":"method","owner":"View3D.RoamerFactory","id":"method-createFreeModeRoamer","meta":{}},{"name":"createTrackLongRoamer","tagname":"method","owner":"View3D.RoamerFactory","id":"method-createTrackLongRoamer","meta":{}},{"name":"releaseRoamers","tagname":"method","owner":"View3D.RoamerFactory","id":"method-releaseRoamers","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-View3D.RoamerFactory","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/RoamerFactory.html#View3D-RoamerFactory' target='_blank'>RoamerFactory.js</a></div></pre><div class='doc-contents'><p>提供创建不同Roamer的接口</p>\n\n<p>应用不应该构建，应该通过View3D直接获取</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div id='method-createFirstAndThirdPersonRoamer' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.RoamerFactory'>View3D.RoamerFactory</span><br/><a href='source/RoamerFactory.html#View3D-RoamerFactory-method-createFirstAndThirdPersonRoamer' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.RoamerFactory-method-createFirstAndThirdPersonRoamer' class='name expandable'>createFirstAndThirdPersonRoamer</a>( <span class='pre'>option</span> ) : <a href=\"#!/api/View3D.Roamer\" rel=\"View3D.Roamer\" class=\"docClass\">View3D.Roamer</a><span class=\"signature\"></span></div><div class='description'><div class='short'>第一人称和第三人称漫游器: createFirstAndThirdPersonRoamer\noption.keySettings中forward、backward、left、right控制前后左右移动，option.moveVeloc...</div><div class='long'><p>第一人称和第三人称漫游器: createFirstAndThirdPersonRoamer<br>\noption.keySettings中forward、backward、left、right控制前后左右移动，option.moveVelocity控制其速度<br>\n鼠标左键或右键按下拖动用来改变视口<br>\n按下option.keySettings.collisionSwitch触发按键用来切换水平碰撞检测状态，默认为开启水平碰撞检测<br>\n按下option.keySettings.home用来移动到option.homePosition坐标点和option.homeDirection朝向<br>\n按下option.keySettings.jump用来进行跳跃<br>\n按下option.keySettings.firstThirdSwitch用来在漫游器内进行一三人称切换<br>\n第三人称状态下，鼠标滚轮用来调整摄像机距离人物眼睛的距离<br>\n在此漫游器下可以通过注册View3D.registerFirstThirdPersonCallback回调函数获得实时状态，并可以调用View3D.setFirstThirdPersonState进行参数调整<br>\n注意：键盘按键均应处于小写英文状态<br></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object<div class='sub-desc'><p>创建漫游交互的选项</p>\n<ul><li><span class='pre'>mode</span> : String (optional)<div class='sub-desc'><p>漫游器初始人称：\"firstPerson\" 第一人称、\"thirdPerson\" 第三人称</p>\n<p>Defaults to: <code>&quot;firstPerson&quot;</code></p></div></li><li><span class='pre'>modelUrl</span> : String<div class='sub-desc'><p>第三人称动画模型文件地址（支持“http://”地址和“file:///”格式），模型文件支持格式：.fbx <br>\n                                                                fbx动画模型中需要实现\"TakeRun\"-奔跑动画、\"TakeJump\"-跳跃动画、\"TakeStand\"-站立动画三个Takes动画 <br>\n                                                             fbx建模要求：人物上方为Y轴，前方为Z轴，人物高度推荐150cm-170cm，使用量纲cm，模型无缩放。<br>\n                                                                具体可以参考resource/pao0-24_ThirdPerson.FBX。</p>\n</div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>加载动画模型结束回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : NetworkError/DataError<div class='sub-desc'><p>加载过程出现错误时返回错误\n                                 err {NetworkError}          模型文件下载失败\n                                err {DataError}             模型数据出错</p>\n</div></li></ul></div></li><li><span class='pre'>moveVelocity</span> : Number (optional)<div class='sub-desc'><p>水平移动速度，单位：毫米/秒，需要大于零，推荐小于20000毫米/秒，超出可能出现穿模现象</p>\n<p>Defaults to: <code>3000</code></p></div></li><li><span class='pre'>homePosition</span> : vec3 (optional)<div class='sub-desc'><p>按option.keySettings.home键返回到的人物脚下位置（人物眼睛距脚下1.5m），世界坐标系，单位：毫米</p>\n<p>Defaults to: <code>vec3(0,0,0)</code></p></div></li><li><span class='pre'>homeDirection</span> : vec3 (optional)<div class='sub-desc'><p>按option.keySettings.home键返回到的视口朝向，世界坐标系</p>\n<p>Defaults to: <code>vec3(1,0,0)</code></p></div></li><li><span class='pre'>keySettings</span> : Object (optional)<div class='sub-desc'><p>按键设置，可设置值\"a\"-\"z\"（26个小写字母）、\"0\"-\"9\"（主数字按键）、\"space\"（空格）、<br>\n                                                                \"up\"、\"down\"、\"left\"、\"right\"（键盘方向键上下左右）<br>\n                                                                单个按键值设置为\"null\"可以屏蔽掉此按键功能</p>\n<ul><li><span class='pre'>forward</span> : String (optional)<div class='sub-desc'><p>前进键设置</p>\n<p>Defaults to: <code>&quot;w&quot;</code></p></div></li><li><span class='pre'>backward</span> : String (optional)<div class='sub-desc'><p>后退键设置</p>\n<p>Defaults to: <code>&quot;s&quot;</code></p></div></li><li><span class='pre'>left</span> : String (optional)<div class='sub-desc'><p>左移键设置</p>\n<p>Defaults to: <code>&quot;a&quot;</code></p></div></li><li><span class='pre'>right</span> : String (optional)<div class='sub-desc'><p>右移键设置</p>\n<p>Defaults to: <code>&quot;d&quot;</code></p></div></li><li><span class='pre'>jump</span> : String (optional)<div class='sub-desc'><p>跳跃键设置</p>\n<p>Defaults to: <code>&quot;f&quot;</code></p></div></li><li><span class='pre'>home</span> : String (optional)<div class='sub-desc'><p>返回键设置</p>\n<p>Defaults to: <code>&quot;h&quot;</code></p></div></li><li><span class='pre'>collisionSwitch</span> : String (optional)<div class='sub-desc'><p>水平碰撞检测切换键设置</p>\n<p>Defaults to: <code>&quot;g&quot;</code></p></div></li><li><span class='pre'>firstThirdSwitch</span> : String (optional)<div class='sub-desc'><p>一三人称切换键设置</p>\n<p>Defaults to: <code>&quot;r&quot;</code></p></div></li></ul></div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Roamer\" rel=\"View3D.Roamer\" class=\"docClass\">View3D.Roamer</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createFlyModeRoamer' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.RoamerFactory'>View3D.RoamerFactory</span><br/><a href='source/RoamerFactory.html#View3D-RoamerFactory-method-createFlyModeRoamer' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.RoamerFactory-method-createFlyModeRoamer' class='name expandable'>createFlyModeRoamer</a>( <span class='pre'>[option]</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>创建飞行模式漫游器: FlyModeRoamer\n该漫游器移动方向分别为相机的水平方向前后、水平方向左右、竖直方向\n鼠标按下后拖动距离越长（相较于鼠标按下位置），鼠标控制移动和旋转的步长越大\n键盘鼠标的操作彼此独立，可以同时进行\n键盘...</div><div class='long'><p>创建飞行模式漫游器: FlyModeRoamer<br>\n该漫游器移动方向分别为相机的水平方向前后、水平方向左右、竖直方向\n鼠标按下后拖动距离越长（相较于鼠标按下位置），鼠标控制移动和旋转的步长越大\n键盘鼠标的操作彼此独立，可以同时进行\n键盘按键设置不能为大写字母</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object (optional)<div class='sub-desc'><p>创建飞行漫游的选项<br></p>\n<ul><li><span class='pre'>leftMBOperation</span> : String (optional)<div class='sub-desc'><p>鼠标左键拖动可绑定旋转（Rotate）、水平运动（Horizontal）或竖直运动（Vertical）<br></p>\n<p>Defaults to: <code>&quot;Rotate&quot;</code></p></div></li><li><span class='pre'>rightMBOperation</span> : String (optional)<div class='sub-desc'><p>鼠标右键拖动可绑定旋转（Rotate）、水平运动（Horizontal）或竖直运动（Vertical）<br></p>\n<p>Defaults to: <code>&quot;Horizontal&quot;</code></p></div></li><li><span class='pre'>middleMBOperation</span> : String (optional)<div class='sub-desc'><p>鼠标中键拖动可绑定旋转（Rotate）、水平运动（Horizontal）或竖直运动（Vertical）<br></p>\n<p>Defaults to: <code>&quot;Vertical&quot;</code></p></div></li><li><span class='pre'>keyBaseMoveSpeed</span> : Number (optional)<div class='sub-desc'><p>设置漫游器的按键移动速度，m/s<br></p>\n<p>Defaults to: <code>10.0</code></p></div></li><li><span class='pre'>mouseBaseMoveSpeed</span> : Number (optional)<div class='sub-desc'><p>设置漫游器的鼠标拖动移动步长比例，必须大于0。值越大，移动步长越大。<br></p>\n<p>Defaults to: <code>1.0</code></p></div></li><li><span class='pre'>mouseBaseRotationSpeed</span> : Number (optional)<div class='sub-desc'><p>设置漫游器的鼠标拖动旋转步长比例，必须大于0。值越大，旋转步长越大。<br></p>\n<p>Defaults to: <code>1.0</code></p></div></li><li><span class='pre'>keyEventMode</span> : string (optional)<div class='sub-desc'><p>选择漫游器的按键操作模式，默认采用wsad模式<br>\n        - \"wsad\"    w,s,a,d控制漫游器水平前后左右移动，q,e控制漫游器的竖直上下移动<br>\n        - \"arrow\"   键盘上下左右方向键控制漫游器水平前后左右移动，q,e控制漫游器的竖直上下移动。 注意：采用该模式时需要关闭浏览器的光标浏览功能<br></p>\n<p>Defaults to: <code>&quot;wsad&quot;</code></p></div></li></ul></div></li></ul></div></div></div><div id='method-createFreeModeRoamer' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.RoamerFactory'>View3D.RoamerFactory</span><br/><a href='source/RoamerFactory.html#View3D-RoamerFactory-method-createFreeModeRoamer' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.RoamerFactory-method-createFreeModeRoamer' class='name expandable'>createFreeModeRoamer</a>( <span class='pre'>[option]</span> ) : <a href=\"#!/api/View3D.Roamer\" rel=\"View3D.Roamer\" class=\"docClass\">View3D.Roamer</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建自由模式漫游器: FreeModeRoamer\n新增参数可自由为左右中键绑定不同的交互。 ...</div><div class='long'><p>创建自由模式漫游器: FreeModeRoamer<br>\n新增参数可自由为左右中键绑定不同的交互。<br>\n为兼容之前版本option.opMode将会保留两个版本，若用户给定了option.opMode则以下四个给定参数leftMouseButton、rightMouseButton、middleMouseButton、mouseWheel不会生效。<br>\n若用户未给任何参数则默认的交互模式为mode1，opMode不为mode1或mode2时默认mode1。<br>\n若左右中键不想绑定任何操作，则可以给空字符串；例如：option.mouseWheel = \"\"</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object (optional)<div class='sub-desc'><p>创建漫游交互的选项</p>\n<ul><li><span class='pre'>opMode</span> : String (optional)<div class='sub-desc'><p>交互模式\n                        - \"mode1\"   鼠标左键拖动旋转、中间滚轮拖动平移、右键拖动缩放、滑动滚轮缩放\n                        - \"mode2\"   鼠标左键拖动旋转、右键拖动平移、滑动滚轮缩放</p>\n<p>Defaults to: <code>&quot;mode1&quot;</code></p></div></li><li><span class='pre'>leftMouseButtonOperation</span> : String (optional)<div class='sub-desc'><p>鼠标左键拖动可绑定旋转（rotate）或平移（pan）或缩放（zoom）</p>\n<p>Defaults to: <code>&quot;rotate&quot;</code></p></div></li><li><span class='pre'>rightMouseButtonOperation</span> : String (optional)<div class='sub-desc'><p>鼠标右键拖动可绑定旋转（rotate）或平移（pan）或缩放（zoom）</p>\n<p>Defaults to: <code>&quot;zoom&quot;</code></p></div></li><li><span class='pre'>middleMouseButtonOperation</span> : String (optional)<div class='sub-desc'><p>中间滚轮拖动可绑定旋转（rotate）或平移（pan）或缩放（zoom）</p>\n<p>Defaults to: <code>&quot;pan&quot;</code></p></div></li><li><span class='pre'>mouseWheelOperation</span> : String (optional)<div class='sub-desc'><p>滑动滚轮可绑定缩放(zoom)，默认是zoom</p>\n<p>Defaults to: <code>&quot;zoom&quot;</code></p></div></li><li><span class='pre'>viewpointLimitation</span> : vec3[] (optional)<div class='sub-desc'><p>限制的可视范围（目前限制范围为Box）Box八个顶点\n                        （<a href=\"https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/83200479\">详细请参考</a>）\n                        缺省时表示没有限制</p>\n</div></li><li><span class='pre'>fixedRotationCenter</span> : vec3 (optional)<div class='sub-desc'><p>固定旋转中心；当设置固定旋转中心后，漫游器会固定以该点为中心进行旋转。<br>\n                        缺省时表示没有固定旋转中心，旋转中心会根据鼠标指向的位置动态获取</p>\n</div></li><li><span class='pre'>fixedZoomCenter</span> : vec3 (optional)<div class='sub-desc'><p>固定的缩放中心。<br>\n                                                    缺省时，缩放操作将以鼠标指向的位置为参考，缩放步长与参考点到相机位置的距离成正比；<br>\n                                                    当设置此选项后，漫游器的缩放操作将以此点为中心，不会再根据鼠标指向而动态变化。<br></p>\n</div></li><li><span class='pre'>fixedPanCenter</span> : vec3 (optional)<div class='sub-desc'><p>固定的平移参考点<br>\n                                                    缺省时，平衡操作将以鼠标指向的位置为参考，平移步长与参考点到相机位置的距离成正比；<br>\n                                                    当设置此选项后，漫游器的平移操作将以此点为中心，不会再根据鼠标指向而动态变化。<br></p>\n</div></li><li><span class='pre'>panScale</span> : Number (optional)<div class='sub-desc'><p>平移的步长比例。可以使用这个值扩大或限制平移操作的步长。<br>\n这个值是直接乘在基础步长上的，值越小，步长越小；值越大，步长越大<br>\n所以当取值为1时，就是之前的平移步长；<br>\n取值小于1时，会缩小步长，相当于限制了平移的能力。设置为0的时候就禁用了平移。<br>\n当取值大于1时，会扩大步长。</p>\n<p>Defaults to: <code>1</code></p></div></li><li><span class='pre'>intersectFilter</span> : IntersectFilter (optional)<div class='sub-desc'><p>求交结果过滤器</p>\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Roamer\" rel=\"View3D.Roamer\" class=\"docClass\">View3D.Roamer</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createTrackLongRoamer' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.RoamerFactory'>View3D.RoamerFactory</span><br/><a href='source/RoamerFactory.html#View3D-RoamerFactory-method-createTrackLongRoamer' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.RoamerFactory-method-createTrackLongRoamer' class='name expandable'>createTrackLongRoamer</a>( <span class='pre'>[option]</span> ) : <a href=\"#!/api/View3D.Roamer\" rel=\"View3D.Roamer\" class=\"docClass\">View3D.Roamer</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建循线模式漫游器: createTrackLongRoamer\noption设置与自由模式漫游器完全相同，循线操作通过注册View3D.registerTrackLongCallback回调函数，并调用View3D.setTrack...</div><div class='long'><p>创建循线模式漫游器: createTrackLongRoamer<br>\noption设置与自由模式漫游器完全相同，循线操作通过注册View3D.registerTrackLongCallback回调函数，并调用View3D.setTrackLongState控制进行操作<br>\n新增参数可自由为左右中键绑定不同的交互。<br>\n为兼容之前版本option.opMode将会保留两个版本，若用户给定了option.opMode则以下四个给定参数leftMouseButton、rightMouseButton、middleMouseButton、mouseWheel不会生效。<br>\n若用户未给任何参数则默认的交互模式为mode1，opMode不为mode1或mode2时默认mode1。<br>\n若左右中键不想绑定任何操作，则可以给空字符串；例如：option.mouseWheel = \"\"</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>option</span> : Object (optional)<div class='sub-desc'><p>创建漫游交互的选项</p>\n<ul><li><span class='pre'>opMode</span> : String (optional)<div class='sub-desc'><p>交互模式\n                        - \"mode1\"   鼠标左键拖动旋转、中间滚轮拖动平移、右键拖动缩放、滑动滚轮缩放\n                        - \"mode2\"   鼠标左键拖动旋转、右键拖动平移、滑动滚轮缩放</p>\n<p>Defaults to: <code>&quot;mode1&quot;</code></p></div></li><li><span class='pre'>leftMouseButtonOperation</span> : String (optional)<div class='sub-desc'><p>鼠标左键拖动可绑定旋转（rotate）或平移（pan）或缩放（zoom）</p>\n<p>Defaults to: <code>&quot;rotate&quot;</code></p></div></li><li><span class='pre'>rightMouseButtonOperation</span> : String (optional)<div class='sub-desc'><p>鼠标右键拖动可绑定旋转（rotate）或平移（pan）或缩放（zoom）</p>\n<p>Defaults to: <code>&quot;zoom&quot;</code></p></div></li><li><span class='pre'>middleMouseButtonOperation</span> : String (optional)<div class='sub-desc'><p>中间滚轮拖动可绑定旋转（rotate）或平移（pan）或缩放（zoom）</p>\n<p>Defaults to: <code>&quot;pan&quot;</code></p></div></li><li><span class='pre'>mouseWheelOperation</span> : String (optional)<div class='sub-desc'><p>滑动滚轮可绑定缩放(zoom)，默认是zoom</p>\n<p>Defaults to: <code>&quot;zoom&quot;</code></p></div></li><li><span class='pre'>viewpointLimitation</span> : vec3[] (optional)<div class='sub-desc'><p>限制的可视范围（目前限制范围为Box）Box八个顶点\n                        （<a href=\"https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/83200479\">详细请参考</a>）\n                        缺省时表示没有限制</p>\n</div></li><li><span class='pre'>fixedRotationCenter</span> : vec3 (optional)<div class='sub-desc'><p>固定旋转中心；当设置固定旋转中心后，漫游器会固定以该点为中心进行旋转。<br>\n                        缺省时表示没有固定旋转中心，旋转中心会根据鼠标指向的位置动态获取</p>\n</div></li><li><span class='pre'>fixedZoomCenter</span> : vec3 (optional)<div class='sub-desc'><p>固定的缩放中心。<br>\n                                                    缺省时，缩放操作将以鼠标指向的位置为参考，缩放步长与参考点到相机位置的距离成正比；<br>\n                                                    当设置此选项后，漫游器的缩放操作将以此点为中心，不会再根据鼠标指向而动态变化。<br></p>\n</div></li><li><span class='pre'>fixedPanCenter</span> : vec3 (optional)<div class='sub-desc'><p>固定的平移参考点<br>\n                                                    缺省时，平衡操作将以鼠标指向的位置为参考，平移步长与参考点到相机位置的距离成正比；<br>\n                                                    当设置此选项后，漫游器的平移操作将以此点为中心，不会再根据鼠标指向而动态变化。<br></p>\n</div></li><li><span class='pre'>panScale</span> : Number (optional)<div class='sub-desc'><p>平移的步长比例。可以使用这个值扩大或限制平移操作的步长。<br>\n这个值是直接乘在基础步长上的，值越小，步长越小；值越大，步长越大<br>\n所以当取值为1时，就是之前的平移步长；<br>\n取值小于1时，会缩小步长，相当于限制了平移的能力。设置为0的时候就禁用了平移。<br>\n当取值大于1时，会扩大步长。</p>\n<p>Defaults to: <code>1</code></p></div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Roamer\" rel=\"View3D.Roamer\" class=\"docClass\">View3D.Roamer</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-releaseRoamers' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.RoamerFactory'>View3D.RoamerFactory</span><br/><a href='source/RoamerFactory.html#View3D-RoamerFactory-method-releaseRoamers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.RoamerFactory-method-releaseRoamers' class='name expandable'>releaseRoamers</a>( <span class='pre'>roamers</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>删除漫游交互 ...</div><div class='long'><p>删除漫游交互</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>roamers</span> : Roamer[]<div class='sub-desc'><p>存储待删除漫游交互的数组</p>\n</div></li></ul></div></div></div></div></div></div></div>","meta":{}});