Ext.data.JsonP.View3D_Command({"tagname":"class","name":"View3D.Command","autodetected":{},"files":[{"filename":"Command.js","href":"Command.html#View3D-Command"}],"members":[],"alternateClassNames":[],"aliases":{},"id":"class-View3D.Command","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Command.html#View3D-Command' target='_blank'>Command.js</a></div></pre><div class='doc-contents'><p>三维Command命令</p>\n\n<p>不应该由应用直接创建，应该由NodeFactory或者EffectFactory等自己构建自己需要的Command</p>\n</div><div class='members'></div></div>","meta":{}});