Ext.data.JsonP.ViewPID_Style_TextStyle({"tagname":"class","name":"ViewPID.Style.TextStyle","autodetected":{},"files":[{"filename":"Style.js","href":"Style2.html#ViewPID-Style-TextStyle"}],"members":[{"name":"color","tagname":"property","owner":"ViewPID.Style.TextStyle","id":"property-color","meta":{}},{"name":"fontName","tagname":"property","owner":"ViewPID.Style.TextStyle","id":"property-fontName","meta":{}},{"name":"size","tagname":"property","owner":"ViewPID.Style.TextStyle","id":"property-size","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-ViewPID.Style.TextStyle","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Style2.html#ViewPID-Style-TextStyle' target='_blank'>Style.js</a></div></pre><div class='doc-contents'><p>标准文本属性定义</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-color' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.Style.TextStyle'>ViewPID.Style.TextStyle</span><br/><a href='source/Style2.html#ViewPID-Style-TextStyle-property-color' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.Style.TextStyle-property-color' class='name expandable'>color</a> : Color<span class=\"signature\"></span></div><div class='description'><div class='short'>字体色，颜色中的alpha值无效 ...</div><div class='long'><p>字体色，颜色中的alpha值无效</p>\n<p>Defaults to: <code>white</code></p></div></div></div><div id='property-fontName' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.Style.TextStyle'>ViewPID.Style.TextStyle</span><br/><a href='source/Style2.html#ViewPID-Style-TextStyle-property-fontName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.Style.TextStyle-property-fontName' class='name expandable'>fontName</a> : String<span class=\"signature\"></span></div><div class='description'><div class='short'>字体名，默认为楷体（该名与当前操作系统（c:\\windows\\fonts）下的字体文件名对应，若不满足时，可以安装新的字体） ...</div><div class='long'><p>字体名，默认为楷体（该名与当前操作系统（c:\\windows\\fonts）下的字体文件名对应，若不满足时，可以安装新的字体）</p>\n<p>Defaults to: <code>&quot;楷体&quot;</code></p></div></div></div><div id='property-size' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.Style.TextStyle'>ViewPID.Style.TextStyle</span><br/><a href='source/Style2.html#ViewPID-Style-TextStyle-property-size' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.Style.TextStyle-property-size' class='name expandable'>size</a> : Number<span class=\"signature\"></span></div><div class='description'><div class='short'>字体大小（毫米）,定义的是文字高度，宽度自动调整 ...</div><div class='long'><p>字体大小（毫米）,定义的是文字高度，宽度自动调整</p>\n<p>Defaults to: <code>5</code></p></div></div></div></div></div></div></div>","meta":{}});