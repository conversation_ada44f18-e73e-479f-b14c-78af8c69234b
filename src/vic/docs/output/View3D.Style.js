Ext.data.JsonP.View3D_Style({"tagname":"class","name":"View3D.Style","autodetected":{},"files":[{"filename":"Style.js","href":"Style.html#View3D-Style"}],"members":[],"alternateClassNames":[],"aliases":{},"id":"class-View3D.Style","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Style.html#View3D-Style' target='_blank'>Style.js</a></div></pre><div class='doc-contents'><p>标准属性定义</p>\n</div><div class='members'></div></div>","meta":{}});