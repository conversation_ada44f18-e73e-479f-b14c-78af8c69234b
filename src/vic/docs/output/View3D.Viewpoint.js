Ext.data.JsonP.View3D_Viewpoint({"tagname":"class","name":"View3D.Viewpoint","autodetected":{},"files":[{"filename":"Viewpoint.js","href":"Viewpoint.html#View3D-Viewpoint"}],"members":[{"name":"center","tagname":"property","owner":"View3D.Viewpoint","id":"property-center","meta":{}},{"name":"eye","tagname":"property","owner":"View3D.Viewpoint","id":"property-eye","meta":{}},{"name":"up","tagname":"property","owner":"View3D.Viewpoint","id":"property-up","meta":{}},{"name":"constructor","tagname":"method","owner":"View3D.Viewpoint","id":"method-constructor","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-View3D.Viewpoint","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Viewpoint.html#View3D-Viewpoint' target='_blank'>Viewpoint.js</a></div></pre><div class='doc-contents'><p><a href=\"https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/75956834\">视角</a></p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-center' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Viewpoint'>View3D.Viewpoint</span><br/><a href='source/Viewpoint.html#View3D-Viewpoint-property-center' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Viewpoint-property-center' class='name expandable'>center</a> : vec3<span class=\"signature\"></span></div><div class='description'><div class='short'><p>眼睛所看到物体的中心位置</p>\n</div><div class='long'><p>眼睛所看到物体的中心位置</p>\n</div></div></div><div id='property-eye' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Viewpoint'>View3D.Viewpoint</span><br/><a href='source/Viewpoint.html#View3D-Viewpoint-property-eye' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Viewpoint-property-eye' class='name expandable'>eye</a> : vec3<span class=\"signature\"></span></div><div class='description'><div class='short'><p>眼睛的位置</p>\n</div><div class='long'><p>眼睛的位置</p>\n</div></div></div><div id='property-up' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Viewpoint'>View3D.Viewpoint</span><br/><a href='source/Viewpoint.html#View3D-Viewpoint-property-up' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Viewpoint-property-up' class='name expandable'>up</a> : vec3<span class=\"signature\"></span></div><div class='description'><div class='short'><p>眼睛的正方向</p>\n</div><div class='long'><p>眼睛的正方向</p>\n</div></div></div></div></div><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div id='method-constructor' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Viewpoint'>View3D.Viewpoint</span><br/><a href='source/Viewpoint.html#View3D-Viewpoint-method-constructor' target='_blank' class='view-source'>view source</a></div><strong class='new-keyword'>new</strong><a href='#!/api/View3D.Viewpoint-method-constructor' class='name expandable'>View3D.Viewpoint</a>( <span class='pre'>eye, center, [up]</span> ) : <a href=\"#!/api/View3D.Viewpoint\" rel=\"View3D.Viewpoint\" class=\"docClass\">View3D.Viewpoint</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建一个新视点 ...</div><div class='long'><p>创建一个新视点</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eye</span> : vec3<div class='sub-desc'><p>眼睛的位置</p>\n</div></li><li><span class='pre'>center</span> : vec3<div class='sub-desc'><p>眼睛所看到物体的中心位置</p>\n</div></li><li><span class='pre'>up</span> : vec3 (optional)<div class='sub-desc'><p>视角正方向</p>\n<p>Defaults to: <code>vec3(0,0,1)</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Viewpoint\" rel=\"View3D.Viewpoint\" class=\"docClass\">View3D.Viewpoint</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div></div></div></div></div>","meta":{}});