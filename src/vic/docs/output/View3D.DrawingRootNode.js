Ext.data.JsonP.View3D_DrawingRootNode({"tagname":"class","name":"View3D.DrawingRootNode","autodetected":{},"files":[{"filename":"DrawingRootNode.js","href":"DrawingRootNode.html#View3D-DrawingRootNode"}],"members":[{"name":"createDetachCommand","tagname":"method","owner":"View3D.DrawingRootNode","id":"method-createDetachCommand","meta":{}},{"name":"createDetachToCommand","tagname":"method","owner":"View3D.DrawingRootNode","id":"method-createDetachToCommand","meta":{}},{"name":"createUndetachCommand","tagname":"method","owner":"View3D.DrawingRootNode","id":"method-createUndetachCommand","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-View3D.DrawingRootNode","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/DrawingRootNode.html#View3D-DrawingRootNode' target='_blank'>DrawingRootNode.js</a></div></pre><div class='doc-contents'><p>Drawing文件对应模型根节点</p>\n\n<p>不应该直接创建，应该由NodeFactory创建出来</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div id='method-createDetachCommand' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.DrawingRootNode'>View3D.DrawingRootNode</span><br/><a href='source/DrawingRootNode.html#View3D-DrawingRootNode-method-createDetachCommand' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.DrawingRootNode-method-createDetachCommand' class='name expandable'>createDetachCommand</a>( <span class='pre'>nodes, callback</span> ) : <a href=\"#!/api/View3D.Command\" rel=\"View3D.Command\" class=\"docClass\">View3D.Command</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建分离Command，将子节点从父节点上分离\n\n注：返回的节点支持放置在Drawing之外使用\n注：通过findDescendNodes或者点选获取的Drawing下的节点仅限于放在Drawing之下使用\n注：如果是OGF文件，该接...</div><div class='long'><p>创建分离Command，将子节点从父节点上分离<br/></p>\n\n<p>注：返回的节点支持放置在Drawing之外使用<br/>\n注：通过findDescendNodes或者点选获取的Drawing下的节点仅限于放在Drawing之下使用<br/>\n注：<b>如果是OGF文件，该接口必须和transformNodes接口一起使用</b><br/></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>nodes</span> : Node[]<div class='sub-desc'><p>需要分离的节点</p>\n</div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>返回分离结果</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>error</span> : Error<div class='sub-desc'><p>返回错误</p>\n</div></li><li><span class='pre'>nodes</span> : Node[]<div class='sub-desc'><p>返回分离后的节点</p>\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Command\" rel=\"View3D.Command\" class=\"docClass\">View3D.Command</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createDetachToCommand' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.DrawingRootNode'>View3D.DrawingRootNode</span><br/><a href='source/DrawingRootNode.html#View3D-DrawingRootNode-method-createDetachToCommand' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.DrawingRootNode-method-createDetachToCommand' class='name expandable'>createDetachToCommand</a>( <span class='pre'>options, callback</span> ) : <a href=\"#!/api/View3D.Command\" rel=\"View3D.Command\" class=\"docClass\">View3D.Command</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建分离Command，将子节点从父节点上分离\n\n注：返回的节点支持放置在Drawing之外使用\n注：通过findDescendNodes或者点选获取的Drawing下的节点仅限于放在Drawing之下使用\n注：如果是OGF文件，该接...</div><div class='long'><p>创建分离Command，将子节点从父节点上分离<br/></p>\n\n<p>注：返回的节点支持放置在Drawing之外使用<br/>\n注：通过findDescendNodes或者点选获取的Drawing下的节点仅限于放在Drawing之下使用<br/>\n注：<b>如果是OGF文件，该接口必须和transformNodes接口一起使用</b><br/>\n注：<b>该接口会把新的节点作为parentsNode的孩子，目的是防止之后再添加回场景中时发生闪烁</b> <br/></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : object<div class='sub-desc'><p>需要分离的节点和分离后节点的父节点</p>\n<ul><li><span class='pre'>nodes</span> : Node[]<div class='sub-desc'><p>需要分离的节点</p>\n</div></li><li><span class='pre'>parentNodes</span> : Node[] (optional)<div class='sub-desc'><p>分离后节点的父节点</p>\n</div></li></ul></div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>返回分离结果</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>error</span> : Error<div class='sub-desc'><p>返回错误</p>\n</div></li><li><span class='pre'>nodes</span> : Node[]<div class='sub-desc'><p>返回分离后的节点</p>\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Command\" rel=\"View3D.Command\" class=\"docClass\">View3D.Command</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createUndetachCommand' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.DrawingRootNode'>View3D.DrawingRootNode</span><br/><a href='source/DrawingRootNode.html#View3D-DrawingRootNode-method-createUndetachCommand' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.DrawingRootNode-method-createUndetachCommand' class='name expandable'>createUndetachCommand</a>( <span class='pre'>nodes, callback</span> ) : <a href=\"#!/api/View3D.Command\" rel=\"View3D.Command\" class=\"docClass\">View3D.Command</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建分离恢复Command，将分离后的子节点重新加入到父节点下 ...</div><div class='long'><p>创建分离恢复Command，将分离后的子节点重新加入到父节点下</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>nodes</span> : Node[]<div class='sub-desc'><p>已从Drawing下分离的节点</p>\n</div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>返回恢复结果</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>error</span> : Error<div class='sub-desc'><p>返回错误</p>\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Command\" rel=\"View3D.Command\" class=\"docClass\">View3D.Command</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div></div></div></div></div>","meta":{}});