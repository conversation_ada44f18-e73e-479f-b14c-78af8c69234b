Ext.data.JsonP.View3D_InteractionFactory({"tagname":"class","name":"View3D.InteractionFactory","autodetected":{},"files":[{"filename":"InteractionFactory.js","href":"InteractionFactory.html#View3D-InteractionFactory"}],"members":[{"name":"createChainFilter","tagname":"method","owner":"View3D.InteractionFactory","id":"method-createChainFilter","meta":{}},{"name":"createCircularTracker","tagname":"method","owner":"View3D.InteractionFactory","id":"method-createCircularTracker","meta":{}},{"name":"createClipedPickFilter","tagname":"method","owner":"View3D.InteractionFactory","id":"method-createClipedPickFilter","meta":{}},{"name":"createCompositeInteraction","tagname":"method","owner":"View3D.InteractionFactory","id":"method-createCompositeInteraction","meta":{}},{"name":"createCompositeTracker","tagname":"method","owner":"View3D.InteractionFactory","id":"method-createCompositeTracker","meta":{}},{"name":"createDirectionLimitDistanceTracker","tagname":"method","owner":"View3D.InteractionFactory","id":"method-createDirectionLimitDistanceTracker","meta":{}},{"name":"createDistanceToPlaneTracker","tagname":"method","owner":"View3D.InteractionFactory","id":"method-createDistanceToPlaneTracker","meta":{}},{"name":"createDistanceToPointTracker","tagname":"method","owner":"View3D.InteractionFactory","id":"method-createDistanceToPointTracker","meta":{}},{"name":"createFollowRectTracker","tagname":"method","owner":"View3D.InteractionFactory","id":"method-createFollowRectTracker","meta":{}},{"name":"createFrontResultFilter","tagname":"method","owner":"View3D.InteractionFactory","id":"method-createFrontResultFilter","meta":{}},{"name":"createIncludeNodeFilter","tagname":"method","owner":"View3D.InteractionFactory","id":"method-createIncludeNodeFilter","meta":{}},{"name":"createLineBasedRectTracker","tagname":"method","owner":"View3D.InteractionFactory","id":"method-createLineBasedRectTracker","meta":{}},{"name":"createMouseHoverInteraction","tagname":"method","owner":"View3D.InteractionFactory","id":"method-createMouseHoverInteraction","meta":{}},{"name":"createNamedNodeFilter","tagname":"method","owner":"View3D.InteractionFactory","id":"method-createNamedNodeFilter","meta":{}},{"name":"createPointPickInteraction","tagname":"method","owner":"View3D.InteractionFactory","id":"method-createPointPickInteraction","meta":{}},{"name":"createTransparencyNodeFilter","tagname":"method","owner":"View3D.InteractionFactory","id":"method-createTransparencyNodeFilter","meta":{}},{"name":"createVHDistanceTracker","tagname":"method","owner":"View3D.InteractionFactory","id":"method-createVHDistanceTracker","meta":{}},{"name":"releaseInteraction","tagname":"method","owner":"View3D.InteractionFactory","id":"method-releaseInteraction","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-View3D.InteractionFactory","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/InteractionFactory.html#View3D-InteractionFactory' target='_blank'>InteractionFactory.js</a></div></pre><div class='doc-contents'><p>提供可创建不同Interaction的接口</p>\n\n<p>提供可创建不同的PickFilter接口</p>\n\n<p>提供可创建不同的Tracker接口</p>\n\n<p>应用不应该构建，应该通过View3D直接获取</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div id='method-createChainFilter' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.InteractionFactory'>View3D.InteractionFactory</span><br/><a href='source/InteractionFactory.html#View3D-InteractionFactory-method-createChainFilter' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.InteractionFactory-method-createChainFilter' class='name expandable'>createChainFilter</a>( <span class='pre'>filters</span> ) : View3D.PickFilter<span class=\"signature\"></span></div><div class='description'><div class='short'>创建过滤器链\n过滤器链中的过滤器会依次起作用 ...</div><div class='long'><p>创建过滤器链\n过滤器链中的过滤器会依次起作用</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>filters</span> : PickFilter[]<div class='sub-desc'><p>子过滤器</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>View3D.PickFilter</span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createCircularTracker' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.InteractionFactory'>View3D.InteractionFactory</span><br/><a href='source/InteractionFactory.html#View3D-InteractionFactory-method-createCircularTracker' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.InteractionFactory-method-createCircularTracker' class='name expandable'>createCircularTracker</a>( <span class='pre'>startPt, [style]</span> ) : <a href=\"#!/api/View3D.Tracker\" rel=\"View3D.Tracker\" class=\"docClass\">View3D.Tracker</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建规则圆形的皮筋和该圆形直径的皮筋，包括动态变化的线和文字 (圆形皮筋与圆形直径皮筋只平行于xy平面，以startPt的z轴为准) ...</div><div class='long'><p>创建规则圆形的皮筋和该圆形直径的皮筋，包括动态变化的线和文字 (圆形皮筋与圆形直径皮筋只平行于xy平面，以startPt的z轴为准)</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>startPt</span> : vec3<div class='sub-desc'><p>皮筋的起点</p>\n</div></li><li><span class='pre'>style</span> : Object (optional)<div class='sub-desc'><p>绘制属性</p>\n<ul><li><span class='pre'>lineStyle</span> : Object (optional)<div class='sub-desc'><p>线属性，参照<a href=\"#!/api/View3D.Style.LineStyle\" rel=\"View3D.Style.LineStyle\" class=\"docClass\">View3D.Style.LineStyle</a>的定义</p>\n</div></li><li><span class='pre'>textFrameStyle</span> : Object (optional)<div class='sub-desc'><p>文字属性，参照<a href=\"#!/api/View3D.Style.TextFrameStyle\" rel=\"View3D.Style.TextFrameStyle\" class=\"docClass\">View3D.Style.TextFrameStyle</a>的定义</p>\n</div></li><li><span class='pre'>pointNum</span> : Number (optional)<div class='sub-desc'><p>组成圆形皮筋的点数量（整数 3-65536）(默认值30)</p>\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Tracker\" rel=\"View3D.Tracker\" class=\"docClass\">View3D.Tracker</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createClipedPickFilter' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.InteractionFactory'>View3D.InteractionFactory</span><br/><a href='source/InteractionFactory.html#View3D-InteractionFactory-method-createClipedPickFilter' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.InteractionFactory-method-createClipedPickFilter' class='name expandable'>createClipedPickFilter</a>( <span class='pre'></span> ) : View3D.PickFilter<span class=\"signature\"></span></div><div class='description'><div class='short'>创建过滤刨切的点击过滤器 ...</div><div class='long'><p>创建过滤刨切的点击过滤器</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'>View3D.PickFilter</span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createCompositeInteraction' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.InteractionFactory'>View3D.InteractionFactory</span><br/><a href='source/InteractionFactory.html#View3D-InteractionFactory-method-createCompositeInteraction' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.InteractionFactory-method-createCompositeInteraction' class='name expandable'>createCompositeInteraction</a>( <span class='pre'>interactions</span> ) : <a href=\"#!/api/View3D.Interaction\" rel=\"View3D.Interaction\" class=\"docClass\">View3D.Interaction</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建混合式交互操作，满足可以开启多个交互的操作 ...</div><div class='long'><p>创建混合式交互操作，满足可以开启多个交互的操作</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>interactions</span> : Interaction[]<div class='sub-desc'><p>交互的集合</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Interaction\" rel=\"View3D.Interaction\" class=\"docClass\">View3D.Interaction</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createCompositeTracker' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.InteractionFactory'>View3D.InteractionFactory</span><br/><a href='source/InteractionFactory.html#View3D-InteractionFactory-method-createCompositeTracker' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.InteractionFactory-method-createCompositeTracker' class='name expandable'>createCompositeTracker</a>( <span class='pre'>trackers</span> ) : <a href=\"#!/api/View3D.Tracker\" rel=\"View3D.Tracker\" class=\"docClass\">View3D.Tracker</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建复合Tracker ...</div><div class='long'><p>创建复合Tracker</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>trackers</span> : Tracker[]<div class='sub-desc'><p>tracker的集合</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Tracker\" rel=\"View3D.Tracker\" class=\"docClass\">View3D.Tracker</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createDirectionLimitDistanceTracker' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.InteractionFactory'>View3D.InteractionFactory</span><br/><a href='source/InteractionFactory.html#View3D-InteractionFactory-method-createDirectionLimitDistanceTracker' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.InteractionFactory-method-createDirectionLimitDistanceTracker' class='name expandable'>createDirectionLimitDistanceTracker</a>( <span class='pre'>startPt, [style], direction, callback</span> ) : <a href=\"#!/api/View3D.Tracker\" rel=\"View3D.Tracker\" class=\"docClass\">View3D.Tracker</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建约束方向距离Tracker ...</div><div class='long'><p>创建约束方向距离Tracker</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>startPt</span> : vec3<div class='sub-desc'><p>皮筋的起点</p>\n</div></li><li><span class='pre'>style</span> : Object (optional)<div class='sub-desc'><p>绘制属性</p>\n<ul><li><span class='pre'>lineStyle</span> : Object (optional)<div class='sub-desc'><p>线属性，参照<a href=\"#!/api/View3D.Style.LineStyle\" rel=\"View3D.Style.LineStyle\" class=\"docClass\">View3D.Style.LineStyle</a>的定义</p>\n</div></li><li><span class='pre'>textFrameStyle</span> : Object (optional)<div class='sub-desc'><p>文字属性，参照<a href=\"#!/api/View3D.Style.TextFrameStyle\" rel=\"View3D.Style.TextFrameStyle\" class=\"docClass\">View3D.Style.TextFrameStyle</a>的定义</p>\n</div></li></ul></div></li><li><span class='pre'>direction</span> : vec3<div class='sub-desc'><p>测量方向,不能传入(0,0,0),传入正方向和负方向效果一样，都是测量该方向的距离</p>\n</div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>获取约束方向距离Tracker的回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>获取回调失败返回的错误</p>\n</div></li><li><span class='pre'>pickResult</span> : vec3<div class='sub-desc'><p>获取回调成功返回Tracker的交点值</p>\n</div></li><li><span class='pre'>pointState</span> : boolean<div class='sub-desc'><p>交点值是否是选中模型上的交点</p>\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Tracker\" rel=\"View3D.Tracker\" class=\"docClass\">View3D.Tracker</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createDistanceToPlaneTracker' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.InteractionFactory'>View3D.InteractionFactory</span><br/><a href='source/InteractionFactory.html#View3D-InteractionFactory-method-createDistanceToPlaneTracker' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.InteractionFactory-method-createDistanceToPlaneTracker' class='name expandable'>createDistanceToPlaneTracker</a>( <span class='pre'>plane, [style]</span> ) : <a href=\"#!/api/View3D.Tracker\" rel=\"View3D.Tracker\" class=\"docClass\">View3D.Tracker</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建距离一个面的皮筋(跟随鼠标的一端会有一个点)，包括动态变化的线和文字 ...</div><div class='long'><p>创建距离一个面的皮筋(跟随鼠标的一端会有一个点)，包括动态变化的线和文字</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>plane</span> : Object<div class='sub-desc'><p>面</p>\n<ul><li><span class='pre'>point</span> : vec3<div class='sub-desc'><p>面上一个点</p>\n</div></li><li><span class='pre'>normal</span> : vec3<div class='sub-desc'><p>面的法线（垂直于面）</p>\n</div></li></ul></div></li><li><span class='pre'>style</span> : Object (optional)<div class='sub-desc'><p>绘制属性</p>\n<ul><li><span class='pre'>lineStyle</span> : Object (optional)<div class='sub-desc'><p>线属性，参照<a href=\"#!/api/View3D.Style.LineStyle\" rel=\"View3D.Style.LineStyle\" class=\"docClass\">View3D.Style.LineStyle</a>的定义</p>\n</div></li><li><span class='pre'>textFrameStyle</span> : Object (optional)<div class='sub-desc'><p>文字属性，参照<a href=\"#!/api/View3D.Style.TextFrameStyle\" rel=\"View3D.Style.TextFrameStyle\" class=\"docClass\">View3D.Style.TextFrameStyle</a>的定义</p>\n</div></li><li><span class='pre'>pointStyle</span> : Object (optional)<div class='sub-desc'><p>点属性，参照<a href=\"#!/api/View3D.Style.PointStyle\" rel=\"View3D.Style.PointStyle\" class=\"docClass\">View3D.Style.PointStyle</a></p>\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Tracker\" rel=\"View3D.Tracker\" class=\"docClass\">View3D.Tracker</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createDistanceToPointTracker' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.InteractionFactory'>View3D.InteractionFactory</span><br/><a href='source/InteractionFactory.html#View3D-InteractionFactory-method-createDistanceToPointTracker' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.InteractionFactory-method-createDistanceToPointTracker' class='name expandable'>createDistanceToPointTracker</a>( <span class='pre'>startPt, [style]</span> ) : <a href=\"#!/api/View3D.Tracker\" rel=\"View3D.Tracker\" class=\"docClass\">View3D.Tracker</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建距离点的皮筋，包括动态变化的线和文字 ...</div><div class='long'><p>创建距离点的皮筋，包括动态变化的线和文字</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>startPt</span> : vec3<div class='sub-desc'><p>皮筋的起点</p>\n</div></li><li><span class='pre'>style</span> : Object (optional)<div class='sub-desc'><p>绘制属性</p>\n<ul><li><span class='pre'>lineStyle</span> : Object (optional)<div class='sub-desc'><p>线属性，参照<a href=\"#!/api/View3D.Style.LineStyle\" rel=\"View3D.Style.LineStyle\" class=\"docClass\">View3D.Style.LineStyle</a>的定义</p>\n</div></li><li><span class='pre'>textFrameStyle</span> : Object (optional)<div class='sub-desc'><p>文字属性，参照<a href=\"#!/api/View3D.Style.TextFrameStyle\" rel=\"View3D.Style.TextFrameStyle\" class=\"docClass\">View3D.Style.TextFrameStyle</a>的定义</p>\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Tracker\" rel=\"View3D.Tracker\" class=\"docClass\">View3D.Tracker</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createFollowRectTracker' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.InteractionFactory'>View3D.InteractionFactory</span><br/><a href='source/InteractionFactory.html#View3D-InteractionFactory-method-createFollowRectTracker' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.InteractionFactory-method-createFollowRectTracker' class='name expandable'>createFollowRectTracker</a>( <span class='pre'></span> ) : <a href=\"#!/api/View3D.Tracker\" rel=\"View3D.Tracker\" class=\"docClass\">View3D.Tracker</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建跟随鼠标移动的小方框Tracker ...</div><div class='long'><p>创建跟随鼠标移动的小方框Tracker</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Tracker\" rel=\"View3D.Tracker\" class=\"docClass\">View3D.Tracker</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createFrontResultFilter' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.InteractionFactory'>View3D.InteractionFactory</span><br/><a href='source/InteractionFactory.html#View3D-InteractionFactory-method-createFrontResultFilter' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.InteractionFactory-method-createFrontResultFilter' class='name expandable'>createFrontResultFilter</a>( <span class='pre'></span> ) : View3D.PickFilter<span class=\"signature\"></span></div><div class='description'><div class='short'>创建过滤掉点选结果中除第一个之外的其他结果的过滤器\n如果点选结果本身就是空的则不执行该过滤 ...</div><div class='long'><p>创建过滤掉点选结果中除第一个之外的其他结果的过滤器\n如果点选结果本身就是空的则不执行该过滤</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'>View3D.PickFilter</span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createIncludeNodeFilter' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.InteractionFactory'>View3D.InteractionFactory</span><br/><a href='source/InteractionFactory.html#View3D-InteractionFactory-method-createIncludeNodeFilter' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.InteractionFactory-method-createIncludeNodeFilter' class='name expandable'>createIncludeNodeFilter</a>( <span class='pre'>includeNodes</span> ) : View3D.PickFilter<span class=\"signature\"></span></div><div class='description'><div class='short'>创建节点路径中包含特定节点的过滤器\n当一个点击结果的NodePath中包含指定的节点则该点击结果不被该过滤器过滤掉 ...</div><div class='long'><p>创建节点路径中包含特定节点的过滤器\n当一个点击结果的NodePath中包含指定的节点则该点击结果不被该过滤器过滤掉</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>includeNodes</span> : Node[]<div class='sub-desc'><p>指定的节点</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>View3D.PickFilter</span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createLineBasedRectTracker' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.InteractionFactory'>View3D.InteractionFactory</span><br/><a href='source/InteractionFactory.html#View3D-InteractionFactory-method-createLineBasedRectTracker' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.InteractionFactory-method-createLineBasedRectTracker' class='name expandable'>createLineBasedRectTracker</a>( <span class='pre'>baseLine, [style]</span> ) : <a href=\"#!/api/View3D.LineBasedRectTracker\" rel=\"View3D.LineBasedRectTracker\" class=\"docClass\">View3D.LineBasedRectTracker</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建绘制矩形Tracker\n把一个线段沿垂直线段方向拉伸形成一个矩形 ...</div><div class='long'><p>创建绘制矩形Tracker\n把一个线段沿垂直线段方向拉伸形成一个矩形</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>baseLine</span> : Object<div class='sub-desc'><p>矩形的起始边</p>\n<ul><li><span class='pre'>startPt</span> : vec3<div class='sub-desc'><p>矩形起始边的起点</p>\n</div></li><li><span class='pre'>endPt</span> : vec3<div class='sub-desc'><p>矩形起始边的终点</p>\n</div></li></ul></div></li><li><span class='pre'>style</span> : Object (optional)<div class='sub-desc'><p>绘制属性</p>\n<ul><li><span class='pre'>lineStyle</span> : Object (optional)<div class='sub-desc'><p>线属性，参照<a href=\"#!/api/View3D.Style.LineStyle\" rel=\"View3D.Style.LineStyle\" class=\"docClass\">View3D.Style.LineStyle</a>的定义</p>\n</div></li><li><span class='pre'>textFrameStyle</span> : Object (optional)<div class='sub-desc'><p>文字属性，参照<a href=\"#!/api/View3D.Style.TextFrameStyle\" rel=\"View3D.Style.TextFrameStyle\" class=\"docClass\">View3D.Style.TextFrameStyle</a>的定义</p>\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.LineBasedRectTracker\" rel=\"View3D.LineBasedRectTracker\" class=\"docClass\">View3D.LineBasedRectTracker</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createMouseHoverInteraction' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.InteractionFactory'>View3D.InteractionFactory</span><br/><a href='source/InteractionFactory.html#View3D-InteractionFactory-method-createMouseHoverInteraction' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.InteractionFactory-method-createMouseHoverInteraction' class='name expandable'>createMouseHoverInteraction</a>( <span class='pre'>hoverHandler, [option]</span> ) : <a href=\"#!/api/View3D.Interaction\" rel=\"View3D.Interaction\" class=\"docClass\">View3D.Interaction</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建鼠标悬停操作交互 ...</div><div class='long'><p>创建鼠标悬停操作交互</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>hoverHandler</span> : Function<div class='sub-desc'><p>当鼠标悬停或悬停结束都会触发此回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>交互过程中失败</p>\n</div></li><li><span class='pre'>msg</span> : Object<div class='sub-desc'><p>鼠标悬停交互结果信息</p>\n<ul><li><span class='pre'>mouseHoverState</span> : Number<div class='sub-desc'><p>0：标识鼠标开始悬停，1：标识鼠标结束悬停</p>\n</div></li><li><span class='pre'>screenPosition</span> : Vec2<div class='sub-desc'><p>鼠标悬停时的屏幕坐标，结束悬停时坐标值无效</p>\n</div></li><li><span class='pre'>picked</span> : Object[]<div class='sub-desc'><p>参照<a href=\"#!/api/View3D.InteractionFactory-method-createPointPickInteraction\" rel=\"View3D.InteractionFactory-method-createPointPickInteraction\" class=\"docClass\">createPointPickInteraction</a> picked的定义</p>\n</div></li></ul></div></li></ul></div></li><li><span class='pre'>option</span> : Object (optional)<div class='sub-desc'><p>创建鼠标点击交互的参数</p>\n<ul><li><span class='pre'>pickFilter</span> : PickFilter (optional)<div class='sub-desc'><p>点击结果过滤器</p>\n</div></li><li><span class='pre'>tracker</span> : Tracker (optional)<div class='sub-desc'><p>交互过程中附带的tracker</p>\n</div></li><li><span class='pre'>hoverTime</span> : Number (optional)<div class='sub-desc'><p>鼠标悬停需要的时间(毫秒)</p>\n<p>Defaults to: <code>300</code></p></div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Interaction\" rel=\"View3D.Interaction\" class=\"docClass\">View3D.Interaction</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createNamedNodeFilter' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.InteractionFactory'>View3D.InteractionFactory</span><br/><a href='source/InteractionFactory.html#View3D-InteractionFactory-method-createNamedNodeFilter' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.InteractionFactory-method-createNamedNodeFilter' class='name expandable'>createNamedNodeFilter</a>( <span class='pre'></span> ) : View3D.PickFilter<span class=\"signature\"></span></div><div class='description'><div class='short'>创建过滤掉没有命名的过滤器\n没有命名是指该点击结果的NodePath中所有的节点都没有命名 ...</div><div class='long'><p>创建过滤掉没有命名的过滤器\n没有命名是指该点击结果的NodePath中所有的节点都没有命名</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'>View3D.PickFilter</span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createPointPickInteraction' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.InteractionFactory'>View3D.InteractionFactory</span><br/><a href='source/InteractionFactory.html#View3D-InteractionFactory-method-createPointPickInteraction' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.InteractionFactory-method-createPointPickInteraction' class='name expandable'>createPointPickInteraction</a>( <span class='pre'>pickHandler, [option]</span> ) : <a href=\"#!/api/View3D.Interaction\" rel=\"View3D.Interaction\" class=\"docClass\">View3D.Interaction</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建鼠标左键/右键点选操作交互\n需要注意的点是：单击pickHandler只会回复一次交互结果；双击的情况下会先回一次单击交互结果再回一次双击交互结果 ...</div><div class='long'><p>创建鼠标左键/右键点选操作交互\n需要注意的点是：单击pickHandler只会回复一次交互结果；双击的情况下会先回一次单击交互结果再回一次双击交互结果</p>\n\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>pickHandler</span> : Function<div class='sub-desc'><p>当选择了一个点，则触发此回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>交互过程中发生错误传出错误</p>\n</div></li><li><span class='pre'>msg</span> : Object<div class='sub-desc'><p>交互结果信息</p>\n<ul><li><span class='pre'>button</span> : Number<div class='sub-desc'><p>标识鼠标左键/右键与触发类型 0-左键单击  2-右键单击 3-左键双击</p>\n</div></li><li><span class='pre'>screenPosition</span> : vec2<div class='sub-desc'><p>鼠标点击位置屏幕坐标</p>\n</div></li><li><span class='pre'>picked</span> : Object[]<div class='sub-desc'><p>选中节点信息，按照被选中节点离屏幕的距离从近到远排序，如果没有选中节点，则返回空数组</p>\n<ul><li><span class='pre'>nodePath</span> : Node[]<div class='sub-desc'><p>选中的节点路径，即从被选中的节点到根节点的所有节点按序排列</p>\n</div></li><li><span class='pre'>position</span> : vec3<div class='sub-desc'><p>点击到节点上的位置</p>\n</div></li><li><span class='pre'>normal</span> : vec3<div class='sub-desc'><p>点击到位置的法向</p>\n</div></li></ul></div></li></ul></div></li></ul></div></li><li><span class='pre'>option</span> : Object (optional)<div class='sub-desc'><p>创建鼠标点击交互的参数</p>\n<ul><li><span class='pre'>pickFilter</span> : PickFilter (optional)<div class='sub-desc'><p>点击结果过滤器</p>\n</div></li><li><span class='pre'>tracker</span> : Tracker (optional)<div class='sub-desc'><p>交互过程中附带的tracker</p>\n</div></li><li><span class='pre'>adsorbSetting</span> : Object (optional)<div class='sub-desc'><p>吸附点设置</p>\n<ul><li><span class='pre'>mode</span> : String (optional)<div class='sub-desc'><p>吸附点模式 \"none\"-不吸附 \"all\"-全部类型吸附点</p>\n<p>Defaults to: <code>&quot;none&quot;</code></p></div></li><li><span class='pre'>distanceRatio</span> : Number (optional)<div class='sub-desc'><p>吸附距离比例参数，无量纲，值需要大于0，推荐范围[30,200]<br>\n                                                                  三维坐标系下吸附距离 = 眼睛到选中物体距离 / 吸附距离比例参数<br>\n                                                                  说明：吸附功能总是选取距鼠标最近的可能点作为吸附点，当模型距离视口较远时且附近存在多个吸附点通过 <br>\n                                                                  鼠标移动可能较难准确选择，此时可以拉近视口，使得三维坐标系下吸附距离变小，鼠标选择精度提高，进而 <br>\n                                                                  更加容易进行吸附点选择。</p>\n<p>Defaults to: <code>50</code></p></div></li></ul></div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Interaction\" rel=\"View3D.Interaction\" class=\"docClass\">View3D.Interaction</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createTransparencyNodeFilter' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.InteractionFactory'>View3D.InteractionFactory</span><br/><a href='source/InteractionFactory.html#View3D-InteractionFactory-method-createTransparencyNodeFilter' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.InteractionFactory-method-createTransparencyNodeFilter' class='name expandable'>createTransparencyNodeFilter</a>( <span class='pre'>options</span> ) : View3D.PickFilter<span class=\"signature\"></span></div><div class='description'><div class='short'>创建过滤掉透明节点的过滤器               \n注意：6.0.0起修正了透明度逻辑，修正后透明度参数（取值范围0~1）值越大，越透明\n在点击结果中过滤掉透明度高于transparencyValue的节点 ...</div><div class='long'><p>创建过滤掉透明节点的过滤器               <br/>\n注意：6.0.0起修正了透明度逻辑，修正后透明度参数（取值范围0~1）值越大，越透明\n在点击结果中过滤掉透明度高于transparencyValue的节点</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : Object<div class='sub-desc'>\n<ul><li><span class='pre'>transparencyValue</span> : Number (optional)<div class='sub-desc'><p>透明度（范围0~1），0为全透明，1为不透明（值：0&lt;=transparencyValue&lt;=1）</p>\n<p>Defaults to: <code>0.5</code></p></div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>View3D.PickFilter</span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createVHDistanceTracker' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.InteractionFactory'>View3D.InteractionFactory</span><br/><a href='source/InteractionFactory.html#View3D-InteractionFactory-method-createVHDistanceTracker' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.InteractionFactory-method-createVHDistanceTracker' class='name expandable'>createVHDistanceTracker</a>( <span class='pre'>startPt, [style]</span> ) : <a href=\"#!/api/View3D.Tracker\" rel=\"View3D.Tracker\" class=\"docClass\">View3D.Tracker</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建垂直距离水平距离Tracker ...</div><div class='long'><p>创建垂直距离水平距离Tracker</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>startPt</span> : vec3<div class='sub-desc'><p>皮筋的起点</p>\n</div></li><li><span class='pre'>style</span> : Object (optional)<div class='sub-desc'><p>绘制属性</p>\n<ul><li><span class='pre'>lineStyle</span> : Object (optional)<div class='sub-desc'><p>线属性，参照<a href=\"#!/api/View3D.Style.LineStyle\" rel=\"View3D.Style.LineStyle\" class=\"docClass\">View3D.Style.LineStyle</a>的定义</p>\n</div></li><li><span class='pre'>textFrameStyle</span> : Object (optional)<div class='sub-desc'><p>文字属性，参照<a href=\"#!/api/View3D.Style.TextFrameStyle\" rel=\"View3D.Style.TextFrameStyle\" class=\"docClass\">View3D.Style.TextFrameStyle</a>的定义</p>\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Tracker\" rel=\"View3D.Tracker\" class=\"docClass\">View3D.Tracker</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-releaseInteraction' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.InteractionFactory'>View3D.InteractionFactory</span><br/><a href='source/InteractionFactory.html#View3D-InteractionFactory-method-releaseInteraction' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.InteractionFactory-method-releaseInteraction' class='name expandable'>releaseInteraction</a>( <span class='pre'>interactions</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>释放交互资源 ...</div><div class='long'><p>释放交互资源</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>interactions</span> : Interaction[]<div class='sub-desc'><p>待释放的交互资源集合</p>\n</div></li></ul></div></div></div></div></div></div></div>","meta":{}});