Ext.data.JsonP.View3D_Style_PointerStyle({"tagname":"class","name":"View3D.Style.PointerStyle","autodetected":{},"files":[{"filename":"Style.js","href":"Style.html#View3D-Style-PointerStyle"}],"members":[{"name":"identifyLocationPoint","tagname":"property","owner":"View3D.Style.PointerStyle","id":"property-identifyLocationPoint","meta":{}},{"name":"line","tagname":"property","owner":"View3D.Style.PointerStyle","id":"property-line","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-View3D.Style.PointerStyle","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Style.html#View3D-Style-PointerStyle' target='_blank'>Style.js</a></div></pre><div class='doc-contents'><p>线及相关点属性定义</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-identifyLocationPoint' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.PointerStyle'>View3D.Style.PointerStyle</span><br/><a href='source/Style.html#View3D-Style-PointerStyle-property-identifyLocationPoint' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.PointerStyle-property-identifyLocationPoint' class='name expandable'>identifyLocationPoint</a> : Object<span class=\"signature\"></span></div><div class='description'><div class='short'><p>位置标识点属性，参照<a href=\"#!/api/View3D.Style.IdentifyLocationPoint\" rel=\"View3D.Style.IdentifyLocationPoint\" class=\"docClass\">View3D.Style.IdentifyLocationPoint</a>的定义</p>\n</div><div class='long'><p>位置标识点属性，参照<a href=\"#!/api/View3D.Style.IdentifyLocationPoint\" rel=\"View3D.Style.IdentifyLocationPoint\" class=\"docClass\">View3D.Style.IdentifyLocationPoint</a>的定义</p>\n</div></div></div><div id='property-line' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.PointerStyle'>View3D.Style.PointerStyle</span><br/><a href='source/Style.html#View3D-Style-PointerStyle-property-line' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.PointerStyle-property-line' class='name expandable'>line</a> : Object<span class=\"signature\"></span></div><div class='description'><div class='short'><p>指向线属性，参照<a href=\"#!/api/View3D.Style.LineStyle\" rel=\"View3D.Style.LineStyle\" class=\"docClass\">View3D.Style.LineStyle</a>的定义</p>\n</div><div class='long'><p>指向线属性，参照<a href=\"#!/api/View3D.Style.LineStyle\" rel=\"View3D.Style.LineStyle\" class=\"docClass\">View3D.Style.LineStyle</a>的定义</p>\n</div></div></div></div></div></div></div>","meta":{}});