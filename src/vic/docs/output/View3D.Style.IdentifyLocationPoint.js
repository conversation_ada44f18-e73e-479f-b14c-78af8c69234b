Ext.data.JsonP.View3D_Style_IdentifyLocationPoint({"tagname":"class","name":"View3D.Style.IdentifyLocationPoint","autodetected":{},"files":[{"filename":"Style.js","href":"Style.html#View3D-Style-IdentifyLocationPoint"}],"members":[{"name":"color","tagname":"property","owner":"View3D.Style.IdentifyLocationPoint","id":"property-color","meta":{}},{"name":"radius","tagname":"property","owner":"View3D.Style.IdentifyLocationPoint","id":"property-radius","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-View3D.Style.IdentifyLocationPoint","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Style.html#View3D-Style-IdentifyLocationPoint' target='_blank'>Style.js</a></div></pre><div class='doc-contents'><p>标准起始点属性定义</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-color' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.IdentifyLocationPoint'>View3D.Style.IdentifyLocationPoint</span><br/><a href='source/Style.html#View3D-Style-IdentifyLocationPoint-property-color' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.IdentifyLocationPoint-property-color' class='name expandable'>color</a> : Color<span class=\"signature\"></span></div><div class='description'><div class='short'>点颜色，颜色中的alpha值无效 ...</div><div class='long'><p>点颜色，颜色中的alpha值无效</p>\n<p>Defaults to: <code>black</code></p></div></div></div><div id='property-radius' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.IdentifyLocationPoint'>View3D.Style.IdentifyLocationPoint</span><br/><a href='source/Style.html#View3D-Style-IdentifyLocationPoint-property-radius' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.IdentifyLocationPoint-property-radius' class='name expandable'>radius</a> : Number<span class=\"signature\"></span></div><div class='description'><div class='short'>点的半径 ...</div><div class='long'><p>点的半径</p>\n<p>Defaults to: <code>0</code></p></div></div></div></div></div></div></div>","meta":{}});