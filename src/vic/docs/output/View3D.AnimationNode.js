Ext.data.JsonP.View3D_AnimationNode({"tagname":"class","name":"View3D.AnimationNode","autodetected":{},"files":[{"filename":"AnimationNode.js","href":"AnimationNode.html#View3D-AnimationNode"}],"extends":"Node","members":[{"name":"createPlayAnimationCommand","tagname":"method","owner":"View3D.AnimationNode","id":"method-createPlayAnimationCommand","meta":{}},{"name":"createSetPlayModeCommand","tagname":"method","owner":"View3D.AnimationNode","id":"method-createSetPlayModeCommand","meta":{}},{"name":"createStopAnimationCommand","tagname":"method","owner":"View3D.AnimationNode","id":"method-createStopAnimationCommand","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-View3D.AnimationNode","component":false,"superclasses":["Node"],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Hierarchy</h4><div class='subclass first-child'>Node<div class='subclass '><strong>View3D.AnimationNode</strong></div></div><h4>Files</h4><div class='dependency'><a href='source/AnimationNode.html#View3D-AnimationNode' target='_blank'>AnimationNode.js</a></div></pre><div class='doc-contents'><p>动画节点</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div id='method-createPlayAnimationCommand' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.AnimationNode'>View3D.AnimationNode</span><br/><a href='source/AnimationNode.html#View3D-AnimationNode-method-createPlayAnimationCommand' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.AnimationNode-method-createPlayAnimationCommand' class='name expandable'>createPlayAnimationCommand</a>( <span class='pre'>animationName, [playOption]</span> ) : <a href=\"#!/api/View3D.Command\" rel=\"View3D.Command\" class=\"docClass\">View3D.Command</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建播放动画命令 ...</div><div class='long'><p>创建播放动画命令</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>animationName</span> : String<div class='sub-desc'><p>需要播放的动画名称</p>\n</div></li><li><span class='pre'>playOption</span> : Object (optional)<div class='sub-desc'><p>播放选项</p>\n<ul><li><span class='pre'>priority</span> : Number (optional)<div class='sub-desc'><p>播放选项，优先级（取值范围0~7）</p>\n<p>Defaults to: <code>0</code></p></div></li><li><span class='pre'>weight</span> : Number (optional)<div class='sub-desc'><p>播放选项，动画重量感（取值范围>0）</p>\n<p>Defaults to: <code>1.0</code></p></div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Command\" rel=\"View3D.Command\" class=\"docClass\">View3D.Command</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createSetPlayModeCommand' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.AnimationNode'>View3D.AnimationNode</span><br/><a href='source/AnimationNode.html#View3D-AnimationNode-method-createSetPlayModeCommand' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.AnimationNode-method-createSetPlayModeCommand' class='name expandable'>createSetPlayModeCommand</a>( <span class='pre'>animationName, playMode</span> ) : <a href=\"#!/api/View3D.Command\" rel=\"View3D.Command\" class=\"docClass\">View3D.Command</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建设置动画播放模式命令 ...</div><div class='long'><p>创建设置动画播放模式命令</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>animationName</span> : String<div class='sub-desc'><p>需要修改播放模式的动画名称</p>\n</div></li><li><span class='pre'>playMode</span> : String<div class='sub-desc'><p>动画播放模式\n- \"once\"    播放一次，完成之后恢复原状\n- \"stay\"    播放一次，完成之后保持最后的状态\n- \"loop\"    循环播放\n- \"ppong\"       正反依次循环播放</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Command\" rel=\"View3D.Command\" class=\"docClass\">View3D.Command</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createStopAnimationCommand' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.AnimationNode'>View3D.AnimationNode</span><br/><a href='source/AnimationNode.html#View3D-AnimationNode-method-createStopAnimationCommand' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.AnimationNode-method-createStopAnimationCommand' class='name expandable'>createStopAnimationCommand</a>( <span class='pre'>animationName</span> ) : <a href=\"#!/api/View3D.Command\" rel=\"View3D.Command\" class=\"docClass\">View3D.Command</a><span class=\"signature\"></span></div><div class='description'><div class='short'>创建停止动画命令 ...</div><div class='long'><p>创建停止动画命令</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>animationName</span> : String<div class='sub-desc'><p>需要停止的动画名称</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/View3D.Command\" rel=\"View3D.Command\" class=\"docClass\">View3D.Command</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div></div></div></div></div>","meta":{}});