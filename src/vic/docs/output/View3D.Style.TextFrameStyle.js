Ext.data.JsonP.View3D_Style_TextFrameStyle({"tagname":"class","name":"View3D.Style.TextFrameStyle","autodetected":{},"files":[{"filename":"Style.js","href":"Style.html#View3D-Style-TextFrameStyle"}],"members":[{"name":"border","tagname":"property","owner":"View3D.Style.TextFrameStyle","id":"property-border","meta":{}},{"name":"fillColor","tagname":"property","owner":"View3D.Style.TextFrameStyle","id":"property-fillColor","meta":{}},{"name":"padding","tagname":"property","owner":"View3D.Style.TextFrameStyle","id":"property-padding","meta":{}},{"name":"shape","tagname":"property","owner":"View3D.Style.TextFrameStyle","id":"property-shape","meta":{}},{"name":"text","tagname":"property","owner":"View3D.Style.TextFrameStyle","id":"property-text","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-View3D.Style.TextFrameStyle","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Style.html#View3D-Style-TextFrameStyle' target='_blank'>Style.js</a></div></pre><div class='doc-contents'><p>标准文本框属性定义</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-border' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.TextFrameStyle'>View3D.Style.TextFrameStyle</span><br/><a href='source/Style.html#View3D-Style-TextFrameStyle-property-border' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.TextFrameStyle-property-border' class='name expandable'>border</a> : Object<span class=\"signature\"></span></div><div class='description'><div class='short'><p>边框属性，参照<a href=\"#!/api/View3D.Style.BorderStyle\" rel=\"View3D.Style.BorderStyle\" class=\"docClass\">View3D.Style.BorderStyle</a>定义</p>\n</div><div class='long'><p>边框属性，参照<a href=\"#!/api/View3D.Style.BorderStyle\" rel=\"View3D.Style.BorderStyle\" class=\"docClass\">View3D.Style.BorderStyle</a>定义</p>\n</div></div></div><div id='property-fillColor' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.TextFrameStyle'>View3D.Style.TextFrameStyle</span><br/><a href='source/Style.html#View3D-Style-TextFrameStyle-property-fillColor' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.TextFrameStyle-property-fillColor' class='name expandable'>fillColor</a> : Color<span class=\"signature\"></span></div><div class='description'><div class='short'>填充色，颜色中的alpha值有效 ...</div><div class='long'><p>填充色，颜色中的alpha值有效</p>\n<p>Defaults to: <code>white</code></p></div></div></div><div id='property-padding' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.TextFrameStyle'>View3D.Style.TextFrameStyle</span><br/><a href='source/Style.html#View3D-Style-TextFrameStyle-property-padding' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.TextFrameStyle-property-padding' class='name expandable'>padding</a> : Number<span class=\"signature\"></span></div><div class='description'><div class='short'>文字离边框距离（像素） ...</div><div class='long'><p>文字离边框距离（像素）</p>\n<p>Defaults to: <code>2</code></p></div></div></div><div id='property-shape' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.TextFrameStyle'>View3D.Style.TextFrameStyle</span><br/><a href='source/Style.html#View3D-Style-TextFrameStyle-property-shape' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.TextFrameStyle-property-shape' class='name expandable'>shape</a> : String<span class=\"signature\"></span></div><div class='description'><div class='short'>文本框外形\n\nrectangle   矩形框\ncircle      圆形框 ...</div><div class='long'><p>文本框外形</p>\n\n<p>rectangle   矩形框\ncircle      圆形框</p>\n<p>Defaults to: <code>&quot;rectangle&quot;</code></p></div></div></div><div id='property-text' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.TextFrameStyle'>View3D.Style.TextFrameStyle</span><br/><a href='source/Style.html#View3D-Style-TextFrameStyle-property-text' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.TextFrameStyle-property-text' class='name expandable'>text</a> : Object<span class=\"signature\"></span></div><div class='description'><div class='short'><p>文本属性，参照<a href=\"#!/api/View3D.Style.TextStyle\" rel=\"View3D.Style.TextStyle\" class=\"docClass\">View3D.Style.TextStyle</a>定义</p>\n</div><div class='long'><p>文本属性，参照<a href=\"#!/api/View3D.Style.TextStyle\" rel=\"View3D.Style.TextStyle\" class=\"docClass\">View3D.Style.TextStyle</a>定义</p>\n</div></div></div></div></div></div></div>","meta":{}});