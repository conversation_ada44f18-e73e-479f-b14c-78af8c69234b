Ext.data.JsonP.View3D_Style_TextLabelStyle({"tagname":"class","name":"View3D.Style.TextLabelStyle","autodetected":{},"files":[{"filename":"Style.js","href":"Style.html#View3D-Style-TextLabelStyle"}],"members":[{"name":"connectPosition","tagname":"property","owner":"View3D.Style.TextLabelStyle","id":"property-connectPosition","meta":{}},{"name":"enableOvershadow","tagname":"property","owner":"View3D.Style.TextLabelStyle","id":"property-enableOvershadow","meta":{}},{"name":"fixSizeRange","tagname":"property","owner":"View3D.Style.TextLabelStyle","id":"property-fixSizeRange","meta":{}},{"name":"pointer","tagname":"property","owner":"View3D.Style.TextLabelStyle","id":"property-pointer","meta":{}},{"name":"textFrame","tagname":"property","owner":"View3D.Style.TextLabelStyle","id":"property-textFrame","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-View3D.Style.TextLabelStyle","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Style.html#View3D-Style-TextLabelStyle' target='_blank'>Style.js</a></div></pre><div class='doc-contents'><p>标准文本标签属性定义</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-connectPosition' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.TextLabelStyle'>View3D.Style.TextLabelStyle</span><br/><a href='source/Style.html#View3D-Style-TextLabelStyle-property-connectPosition' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.TextLabelStyle-property-connectPosition' class='name expandable'>connectPosition</a> : Object<span class=\"signature\"></span></div><div class='description'><div class='short'>线与边框连接位置 ...</div><div class='long'><p>线与边框连接位置</p>\n<p>Defaults to: <code>{x: &quot;center&quot;, y: &quot;bottom&quot;}</code></p><ul><li><span class='pre'>x</span> : String (optional)<div class='sub-desc'><p>线与边框连接位置</p>\n\n<ul>\n<li>\"left\"    左</li>\n<li>\"center\"  中</li>\n<li>\"right\"       右</li>\n</ul>\n\n<p>Defaults to: <code>&quot;center&quot;</code></p></div></li><li><span class='pre'>y</span> : String (optional)<div class='sub-desc'><p>垂直连接位置</p>\n\n<ul>\n<li>\"bottom\"  下</li>\n<li>\"middle\"  中</li>\n<li>\"top\"     上</li>\n</ul>\n\n<p>Defaults to: <code>&quot;bottom&quot;</code></p></div></li></ul></div></div></div><div id='property-enableOvershadow' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.TextLabelStyle'>View3D.Style.TextLabelStyle</span><br/><a href='source/Style.html#View3D-Style-TextLabelStyle-property-enableOvershadow' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.TextLabelStyle-property-enableOvershadow' class='name expandable'>enableOvershadow</a> : Boolean<span class=\"signature\"></span></div><div class='description'><div class='short'>文本标签是否需要一直显示不会被遮挡\n如果该值设置为false，那么文本标注会在所有模型之前显示，永远不会遮挡\n 如果该值设置为true，从看得视角来说该文本标注会被在它之前的模型挡住，转动视角该看见的时候也会看见，且标注是一直面向屏幕的 ...</div><div class='long'><p>文本标签是否需要一直显示不会被遮挡\n如果该值设置为false，那么文本标注会在所有模型之前显示，永远不会遮挡\n 如果该值设置为true，从看得视角来说该文本标注会被在它之前的模型挡住，转动视角该看见的时候也会看见，且标注是一直面向屏幕的</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-fixSizeRange' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.TextLabelStyle'>View3D.Style.TextLabelStyle</span><br/><a href='source/Style.html#View3D-Style-TextLabelStyle-property-fixSizeRange' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.TextLabelStyle-property-fixSizeRange' class='name expandable'>fixSizeRange</a> : Object<span class=\"signature\"></span></div><div class='description'><div class='short'>文本标签固定大小的显示范围，没有量纲，max应该大于min\n 文本标签在该范围内能够维持固定大小，否则不维持屏幕占比\n\n例如：fixSizeRange{0, 10}，即文本标签固定大小的显示范围为0-10 ...</div><div class='long'><p>文本标签固定大小的显示范围，没有量纲，max应该大于min\n 文本标签在该范围内能够维持固定大小，否则不维持屏幕占比</p>\n\n<p>例如：fixSizeRange{0, 10}，即文本标签固定大小的显示范围为0-10</p>\n<ul><li><span class='pre'>min</span> : Number (optional)<div class='sub-desc'><p>最小值（取值范围[0,+∞)），取0代表文本会随着视点距离的靠近会无限缩小，一直维持在屏幕显示的大小不变</p>\n<p>Defaults to: <code>0</code></p></div></li><li><span class='pre'>max</span> : Number (optional)<div class='sub-desc'><p>最大值（取值范围(0,+∞)），默认值表示最大放大倍数为3.4E38倍，超过该倍数无法维持占屏幕大小不变</p>\n<p>Defaults to: <code>3.4E38</code></p></div></li></ul></div></div></div><div id='property-pointer' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.TextLabelStyle'>View3D.Style.TextLabelStyle</span><br/><a href='source/Style.html#View3D-Style-TextLabelStyle-property-pointer' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.TextLabelStyle-property-pointer' class='name expandable'>pointer</a> : Object<span class=\"signature\"></span></div><div class='description'><div class='short'><p>指向线及点的属性，参照<a href=\"#!/api/View3D.Style.PointerStyle\" rel=\"View3D.Style.PointerStyle\" class=\"docClass\">View3D.Style.PointerStyle</a>的定义</p>\n</div><div class='long'><p>指向线及点的属性，参照<a href=\"#!/api/View3D.Style.PointerStyle\" rel=\"View3D.Style.PointerStyle\" class=\"docClass\">View3D.Style.PointerStyle</a>的定义</p>\n</div></div></div><div id='property-textFrame' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.TextLabelStyle'>View3D.Style.TextLabelStyle</span><br/><a href='source/Style.html#View3D-Style-TextLabelStyle-property-textFrame' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.TextLabelStyle-property-textFrame' class='name expandable'>textFrame</a> : Object<span class=\"signature\"></span></div><div class='description'><div class='short'><p>文本框属性，参照<a href=\"#!/api/View3D.Style.TextFrameStyle\" rel=\"View3D.Style.TextFrameStyle\" class=\"docClass\">View3D.Style.TextFrameStyle</a>的定义</p>\n</div><div class='long'><p>文本框属性，参照<a href=\"#!/api/View3D.Style.TextFrameStyle\" rel=\"View3D.Style.TextFrameStyle\" class=\"docClass\">View3D.Style.TextFrameStyle</a>的定义</p>\n</div></div></div></div></div></div></div>","meta":{}});