Ext.data.JsonP.Factory({"tagname":"class","name":"Factory","autodetected":{},"files":[{"filename":"Factory.js","href":"Factory.html#Factory"}],"members":[{"name":"createView3D","tagname":"method","owner":"Factory","id":"method-createView3D","meta":{}},{"name":"createViewPID","tagname":"method","owner":"Factory","id":"method-createViewPID","meta":{}},{"name":"releaseView","tagname":"method","owner":"Factory","id":"method-releaseView","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-Factory","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Factory.html#Factory' target='_blank'>Factory.js</a></div></pre><div class='doc-contents'><p>创建PID和三维显示窗口</p>\n\n<p>应用不应该构建，应该通过createFactory创建得到</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div id='method-createView3D' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Factory'>Factory</span><br/><a href='source/Factory.html#Factory-method-createView3D' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Factory-method-createView3D' class='name expandable'>createView3D</a>( <span class='pre'>elem, callback, mode, [option]</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>参数具体信息说明\n创建3D显示窗口 ...</div><div class='long'><p><a href=\"https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/1148354569/5.8.0\">参数具体信息说明</a>\n创建3D显示窗口</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>elem</span> : HTMLElement<div class='sub-desc'><p>父元素</p>\n</div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>创建完成后回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>创建失败返回错误</p>\n</div></li><li><span class='pre'>view3D</span> : <a href=\"#!/api/View3D.View3D\" rel=\"View3D.View3D\" class=\"docClass\">View3D.View3D</a><div class='sub-desc'><p>创建成功后返回显示窗口</p>\n</div></li></ul></div></li><li><span class='pre'>mode</span> : Number<div class='sub-desc'><p>三维传输模式\n                                                                0: Compatible表示兼容模式（兼容IE,chrome）\n                                                            1: DataSaver表示省流模式，带宽占用低（只支持chrome）</p>\n</div></li><li><span class='pre'>option</span> : Object (optional)<div class='sub-desc'><p>3D显示窗口配置参数</p>\n<ul><li><span class='pre'>maxFPS</span> : Number (optional)<div class='sub-desc'><p>最大帧率</p>\n<p>Defaults to: <code>20</code></p></div></li><li><span class='pre'>LODScale</span> : Number (optional)<div class='sub-desc'><p>可控制场景中模型的加载数量</p>\n<p>Defaults to: <code>1</code></p></div></li><li><span class='pre'>maxPagedLODNumber</span> : Number (optional)<div class='sub-desc'><p>最大加载PagedLOD数量</p>\n<p>Defaults to: <code>500</code></p></div></li><li><span class='pre'>enableTransparency</span> : boolean (optional)<div class='sub-desc'><p>模型颜色带透明位的是否显示为不透明</p>\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>loadDurMoving</span> : boolean (optional)<div class='sub-desc'><p>场景移动过程中是否允许加载模型</p>\n<p>Defaults to: <code>true</code></p></div></li><li><span class='pre'>maxFrameTime</span> : Number (optional)<div class='sub-desc'><p>渲染一帧允许的最大时间</p>\n<p>Defaults to: <code>0.1</code></p></div></li><li><span class='pre'>computeNearFarMode</span> : Number (optional)<div class='sub-desc'><p>计算远近平面的方式\n                                                                0: PrecisionMode表示精准模式\n                                                            1: FastMode表示快速模式\n                                                             FastMode可以解决某些视角下漫游会卡的问题，但可能导致模型闪面</p>\n<p>Defaults to: <code>0</code></p></div></li><li><span class='pre'>backgroundColor</span> : Color (optional)<div class='sub-desc'><p>默认背景颜色</p>\n<p>Defaults to: <code>COLOR('rgb(255, 255, 255)').alpha(1)</code></p></div></li><li><span class='pre'>globalOutlineColor</span> : Color (optional)<div class='sub-desc'><p>默认描边颜色</p>\n<p>Defaults to: <code>COLOR('rgb(51, 51, 51)').alpha(1)</code></p></div></li><li><span class='pre'>globalOutlineMixRatio</span> : Number (optional)<div class='sub-desc'><p>默认描边颜色混合占比</p>\n<p>Defaults to: <code>0.6</code></p></div></li></ul></div></li></ul></div></div></div><div id='method-createViewPID' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Factory'>Factory</span><br/><a href='source/Factory.html#Factory-method-createViewPID' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Factory-method-createViewPID' class='name expandable'>createViewPID</a>( <span class='pre'>elem, callback</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>创建PID显示窗口 ...</div><div class='long'><p>创建PID显示窗口</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>elem</span> : HTMLElement<div class='sub-desc'><p>父元素</p>\n</div></li><li><span class='pre'>callback</span> : Function<div class='sub-desc'><p>创建完成后回调</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>err</span> : Error<div class='sub-desc'><p>创建失败返回错误</p>\n</div></li><li><span class='pre'>viewPID</span> : <a href=\"#!/api/ViewPID.ViewPID\" rel=\"ViewPID.ViewPID\" class=\"docClass\">ViewPID.ViewPID</a><div class='sub-desc'><p>创建成功后返回显示窗口</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-releaseView' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Factory'>Factory</span><br/><a href='source/Factory.html#Factory-method-releaseView' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Factory-method-releaseView' class='name expandable'>releaseView</a>( <span class='pre'>views</span> )<span class=\"signature\"></span></div><div class='description'><div class='short'>释放窗口资源 ...</div><div class='long'><p>释放窗口资源</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>views</span> : View3D\\ViewPID[]<div class='sub-desc'><p>待释放的窗口资源集合</p>\n</div></li></ul></div></div></div></div></div></div></div>","meta":{}});