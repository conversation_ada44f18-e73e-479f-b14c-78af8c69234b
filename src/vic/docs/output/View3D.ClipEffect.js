Ext.data.JsonP.View3D_ClipEffect({"tagname":"class","name":"View3D.ClipEffect","autodetected":{},"files":[{"filename":"Effects.js","href":"Effects.html#View3D-ClipEffect"}],"extends":"View3D.Effect","members":[],"alternateClassNames":[],"aliases":{},"id":"class-View3D.ClipEffect","component":false,"superclasses":["View3D.Effect"],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Hierarchy</h4><div class='subclass first-child'><a href='#!/api/View3D.Effect' rel='View3D.Effect' class='docClass'>View3D.Effect</a><div class='subclass '><strong>View3D.ClipEffect</strong></div></div><h4>Files</h4><div class='dependency'><a href='source/Effects.html#View3D-ClipEffect' target='_blank'>Effects.js</a></div></pre><div class='doc-contents'><p>剖切效果</p>\n</div><div class='members'></div></div>","meta":{}});