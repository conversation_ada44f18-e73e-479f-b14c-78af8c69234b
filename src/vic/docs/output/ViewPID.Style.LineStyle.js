Ext.data.JsonP.ViewPID_Style_LineStyle({"tagname":"class","name":"ViewPID.Style.LineStyle","autodetected":{},"files":[{"filename":"Style.js","href":"Style2.html#ViewPID-Style-LineStyle"}],"members":[{"name":"color","tagname":"property","owner":"ViewPID.Style.LineStyle","id":"property-color","meta":{}},{"name":"type","tagname":"property","owner":"ViewPID.Style.LineStyle","id":"property-type","meta":{}},{"name":"width","tagname":"property","owner":"ViewPID.Style.LineStyle","id":"property-width","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-ViewPID.Style.LineStyle","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Style2.html#ViewPID-Style-LineStyle' target='_blank'>Style.js</a></div></pre><div class='doc-contents'><p>标准线属性定义</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-color' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.Style.LineStyle'>ViewPID.Style.LineStyle</span><br/><a href='source/Style2.html#ViewPID-Style-LineStyle-property-color' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.Style.LineStyle-property-color' class='name expandable'>color</a> : Color<span class=\"signature\"></span></div><div class='description'><div class='short'>线色，颜色中的alpha值无效 ...</div><div class='long'><p>线色，颜色中的alpha值无效</p>\n<p>Defaults to: <code>white</code></p></div></div></div><div id='property-type' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.Style.LineStyle'>ViewPID.Style.LineStyle</span><br/><a href='source/Style2.html#ViewPID-Style-LineStyle-property-type' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.Style.LineStyle-property-type' class='name expandable'>type</a> : String<span class=\"signature\"></span></div><div class='description'><div class='short'>线型\n\n\n\"solid\"   实线\n\"stipple\"    虚线 ...</div><div class='long'><p>线型</p>\n\n<ul>\n<li>\"solid\"   实线</li>\n<li>\"stipple\"    虚线</li>\n</ul>\n\n<p>Defaults to: <code>&quot;solid&quot;</code></p></div></div></div><div id='property-width' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='ViewPID.Style.LineStyle'>ViewPID.Style.LineStyle</span><br/><a href='source/Style2.html#ViewPID-Style-LineStyle-property-width' target='_blank' class='view-source'>view source</a></div><a href='#!/api/ViewPID.Style.LineStyle-property-width' class='name expandable'>width</a> : Number<span class=\"signature\"></span></div><div class='description'><div class='short'>线宽（单位：厘米）线宽取值范围（0.0，0.3，0.35，0.4，0.5，0.6，0.7，0.8，0.9，1.0\n                                    ，1.2，1.4，1.58，2.0，2.11。）...</div><div class='long'><p>线宽（单位：厘米）线宽取值范围（0.0，0.3，0.35，0.4，0.5，0.6，0.7，0.8，0.9，1.0\n                                    ，1.2，1.4，1.58，2.0，2.11。）若为0~2.11范围内的其他值，则绘制效果与0.0相同。</p>\n<p>Defaults to: <code>0</code></p></div></div></div></div></div></div></div>","meta":{}});