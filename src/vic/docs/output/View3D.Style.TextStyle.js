Ext.data.JsonP.View3D_Style_TextStyle({"tagname":"class","name":"View3D.Style.TextStyle","autodetected":{},"files":[{"filename":"Style.js","href":"Style.html#View3D-Style-TextStyle"}],"members":[{"name":"alignment","tagname":"property","owner":"View3D.Style.TextStyle","id":"property-alignment","meta":{}},{"name":"backdropColor","tagname":"property","owner":"View3D.Style.TextStyle","id":"property-backdropColor","meta":{}},{"name":"backdropType","tagname":"property","owner":"View3D.Style.TextStyle","id":"property-backdropType","meta":{}},{"name":"color","tagname":"property","owner":"View3D.Style.TextStyle","id":"property-color","meta":{}},{"name":"fontName","tagname":"property","owner":"View3D.Style.TextStyle","id":"property-fontName","meta":{}},{"name":"lineSpacing","tagname":"property","owner":"View3D.Style.TextStyle","id":"property-lineSpacing","meta":{}},{"name":"size","tagname":"property","owner":"View3D.Style.TextStyle","id":"property-size","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-View3D.Style.TextStyle","component":false,"superclasses":[],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Style.html#View3D-Style-TextStyle' target='_blank'>Style.js</a></div></pre><div class='doc-contents'><p>标准文本属性定义</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-alignment' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.TextStyle'>View3D.Style.TextStyle</span><br/><a href='source/Style.html#View3D-Style-TextStyle-property-alignment' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.TextStyle-property-alignment' class='name expandable'>alignment</a> : String<span class=\"signature\"></span></div><div class='description'><div class='short'>多行文本之间对齐方式\n\n\n\"left\"    左对齐\n\"center\" 居中对齐\n\"right\"   右对齐 ...</div><div class='long'><p>多行文本之间对齐方式</p>\n\n<ul>\n<li>\"left\"    左对齐</li>\n<li>\"center\" 居中对齐</li>\n<li>\"right\"   右对齐</li>\n</ul>\n\n<p>Defaults to: <code>&quot;left&quot;</code></p></div></div></div><div id='property-backdropColor' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.TextStyle'>View3D.Style.TextStyle</span><br/><a href='source/Style.html#View3D-Style-TextStyle-property-backdropColor' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.TextStyle-property-backdropColor' class='name expandable'>backdropColor</a> : Color<span class=\"signature\"></span></div><div class='description'><div class='short'>阴影效果的颜色，颜色中的alpha值无效 ...</div><div class='long'><p>阴影效果的颜色，颜色中的alpha值无效</p>\n<p>Defaults to: <code>black</code></p></div></div></div><div id='property-backdropType' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.TextStyle'>View3D.Style.TextStyle</span><br/><a href='source/Style.html#View3D-Style-TextStyle-property-backdropType' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.TextStyle-property-backdropType' class='name expandable'>backdropType</a> : Number<span class=\"signature\"></span></div><div class='description'><div class='short'><p>字体的阴影效果，取值范围[0,8]（<a href=\"https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/75957780/VIC+Style\">具体效果详见</a>）</p>\n</div><div class='long'><p>字体的阴影效果，取值范围[0,8]（<a href=\"https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/75957780/VIC+Style\">具体效果详见</a>）</p>\n</div></div></div><div id='property-color' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.TextStyle'>View3D.Style.TextStyle</span><br/><a href='source/Style.html#View3D-Style-TextStyle-property-color' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.TextStyle-property-color' class='name expandable'>color</a> : Color<span class=\"signature\"></span></div><div class='description'><div class='short'>字体色，颜色中的alpha值无效 ...</div><div class='long'><p>字体色，颜色中的alpha值无效</p>\n<p>Defaults to: <code>black</code></p></div></div></div><div id='property-fontName' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.TextStyle'>View3D.Style.TextStyle</span><br/><a href='source/Style.html#View3D-Style-TextStyle-property-fontName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.TextStyle-property-fontName' class='name expandable'>fontName</a> : String<span class=\"signature\"></span></div><div class='description'><div class='short'>字体名，设置为空，默认为楷体：simkai（该名与当前操作系统（c:\\windows\\fonts）下的字体名对应，若不满足时，可以安装新的字体） ...</div><div class='long'><p>字体名，设置为空，默认为楷体：simkai（该名与当前操作系统（c:\\windows\\fonts）下的字体名对应，若不满足时，可以安装新的字体）</p>\n<p>Defaults to: <code>&quot;&quot;</code></p></div></div></div><div id='property-lineSpacing' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.TextStyle'>View3D.Style.TextStyle</span><br/><a href='source/Style.html#View3D-Style-TextStyle-property-lineSpacing' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.TextStyle-property-lineSpacing' class='name expandable'>lineSpacing</a> : Number<span class=\"signature\"></span></div><div class='description'><div class='short'>行间距，以字符高度的百分比表示；建议值为25％（即，将行距设置为0.25） ...</div><div class='long'><p>行间距，以字符高度的百分比表示；建议值为25％（即，将行距设置为0.25）</p>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-size' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.Style.TextStyle'>View3D.Style.TextStyle</span><br/><a href='source/Style.html#View3D-Style-TextStyle-property-size' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.Style.TextStyle-property-size' class='name expandable'>size</a> : Number<span class=\"signature\"></span></div><div class='description'><div class='short'>字体大小（像素）,定义的是文字高度，宽度自动调整 ...</div><div class='long'><p>字体大小（像素）,定义的是文字高度，宽度自动调整</p>\n<p>Defaults to: <code>15</code></p></div></div></div></div></div></div></div>","meta":{}});