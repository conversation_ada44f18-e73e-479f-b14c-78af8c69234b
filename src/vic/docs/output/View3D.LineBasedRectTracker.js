Ext.data.JsonP.View3D_LineBasedRectTracker({"tagname":"class","name":"View3D.LineBasedRectTracker","autodetected":{},"files":[{"filename":"LineBasedRectTracker.js","href":"LineBasedRectTracker.html#View3D-LineBasedRectTracker"}],"extends":"Tracker","members":[{"name":"calcRectPts","tagname":"method","owner":"View3D.LineBasedRectTracker","id":"method-calcRectPts","meta":{}}],"alternateClassNames":[],"aliases":{},"id":"class-View3D.LineBasedRectTracker","component":false,"superclasses":["Tracker"],"subclasses":[],"mixedInto":[],"mixins":[],"parentMixins":[],"requires":[],"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Hierarchy</h4><div class='subclass first-child'>Tracker<div class='subclass '><strong>View3D.LineBasedRectTracker</strong></div></div><h4>Files</h4><div class='dependency'><a href='source/LineBasedRectTracker.html#View3D-LineBasedRectTracker' target='_blank'>LineBasedRectTracker.js</a></div></pre><div class='doc-contents'><p>基于线的矩形tracker</p>\n\n<p>可用于绘制矩形时展现绘制的动态效果</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div id='method-calcRectPts' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='View3D.LineBasedRectTracker'>View3D.LineBasedRectTracker</span><br/><a href='source/LineBasedRectTracker.html#View3D-LineBasedRectTracker-method-calcRectPts' target='_blank' class='view-source'>view source</a></div><a href='#!/api/View3D.LineBasedRectTracker-method-calcRectPts' class='name expandable'>calcRectPts</a>( <span class='pre'>baseLine, point</span> ) : vec3[]<span class=\"signature\"></span></div><div class='description'><div class='short'>根据起始线和另外一个不在线上的点计算出矩形对应的四个点 ...</div><div class='long'><p>根据起始线和另外一个不在线上的点计算出矩形对应的四个点</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>baseLine</span> : Object<div class='sub-desc'><p>矩形的起始边</p>\n<ul><li><span class='pre'>startPt</span> : vec3<div class='sub-desc'><p>矩形起始边的起点</p>\n</div></li><li><span class='pre'>endPt</span> : vec3<div class='sub-desc'><p>矩形起始边的终点</p>\n</div></li></ul></div></li><li><span class='pre'>point</span> : vec3<div class='sub-desc'><p>不在线上的另外一个点</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>vec3[]</span><div class='sub-desc'><p>矩形的四个顶点的点组</p>\n</div></li></ul></div></div></div></div></div></div></div>","meta":{}});