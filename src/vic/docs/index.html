<!DOCTYPE html>
<html>
<head>
  <title>Documentation - JSDuck</title>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta http-equiv="X-UA-Compatible" content="chrome=1">
  <meta name="fragment" content="!">

  

  <link rel="shortcut icon" type="image/ico" href="favicon.ico" />
  <link rel="stylesheet" href="resources/css/app-4689d2a5522dcd3c9e9923ca59c33f27.css" type="text/css" />
  <link rel="stylesheet" href="styles-8184dad50dadb76aad743829a7c19ed0.css" type="text/css" />

  <script type="text/javascript" src="extjs/ext-all.js"></script>
  <script type="text/javascript" src="data-95c2a98cfe26b3601ce6a967b74f4af0.js"></script>

  <script type="text/javascript" src="app-0c945a27f43452df695771ddb60b3d14.js"></script>

  

</head>
<body id="ext-body">

  <div id="loading"><span class="title">Documentation - JSDuck</span><span class="logo"></span></div>

  <div id="header-content"><strong>Documentation</strong> JSDuck</div>
  <div id='welcome-content' style='display:none'><!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<style> 
      body{text-align:left} 
    </style> 
	<style type="text/css">
	#testDiv{  width: 55%;  
    height: auto;  
    position:absolute;
	left:0;
    top: 0;
    bottom: 0;
    right: 0;
    margin: auto;
    word-wrap:break-word;  
    word-break:break-all;}
	</style>

</head>
<body>
	<div id="testDiv">
        <div style="background-color:#f6f5f5;border:2px solid #f1ecec">
            <br />
            <h1 style="text-align:center">欢迎使用VIC的API文档</h1>
            <br />

            <h2><font size="4"><b>VIC使用的第三方库：</b></font></h2>
			<br />
            <a href="http://glmatrix.net/docs/" target="_blank"><font size="3">1.gl-matrix-double 用于一些基本的向量、点、矩阵的创建和运算</font></a>
            <h2>&emsp; Notice: VIC中使用到的vec3、vec4、matrix都是基于gl-matrix-double构建的对象</h2>
            <a href="https://www.npmjs.com/package/color" target="_blank"><font size="3">2.COLOR 支持颜色的转换和使用</font></a>

        </div>

	    <div style="background-color:#f6f5f5;border:2px solid #f1ecec;margin-top:20px">
            <br />
            <h2><font size="4"><b>VIC简介：</b></font></h2>
			<br />
            <font size="3">
                <a href="https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/75957983/VIC" target="_blank">VIC使用指南</a>
                <br />
                <br />
                <a href="https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/75957872/gl-matrix-double" target="_blank">gl-matrix-double 使用指南</a>
                <br />
                <br />
                <a href="https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/75956863/VIC+FAQ" target="_blank">VIC FAQ</a>
                <br />
                <br />
                <a href="https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/75957780/VIC+Style" target="_blank">VIC Style基础属性</a>
                <br />
                <br />
                <a href="https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/75957738/VIC" target="_blank">VIC 三维端基本概念</a>
                <br />
                <br />
                <a href="https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/75956914" target="_blank">光照参数说明</a>
                <br />
                <br />
                <a href="https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/75956834" target="_blank">图形学基本知识</a>
                <br />
                <br />
                <a href="https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/75958213/VIC" target="_blank">VIC使用示例</a>
                <br />
                <br />
                <a href="https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/75957209/VIC" target="_blank">VIC核心模型</a>
                <br />
                <br />
                <a href="https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/556040496/VIC" target="_blank">接口执行时间统计开关</a>
            </font>
        </div>
	</div>
</body>
</html></div>
            <div id='categories-content' style='display:none'>
            <div class='section'>
<h1>Error</h1>
<div class='left-column'>
<h3>Others...</h3>
<ul class='links'>
<li><a href="#!/api/Error.DataError" rel="Error.DataError" class="docClass">Error.DataError</a></li>
<li><a href="#!/api/Error.InvalidArgumentError" rel="Error.InvalidArgumentError" class="docClass">Error.InvalidArgumentError</a></li>
<li><a href="#!/api/Error.NetworkError" rel="Error.NetworkError" class="docClass">Error.NetworkError</a></li>
<li><a href="#!/api/Error.SystemError" rel="Error.SystemError" class="docClass">Error.SystemError</a></li>
<li><a href="#!/api/Error.VersioningError" rel="Error.VersioningError" class="docClass">Error.VersioningError</a></li>
</ul>
</div>
<div class='middle-column'>
</div>
<div class='right-column'>
</div>
<div style='clear:both'></div>
</div>
<div class='section'>
<h1>Global</h1>
<div class='left-column'>
<h3>Others...</h3>
<ul class='links'>
<li><a href="#!/api/Global.createFactory" rel="Global.createFactory" class="docClass">Global.createFactory</a></li>
<li><a href="#!/api/Global.releaseFactory" rel="Global.releaseFactory" class="docClass">Global.releaseFactory</a></li>
</ul>
</div>
<div class='middle-column'>
</div>
<div class='right-column'>
</div>
<div style='clear:both'></div>
</div>
<div class='section'>
<h1>View3D</h1>
<div class='left-column'>
<h3>Style</h3>
<ul class='links'>
<li><a href="#!/api/View3D.Style" rel="View3D.Style" class="docClass">View3D.Style</a></li>
<li><a href="#!/api/View3D.Style.AxisStyle" rel="View3D.Style.AxisStyle" class="docClass">View3D.Style.AxisStyle</a></li>
<li><a href="#!/api/View3D.Style.BorderStyle" rel="View3D.Style.BorderStyle" class="docClass">View3D.Style.BorderStyle</a></li>
<li><a href="#!/api/View3D.Style.IdentifyLocationPoint" rel="View3D.Style.IdentifyLocationPoint" class="docClass">View3D.Style.IdentifyLocationPoint</a></li>
<li><a href="#!/api/View3D.Style.LightStyle" rel="View3D.Style.LightStyle" class="docClass">View3D.Style.LightStyle</a></li>
<li><a href="#!/api/View3D.Style.LineStyle" rel="View3D.Style.LineStyle" class="docClass">View3D.Style.LineStyle</a></li>
<li><a href="#!/api/View3D.Style.PictureLabelStyle" rel="View3D.Style.PictureLabelStyle" class="docClass">View3D.Style.PictureLabelStyle</a></li>
<li><a href="#!/api/View3D.Style.PointStyle" rel="View3D.Style.PointStyle" class="docClass">View3D.Style.PointStyle</a></li>
<li><a href="#!/api/View3D.Style.PointerStyle" rel="View3D.Style.PointerStyle" class="docClass">View3D.Style.PointerStyle</a></li>
<li><a href="#!/api/View3D.Style.TextFrameStyle" rel="View3D.Style.TextFrameStyle" class="docClass">View3D.Style.TextFrameStyle</a></li>
<li><a href="#!/api/View3D.Style.TextLabelStyle" rel="View3D.Style.TextLabelStyle" class="docClass">View3D.Style.TextLabelStyle</a></li>
<li><a href="#!/api/View3D.Style.TextStyle" rel="View3D.Style.TextStyle" class="docClass">View3D.Style.TextStyle</a></li>
</ul>
</div>
<div class='middle-column'>
<h3>Others...</h3>
<ul class='links'>
<li><a href="#!/api/View3D.AnimationNode" rel="View3D.AnimationNode" class="docClass">View3D.AnimationNode</a></li>
<li><a href="#!/api/View3D.ClipEffect" rel="View3D.ClipEffect" class="docClass">View3D.ClipEffect</a></li>
<li><a href="#!/api/View3D.Command" rel="View3D.Command" class="docClass">View3D.Command</a></li>
<li><a href="#!/api/View3D.DraggableEffect" rel="View3D.DraggableEffect" class="docClass">View3D.DraggableEffect</a></li>
<li><a href="#!/api/View3D.Dragger" rel="View3D.Dragger" class="docClass">View3D.Dragger</a></li>
<li><a href="#!/api/View3D.DrawingRootNode" rel="View3D.DrawingRootNode" class="docClass">View3D.DrawingRootNode</a></li>
<li><a href="#!/api/View3D.Effect" rel="View3D.Effect" class="docClass">View3D.Effect</a></li>
<li><a href="#!/api/View3D.EffectFactory" rel="View3D.EffectFactory" class="docClass">View3D.EffectFactory</a></li>
<li><a href="#!/api/View3D.Filter" rel="View3D.Filter" class="docClass">View3D.Filter</a></li>
<li><a href="#!/api/View3D.Interaction" rel="View3D.Interaction" class="docClass">View3D.Interaction</a></li>
<li><a href="#!/api/View3D.InteractionFactory" rel="View3D.InteractionFactory" class="docClass">View3D.InteractionFactory</a></li>
<li><a href="#!/api/View3D.LightEffect" rel="View3D.LightEffect" class="docClass">View3D.LightEffect</a></li>
<li><a href="#!/api/View3D.LineBasedRectTracker" rel="View3D.LineBasedRectTracker" class="docClass">View3D.LineBasedRectTracker</a></li>
<li><a href="#!/api/View3D.Node" rel="View3D.Node" class="docClass">View3D.Node</a></li>
<li><a href="#!/api/View3D.NodeFactory" rel="View3D.NodeFactory" class="docClass">View3D.NodeFactory</a></li>
<li><a href="#!/api/View3D.Roamer" rel="View3D.Roamer" class="docClass">View3D.Roamer</a></li>
<li><a href="#!/api/View3D.RoamerFactory" rel="View3D.RoamerFactory" class="docClass">View3D.RoamerFactory</a></li>
<li><a href="#!/api/View3D.Tracker" rel="View3D.Tracker" class="docClass">View3D.Tracker</a></li>
<li><a href="#!/api/View3D.TransparencyEffect" rel="View3D.TransparencyEffect" class="docClass">View3D.TransparencyEffect</a></li>
<li><a href="#!/api/View3D.View3D" rel="View3D.View3D" class="docClass">View3D.View3D</a></li>
<li><a href="#!/api/View3D.Viewpoint" rel="View3D.Viewpoint" class="docClass">View3D.Viewpoint</a></li>
</ul>
</div>
<div class='right-column'>
</div>
<div style='clear:both'></div>
</div>
<div class='section'>
<h1>ViewPID</h1>
<div class='left-column'>
<h3>Style</h3>
<ul class='links'>
<li><a href="#!/api/ViewPID.Style" rel="ViewPID.Style" class="docClass">ViewPID.Style</a></li>
<li><a href="#!/api/ViewPID.Style.ArcStyle" rel="ViewPID.Style.ArcStyle" class="docClass">ViewPID.Style.ArcStyle</a></li>
<li><a href="#!/api/ViewPID.Style.LineStyle" rel="ViewPID.Style.LineStyle" class="docClass">ViewPID.Style.LineStyle</a></li>
<li><a href="#!/api/ViewPID.Style.PolygonStyle" rel="ViewPID.Style.PolygonStyle" class="docClass">ViewPID.Style.PolygonStyle</a></li>
<li><a href="#!/api/ViewPID.Style.TextFrameStyle" rel="ViewPID.Style.TextFrameStyle" class="docClass">ViewPID.Style.TextFrameStyle</a></li>
<li><a href="#!/api/ViewPID.Style.TextStyle" rel="ViewPID.Style.TextStyle" class="docClass">ViewPID.Style.TextStyle</a></li>
</ul>
</div>
<div class='middle-column'>
<h3>Others...</h3>
<ul class='links'>
<li><a href="#!/api/ViewPID.Effect" rel="ViewPID.Effect" class="docClass">ViewPID.Effect</a></li>
<li><a href="#!/api/ViewPID.ViewPID" rel="ViewPID.ViewPID" class="docClass">ViewPID.ViewPID</a></li>
</ul>
</div>
<div class='right-column'>
</div>
<div style='clear:both'></div>
</div>
<div class='section'>
<h1>Others...</h1>
<div class='left-column'>
<h3>Others...</h3>
<ul class='links'>
<li><a href="#!/api/Factory" rel="Factory" class="docClass">Factory</a></li>
</ul>
</div>
<div class='middle-column'>
</div>
<div class='right-column'>
</div>
<div style='clear:both'></div>
</div>
          </div>

  
  
  <div id='footer-content' style='display: none'>Generated on Tue 17 Jan 2023 14:35:31 by <a href='https://github.com/senchalabs/jsduck'>JSDuck</a> 5.3.4.</div>

  

  <script type="text/javascript">
  (function(){
    var protocol = (document.location.protocol === "https:") ? "https:" : "http:";
    document.write("<link href='"+protocol+"//fonts.googleapis.com/css?family=Exo' rel='stylesheet' type='text/css' />");
  })();
  </script>

</body>
</html>
