var COLOR = require("color");
	
var black = COLOR('rgb(0, 0, 0)').alpha(1);
var white = COLOR('rgb(255, 255, 255)').alpha(1);
var red = COLOR('rgb(255, 0, 0)').alpha(1);

/**
 * @class ViewPID.Style
 * 标准属性定义
 */
var Style = { };
 
/**
 * @class ViewPID.Style.LineStyle
 * 标准线属性定义 
 */
Style.LineStyle = {
	/**
	 * @property {Color}  [color=white] 	线色，颜色中的alpha值无效
	 */
	color: white,
	/**
	 * @property {Number} [width=0] 		线宽（单位：厘米）线宽取值范围（0.0，0.3，0.35，0.4，0.5，0.6，0.7，0.8，0.9，1.0
	 *										，1.2，1.4，1.58，2.0，2.11。）若为0~2.11范围内的其他值，则绘制效果与0.0相同。
	 */
	width: 0,
    /**
	 * @property {String} [type="solid"]	线型
	 *
	 *	- "solid" 	实线
	 *  - "stipple"	虚线
	 */
	type: 'solid'
};

/**
 * @class ViewPID.Style.TextStyle
 * 标准文本属性定义 
 */
Style.TextStyle = {
	/**
	 * @property {Color} 	[color=white] 				字体色，颜色中的alpha值无效
	 */
	color: white,
	/**
	 * @property {Number} 	[size=5] 					字体大小（毫米）,定义的是文字高度，宽度自动调整	
	 */
	size: 5,
	/**
	 * @property {String} 	[fontName="楷体"]			字体名，默认为楷体（该名与当前操作系统（c:\windows\fonts）下的字体文件名对应，若不满足时，可以安装新的字体）
	 */
	fontName: "楷体"
};

/**
 * @class ViewPID.Style.TextFrameStyle
 * 标准文本框属性定义 
 */
Style.TextFrameStyle = {
	/**
	 * @property {boolean} 	[showBorders=false]				控制文本边框的可见性（边框的颜色随字体的颜色）
	 */
	showBorders: false,
	/**
	 * @property {boolean} 	[backgroundFill=false]			控制是否填充背景色
	 */
	backgroundFill: false,
	/**
	 * @property {Color} 	[backgroundFillColor=black]		文字背景填充色，颜色中的alpha值无效
	 */
	backgroundFillColor: black,
	/**
	 * @property {Number} 	[padding=2] 					文字离边框距离（毫米）
	 */ 
	padding: 2,
	 /**
	 * @property {Object}   [text]							文本属性，参照ViewPID.Style.TextStyle定义
	 */
	text: Style.TextStyle
};

/**
 * @class ViewPID.Style.ArcStyle
 * 标准画弧属性定义
 */
 
Style.ArcStyle = {
	/**
	 * @property {Color} 	[color=red] 					弧线颜色，颜色中的alpha值无效
	 */
	color: red,
	/**
	 * @property {Number} 	[angle=120] 					弧线所对应的圆心角：设圆弧所包含的圆心角为A(弧度表示)，凸度=tan(1/4*A)。
	 */
	angle: 120,
	/**
	 * @property {Number} 	[width=0.5] 					弧线宽度：单位：毫米；范围：大于0
	 */
	width: 0.5
};

/**
 * @class ViewPID.Style.PolygonStyle
 * 标准画多边形属性定义 
 */
Style.PolygonStyle = {
	/**
	 * @property {Color}   [color=white] 					多边形颜色
	 */
	color: white,
	/**
	 * @property {Number}  [transparencyValue=1.0] 			多边形透明度,透明度（范围0~1），0为不透明，1为全透明（值：0<=transparencyValue<=1）
	 */
	transparencyValue: 0.0

};
 
module.exports = Style;