"use strict";

var Condicio = require("condicio");

/**
 * @class Error.DataError
 * 数据错误,请求资源文件的数据错误
 */
function DataError(message) {
	/**
	 * @property {String}  [name="DataError"] 	错误名
	 */	
    this.name = "DataError";
	/**
	 * @property {String}  message 				错误信息
	 */    
	this.message = message;
};

/**
 * @class Error.NetworkError
 * 网络错误，请求资源时发生网络错误
 */
function NetworkError(message) {
	/**
	 * @property {String}  [name="NetworkError"] 	错误名
	 */
    this.name = "NetworkError";
	/**
	 * @property {String}  message					错误信息
	 */
    this.message = message;
};

/**
 * @class Error.SystemError
 * 系统错误（因权限无法进行文件I/O，对文件资源的使用被占用、无法删除）
 */
function SystemError(message){
	/**
	 * @property {String}  [name="SystemError"] 	错误名
	 */
	this.name = "SystemError";
	/**
	 * @property {String}  message					错误信息
	 */    
	this.message = message;
};

/**
 * @class Error.InvalidArgumentError
 * 参数错误，VIC与BRS进行资源或对象请求时传入参数错误
 */
function InvalidArgumentError(message){
	/**
	 * @property {String}  [name="InvalidArgumentError"] 	错误名
	 */
	 this.name = "InvalidArgumentError";
	/**
	 * @property {String}  message					错误信息
	 */
	 this.message = message;
};

/**
 * @class Error.VersioningError
 * 版本管理异常，VIC与BRS进行版本协商不通过
 */
function VersioningError(message){
	/**
	 * @property {String}  [name="VersioningError"] 	错误名
	 */
	this.name = "VersioningError";
	/**
	 * @property {String}  message					错误信息
	 */
	this.message = message;
};
 
/** 
 * @ignore
 * @enum {Number} ErrorCode   错误代码
 */
var ErrorCode = {
    E_Data: -10001,
    E_Network: -10002,
	E_InvalidArgument: -10003,
	E_System: -10004,
	E_Versioning: -10005
};

/**
 * @ignore
 * 
 * 包装所有的错误
 */
var genError = function(error){
	if(error.code === ErrorCode.E_Data){
		return new DataError(error.message);
	}
	else if(error.code === ErrorCode.E_Network){
		return new NetworkError(error.message);
	}
	else if (error.code === ErrorCode.E_InvalidArgument){
		return new InvalidArgumentError(error.message);
	}
	else if (error.code === ErrorCode.E_System){
		return new SystemError(error.message);
	}
	else if (error.code === ErrorCode.E_Versioning){
		return new VersioningError(error.message);
	}
	else {
		throw Error("Unknown Error");
	}
 };
 
exports.genError = genError;
exports.DataError = DataError;
exports.NetworkError = NetworkError;
exports.SystemError = SystemError;
exports.VersioningError = VersioningError;
exports.InvalidArgumentError = InvalidArgumentError;

