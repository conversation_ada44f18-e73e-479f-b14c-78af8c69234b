"use strict";

var GLM = require("gl-matrix-double");
/**
 * @ignore
 * 用于一些类型在Json.Stringify之前的一些转化
 */
var cvtForJson  = {
	cvtVec3 : function (vec3) {
		var numberArray = [];
		for (var i = 0; i < 3; ++i) {
			numberArray.push(vec3[i]);
		}
		return numberArray;
	},
	cvtVec4 : function (vec4) {
		var numberArray = [];
		for (var i = 0; i < 4; ++i) {
			numberArray.push(vec4[i]);
		}
		return numberArray;
	},
	cvtVec2 : function (vec2) {
		var numberArray = [];
		for (var i = 0; i < 2; ++i) {
			numberArray.push(vec2[i]);
		}
		return numberArray;
	},
	cvtNodeArray : function (nodes) {
		var nodeIDArray = [];
		for (var i = 0; i < nodes.length; ++i)
		{
			nodeIDArray.push(nodes[i].objectID);
		}
		return nodeIDArray;
	},
	cvtMat4 : function (mat) {
		var matArray = [];
		for (var i=0; i<16; ++i) {
			matArray.push(mat[i]);
		}
		return matArray;
	},
	cvtVec3Array : function (vec3Array) {
		var retVec3Array = [];
		for (var j = 0; j < vec3Array.length; ++j)
		{
			var vec3 = vec3Array[j];
			var numberArray = [];
			for (var i = 0; i < 3; ++i) {
				numberArray.push(vec3[i]);
			}
			retVec3Array.push(numberArray);
		}
		return retVec3Array;
	},
	cvtVec2Array : function(vec2Array){
		var retVec2Array = [];
		for (var i = 0; i < vec2Array.length; ++i) {
			var vec2 = vec2Array[i];
			var numberArray = [];
			for (var j = 0; j < 2; ++j) {
				numberArray.push(vec2[j]);
			}
			retVec2Array.push(numberArray);
		}
		return retVec2Array;
	},
	
	cvtToVec3 : function (numberArray) {
		return GLM.vec3.fromValues(numberArray[0], numberArray[1], numberArray[2]);
	},
	cvtToVec2 : function (numberArray) {
		return GLM.vec2.fromValues(numberArray[0], numberArray[1]);
	},
	cvtToMat4 : function (numberArray) {
		var mat = GLM.mat4.create();
		for (var i=0; i<16; ++i) {
			mat[i] = numberArray[i];
		}
		return mat;
	},
	cvtToVec3Array : function (numbersArray) {
		var vec3Array = [];
		for (var i = 0; i < numbersArray.length; ++i)
		{
			vec3Array.push(GLM.vec3.fromValues(numbersArray[i][0], numbersArray[i][1], numbersArray[i][2]));
		}
		return vec3Array;
	},
	cvtCommandArray : function(commands){
		var commandIDArray = [];
		for(var i = 0; i<commands.length; ++i)
		{
			commandIDArray.push(commands[i].objectID);
		}
		return commandIDArray;
	}
};

module.exports = cvtForJson;