"use strict";

var _ = require("lodash");
var Tracker = require("./Tracker.js");
var MATRIX = require("gl-matrix-double");

/**
 * @class View3D.LineBasedRectTracker
 * @extend Tracker
 * 基于线的矩形tracker
 *
 * 可用于绘制矩形时展现绘制的动态效果
 */
function LineBasedRectTracker(objectID) {
    this.objectID = objectID;
};

LineBasedRectTracker.prototype = _.create(Tracker.prototype, {
	constructor: LineBasedRectTracker
});


 /**
 * 根据起始线和另外一个不在线上的点计算出矩形对应的四个点
 *
 * @param	{Object}	baseLine				矩形的起始边
 * @param  	{vec3} 		baseLine.startPt  		矩形起始边的起点
 * @param  	{vec3} 		baseLine.endPt  		矩形起始边的终点
 * @param 	{vec3} 		point  					不在线上的另外一个点
 *
 * @return {vec3[]}								矩形的四个顶点的点组
 */
LineBasedRectTracker.prototype.calcRectPts = function(baseLine, point){
	var lineEndPt = baseLine.endPt;
	var lineStartPt = baseLine.startPt;
	var startLineDir = MATRIX.vec3.create();
	startLineDir = MATRIX.vec3.sub(startLineDir, lineEndPt, lineStartPt);	//e - s
	startLineDir = MATRIX.vec3.normalize(startLineDir, startLineDir);		
	
	var thirdPt = point;
	var thirdDir = MATRIX.vec3.create();
	thirdDir = MATRIX.vec3.sub(thirdDir, thirdPt, lineEndPt);	//t - s
	var thirdLength = MATRIX.vec3.length(thirdDir);
	thirdDir = MATRIX.vec3.normalize(thirdDir, thirdDir);		
	
	var dot = MATRIX.vec3.dot(startLineDir, thirdDir);			//startLineDir * thirdDir;
	
	var norm = MATRIX.vec3.create();
	norm = MATRIX.vec3.cross(norm, startLineDir, thirdDir);
	
	var offsetDir = MATRIX.vec3.create();
	offsetDir = MATRIX.vec3.cross(offsetDir, norm, startLineDir);
	offsetDir = MATRIX.vec3.normalize(offsetDir, offsetDir);
	var cosa = MATRIX.vec3.dot(thirdDir, offsetDir);
	var offsetLength = thirdLength * cosa;
	var offsetMove = MATRIX.vec3.create();
	offsetMove = MATRIX.vec3.scale(offsetMove, offsetDir, offsetLength);
	
	var rectPts = [];
	rectPts.push(lineStartPt);
	rectPts.push(lineEndPt);
	var movedStartPt = MATRIX.vec3.create();
	movedStartPt = MATRIX.vec3.add(movedStartPt, lineStartPt, offsetMove);
	var movedEndPt = MATRIX.vec3.create();
	movedEndPt = MATRIX.vec3.add(movedEndPt, lineEndPt, offsetMove);
	rectPts.push(movedEndPt);
	rectPts.push(movedStartPt);
	
	return rectPts;
};

module.exports = LineBasedRectTracker;
