"use strict";

var _ = require("lodash"),
	Condicio = require("condicio"),
	COLOR = require("color"),
	jQuery = require("jquery"),
	Effect = require("./Effect.js"),
	Command = require("./Command.js"),
	ParamValidation = require("../ParamValidation.js"),
	CvtForJson = require("../CvtForJson.js"),
	Utility = require("../Utility.js"),
	Style = require("./Style.js");
	
/**
 * @class View3D.LightEffect
 * @extend View3D.Effect
 * 光照效果
 *
 * 创建修改光照的命令
 */
function LightEffect(session, objectID) {
	this.session = session;
    this.objectID = objectID;
};

LightEffect.prototype = _.create(Effect.prototype, {
	constructor: LightEffect
});

/**
 * <a href="https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/75956914">参数相关详情请参考</a>
 * @param  {Object}		lightStyle  					光照基本属性，参照View3D.Style.LightStyle的定义
 * @return {View3D.Command}
 */
LightEffect.prototype.createChangeLightCommand = function(lightStyle){

	Condicio.checkIsObject(lightStyle, "The type of lightStyle must be 'Object'!");
	var newStyle = jQuery.extend({}, Style.LightStyle, lightStyle);	
	ParamValidation.checkIsLightStyle(newStyle);
	
	var lightStyle = {
		diffuse: newStyle.diffuse.rgbaArray(),
		specular: newStyle.specular.rgbaArray(),
		ambient: newStyle.ambient.rgbaArray(),		
		position: CvtForJson.cvtVec4(newStyle.position),
		spotCutoff: newStyle.spotCutoff,
		range: newStyle.range,
		direction: CvtForJson.cvtVec3(newStyle.direction)
	} 
	
	var commandID = Utility.genGUID();
	
	var param = {
		commandID: commandID,
		lightStyle: lightStyle
	}
	
    this.session.request(this.objectID, "createChangeLightCommand", param);
	
	return new Command(commandID);
	
};

module.exports = LightEffect;