
"use strict";

var _ = require("lodash");

var Interaction = require("./Interaction.js");
	

/**
 * @ignore
 * @class PointPickInteraction
 * 点选交互
 */
function PointPickInteraction(objectID, pickHandlerID, pickHanlder) {
	this.objectID = objectID;
	this.pickHanlder = pickHanlder;
	this.pickHandlerID = pickHandlerID;
};

PointPickInteraction.prototype = _.create(Interaction.prototype, {
	constructor: PointPickInteraction
});

PointPickInteraction.prototype.on = function(session){
	session.registerCallback(this.pickHandlerID, this.pickHanlder,"PointPickInteraction", true);
};

PointPickInteraction.prototype.off = function(session){
	session.unregisterCallback(this.pickHandlerID);
};
	

module.exports = PointPickInteraction;
