
"use strict";

var _ = require("lodash");

var Interaction = require("./Interaction.js");
	

/**
 * @ignore
 * @class
 * 鼠标悬停交互
 */
function MouseHoverInteraction(objectID, hoverHandlerID, hoverHandler) {
	this.objectID = objectID;
	this.hoverHandler = hoverHandler;
	this.hoverHandlerID = hoverHandlerID;
};

MouseHoverInteraction.prototype = _.create(Interaction.prototype, {
    constructor: MouseHoverInteraction
});

MouseHoverInteraction.prototype.on = function (session) {
	session.registerCallback(this.hoverHandlerID, this.hoverHandler, "MouseHoverInteraction", true);
};

MouseHoverInteraction.prototype.off = function (session) {
	session.unregisterCallback(this.hoverHandlerID);
};
	

module.exports = MouseHoverInteraction;
