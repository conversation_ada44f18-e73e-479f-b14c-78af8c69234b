"use strict";

var _ = require("lodash");

/**
 * @class View3D.Effect
 * 三维效果
 */
function Effect(objectID) {
    this.objectID = objectID;
};

Effect.prototype = _.create(Effect.prototype, {
	constructor: Effect
});

/**
 * @hide
 * 开启效果
 *
 * @param {Session} session	连接实例
 */
Effect.prototype.on = function(session){ };

/**
 * @hide
 * 结束效果
 *
 * @param {Session} session	连接实例
 */
Effect.prototype.off = function(session){ };

module.exports = Effect;