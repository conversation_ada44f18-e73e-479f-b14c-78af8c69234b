"use strict";

var _ = require("lodash");

/**
 * @class View3D.Interaction
 * 交互基接口
 *
 * 应用不应该构建，应该通过InteractionFactory.createXXInteraction创建得到
 * @abstract
 */
function Interaction(objectID) {
	this.objectID = objectID;
};

/**
 * @hide
 * 开启交互
 *
 * @param {Session} session	连接实例
 */
Interaction.prototype.on = function(session){ };

/**
 * @hide
 * 结束交互
 *
 * @param {Session} session	连接实例
 */
Interaction.prototype.off = function(session){ };

module.exports = Interaction;