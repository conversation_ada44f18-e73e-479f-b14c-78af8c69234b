var COLOR = require("color");
var Matrix = require("gl-matrix-double");
	
var black = COLOR('rgb(0, 0, 0)').alpha(1);
var white = COLOR('rgb(255, 255, 255)');
var yellow = COLOR('rgb(255, 255, 0)');
var red = COLOR('rgb(255, 0, 0)');

/**
 * @class View3D.Style
 * 标准属性定义
 */
var Style = { };
 
/**
 * @class View3D.Style.LineStyle
 * 标准线属性定义 
 */
Style.LineStyle = {
	/**
	 * @property {Color}  [color=black] 	线色，颜色中的alpha值无效
	 */
	color: black,
	/**
	 * @property {Number} [width=2] 		线宽（像素）范围（1 ~ 10）
	 */
	width: 2,
    /**
	 * @property {String} [type="solid"]	线型
	 *
	 *	- "solid" 	实线
	 *  - "stipple"	虚线
	 */
	type: 'solid'
};

/**
 * @class View3D.Style.BorderStyle
 * 标准边框属性定义 
 */
Style.BorderStyle = {
	/**
	 * @property {Color}  [color=black] 	边框色，颜色中的alpha值有效
	 */
	color: black,
	/**
	 * @property {Number} [width=3] 		边框宽（像素）范围（1 ~ 10）
	 */
	width: 3,
};

/**
 * @class View3D.Style.TextStyle
 * 标准文本属性定义 
 */
Style.TextStyle = {
	/**
	 * @property {Color}  [color=black] 		字体色，颜色中的alpha值无效
	 */
	color: black,
	/**
	 * @property {Number} [size=15] 			字体大小（像素）,定义的是文字高度，宽度自动调整	
	 */
	size: 15,
    /**
	 * @property {String} [alignment="left"]	多行文本之间对齐方式
	 *
	 *	- "left" 	左对齐
	 *  - "center"	居中对齐
	 *  - "right"   右对齐
	 */
	alignment: 'left',
	/**
	 * @property {String} [fontName=""]			字体名，设置为空，默认为楷体：simkai（该名与当前操作系统（c:\windows\fonts）下的字体名对应，若不满足时，可以安装新的字体）
	 */
	fontName: "",
	/**
	 * @property {Number} [backdropType]		字体的阴影效果，取值范围[0,8]（<a href="https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/75957780/VIC+Style">具体效果详见</a>）
	 */
	backdropType: -1,
	/**
	 * @property {Color}  [backdropColor=black]	阴影效果的颜色，颜色中的alpha值无效
	 */
	backdropColor: black,
	/**
	 * @property {Number}  [lineSpacing=0]	行间距，以字符高度的百分比表示；建议值为25％（即，将行距设置为0.25）
	 */
	lineSpacing: 0
};

/**
 * @class View3D.Style.TextFrameStyle
 * 标准文本框属性定义 
 */
Style.TextFrameStyle = {
	/**
	 * @property {String}  		[shape="rectangle"] 	文本框外形
	 *
	 * rectangle	矩形框
	 * circle 		圆形框
	 */
	shape: "rectangle",
	/**
	 * @property {Color}  		[fillColor=white] 	填充色，颜色中的alpha值有效
	 */
	fillColor: white,
	/**
	 * @property {Number} 		[padding=2] 	文字离边框距离（像素）
	 */
	padding: 2,
    /**
	 * @property {Object} 		[border]		边框属性，参照View3D.Style.BorderStyle定义
	 */
	border: Style.BorderStyle,
    /**
	 * @property {Object} 		[text]			文本属性，参照View3D.Style.TextStyle定义
	 */
	text: Style.TextStyle,
};

/**
 * @class View3D.Style.IdentifyLocationPoint
 * 标准起始点属性定义 
 */
Style.IdentifyLocationPoint = {
	/**
	 * @property {Color}  [color=black] 	点颜色，颜色中的alpha值无效
	 */
	color: black,
	/**
	 * @property {Number} [ radius=0] 		点的半径
	 */
	radius: 0,
};

/**
 * @class View3D.Style.PointerStyle
 * 线及相关点属性定义 
 */
Style.PointerStyle = {
	/**
	 * @property {Object}  	[line] 						指向线属性，参照View3D.Style.LineStyle的定义
	 */
	line: Style.LineStyle,
	/**
	 * @property {Object}  	[identifyLocationPoint] 		位置标识点属性，参照View3D.Style.IdentifyLocationPoint的定义
	 */
	identifyLocationPoint: Style.IdentifyLocationPoint,
};

/**
 * @class View3D.Style.TextLabelStyle
 * 标准文本标签属性定义 
 */
Style.TextLabelStyle = {
	/**
	 * @property {Object}  	[pointer] 						指向线及点的属性，参照View3D.Style.PointerStyle的定义
	 */
	pointer: Style.PointerStyle,
	/**
	 * @property {Object}	[textFrame] 					文本框属性，参照View3D.Style.TextFrameStyle的定义
	 */
	textFrame: Style.TextFrameStyle,
    /**
	 * @property {Object} 	[connectPosition]				线与边框连接位置
	 * @property {String} 	[connectPosition.x="center"]	线与边框连接位置
	 *
	 * - "left" 	左
	 * - "center" 	中
	 * - "right"   	右
	 * @property {String} 	[connectPosition.y="bottom"]	垂直连接位置
	 *
	 * - "bottom" 	下
	 * - "middle" 	中
	 * - "top"   	上
	 */
	connectPosition: {
		x: "center",
		y: "bottom"
	},
	/** @property {Object} 	[fixSizeRange]					文本标签固定大小的显示范围，没有量纲，max应该大于min
	 *  文本标签在该范围内能够维持固定大小，否则不维持屏幕占比
	 * 
	 *	例如：fixSizeRange{0, 10}，即文本标签固定大小的显示范围为0-10
	 *
	 *	@property {Number} 	[fixSizeRange.min = 0]			最小值（取值范围[0,+∞)），取0代表文本会随着视点距离的靠近会无限缩小，一直维持在屏幕显示的大小不变
	 *  @property {Number} 	[fixSizeRange.max = 3.4E38]		最大值（取值范围(0,+∞)），默认值表示最大放大倍数为3.4E38倍，超过该倍数无法维持占屏幕大小不变
	 * 
	 */
	fixSizeRange:{											
		min: 0,
		max: 3.402823466 * Math.pow(10,38)
	},
	/**
	 *	@property {Boolean}	[enableOvershadow = false]				文本标签是否需要一直显示不会被遮挡
	 *	如果该值设置为false，那么文本标注会在所有模型之前显示，永远不会遮挡
	 *  如果该值设置为true，从看得视角来说该文本标注会被在它之前的模型挡住，转动视角该看见的时候也会看见，且标注是一直面向屏幕的
	 */
	enableOvershadow: false
};

/**
 * @class View3D.Style.PictureLabelStyle
 * 标准图片标签属性定义 
 */
 Style.PictureLabelStyle = {
	/** @property {Object} 	[fixSizeRange]					图像标签固定大小的显示范围，没有量纲，max应该大于min
	 *  图片在该范围内能够维持固定大小，否则不维持屏幕占比
	 *  
	 *	例如：fixSizeRange{0, 10}，即图像标签固定大小的显示范围为0-10
	 *  
	 *	@property {Number} 	[fixSizeRange.min = 0]			最小值（取值范围[0,+∞)），取0代表图片会随着视点距离的靠近会无限缩小，一直维持在屏幕显示的大小不变
	 *  @property {Number} 	[fixSizeRange.max = 40.0]		最大值（取值范围(0,+∞)），默认值表示最大放大倍数为40倍，超过该倍数无法维持占屏幕大小不变 
	 * 
	 */
	fixSizeRange:{											
		min: 0,
		max: 40.0
	},
	/**
	 *  @property {Number} [width = 48]		图像标签的宽度,高度大小会根据图像高宽比自动调整
	*/
	width: 48
};

/**
 * @class View3D.Style.PointStyle
 * 标准点属性定义 
 */
Style.PointStyle = {
	/**
	 * @property {Color}  [color=black] 	点颜色，颜色中的alpha值无效
	 */
	color: black,
	/**
	 * @property {Number} [size=4] 		    点大小（像素）范围（大于0）
	 */
	size: 4,
};



/**
 * @class View3D.Style.LightStyle
 * 光属性定义  * <a href="https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/75956914">参数相关详情请参考</a>
 */
 Style.LightStyle = {	 
	/**
	 * @property {Color}  [ambient=black] 	  					环境光颜色
	 */
	ambient: black,
	/** 
	 * @property {Color}  [diffuse=black] 	  					镜面光颜色
	 */
    diffuse: black,
	/**
	 * @property {Color}  [specular=black] 	 					漫射光颜色
	 */
	specular: black,
	/**
	 * @property {Number} [spotCutoff=180]      				聚光灯聚光角度
	 */
	spotCutoff: 180,
	/**
	 * @property {vec4}   [position]							光源位置（vec4的最后一位决定使用的是哪种光源）
	 */
	position: Matrix.vec4.fromValues(0,0,1,0),
	/**	
	 * @property {vec3}   [direction]							光照方向
	 */	
	direction: Matrix.vec3.fromValues(0,0,-1),
	/**
	 * @property {Number}   [range]								光照范围
	 */	
	range: 0,
	

 };

 /**
 * @class View3D.Style.AxisStyle
 * 轴属性定义 
 */
Style.AxisStyle = {
	/**
	 * @property {Number} [length=150]				长（像素）
	 */
	length: 150,
	/**
	 * @property {Number} [width=3] 				轴宽（像素）
	 */
	width: 3,
	/**
	 * @property {Color}  [color=red] 				颜色，颜色中的alpha值无效
	 */
	color: red,
    /**
	 * @property {Number} [pickedRadius=6]			轴可被选中半径（像素）
	 */
	pickedRadius: 6,
	/**
	 * @property {Color}  [pickedColor=yellow]		轴被选中后的颜色，颜色中的alpha值无效
	 *
	 */
	pickedColor: yellow,
};

module.exports = Style;