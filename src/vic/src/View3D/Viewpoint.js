"use strict";

var Condicio = require("condicio"),
	Matrix = require("gl-matrix-double"),
	ParamValidation = require("../ParamValidation.js");



/**
 * @class View3D.Viewpoint
 * <a href="https://rzon.atlassian.net/wiki/spaces/RenderingTech/pages/75956834">视角</a>
 *
 * @constructor						创建一个新视点
 * @param {vec3} eye 				眼睛的位置
 * @param {vec3} center 			眼睛所看到物体的中心位置
 * @param {vec3} [up = vec3(0,0,1)] 视角正方向
 */
var Viewpoint = function(eye, center, up) {
	ParamValidation.checkIsVec3(eye, "The type of eye must be 'vec3' and valid");
	ParamValidation.checkIsVec3(center, "The type of center must be 'vec3' and valid");
	
	/**
	 * @property {vec3} eye 眼睛的位置
	 */
	this.eye = eye;
	/**
	 * @property {vec3} center 眼睛所看到物体的中心位置
	 */
	this.center = center;
	
	/**
	 * @property {vec3} up 眼睛的正方向
	 */
	if(Condicio.isUndefined(up))
		up = Matrix.vec3(0,0,1);
	else
	    ParamValidation.checkIsVec3(up, "The type of up must be 'vec3' and valid");
	this.up = up;
};

module.exports = Viewpoint;