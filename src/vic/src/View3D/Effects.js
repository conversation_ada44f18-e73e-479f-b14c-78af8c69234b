"use strict";

var _ = require("lodash"),
	Condicio = require("condicio"),
	Effect = require("./Effect.js");

/**
 * @class View3D.ClipEffect
 * @extend  View3D.Effect
 * 剖切效果
 *
*/
function ClipEffect(objectID, pickHandlerID, pickHandler) {
	this.objectID = objectID;
	this.pickHandler = pickHandler;
	this.pickHandlerID = pickHandlerID;
};

ClipEffect.prototype = _.create(Effect.prototype, {
	constructor: ClipEffect
});

ClipEffect.prototype.on = function(session){
	session.registerCallback(this.pickHandlerID, this.pickHandler, "", true);
};

ClipEffect.prototype.off = function(session){
	session.unregisterCallback(this.pickHandlerID);
};

/**
 * @class View3D.DraggableEffect
 * @extend View3D.Effect
 * 拖拽移动效果
 *
 */
function DraggableEffect(objectID, dragger) {
	this.objectID = objectID;
	this.dragger = dragger;
};

DraggableEffect.prototype = _.create(Effect.prototype, {
	constructor: DraggableEffect
});

DraggableEffect.prototype.on = function(session){
	this.dragger.on(session);
};

DraggableEffect.prototype.off = function(session){
	this.dragger.off(session);
};	

exports.ClipEffect = ClipEffect;
exports.DraggableEffect = DraggableEffect;