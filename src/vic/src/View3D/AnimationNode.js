"use strict";

var _ = require("lodash"),
	Condicio = require("condicio"),
	Node = require("./Node.js"),
	Command = require("./Command.js"),
	Utility = require("../Utility.js"),
	jQuery = require("jquery");

/**
 * @class View3D.AnimationNode
 * @extend Node
 * 动画节点
 *
 */
function AnimationNode(session, objectID) {
	this.session = session;
    this.objectID = objectID;
};

AnimationNode.prototype = _.create(Node.prototype, {
	constructor: AnimationNode
});

/**
 * 创建播放动画命令
 *
 * @param {String} animationName  				需要播放的动画名称
 * @param {Object} [playOption]  				播放选项
 * @param {Number} [playOption.priority = 0]  	播放选项，优先级（取值范围0~7）
 * @param {Number} [playOption.weight = 1.0]  			播放选项，动画重量感（取值范围>0）
 *
 * @return {View3D.Command}
 */
AnimationNode.prototype.createPlayAnimationCommand = function(animationName, playOption){

	var defaultOption = {
		priority: 0,
		weight: 1.0
	};
	var newOption = jQuery.extend(true, {}, defaultOption, playOption);
	
	Condicio.checkIsString(animationName, "The type of animationName must be 'String'!");
	Condicio.checkIsObject(newOption, "The type of playOption must be 'Object'!");
	Condicio.checkIsNumber(newOption.priority, "The type of playOption-priority must be 'Number'!");
    Condicio.checkArgument(newOption.priority >= 0 && newOption.priority <= 7, "playOption-priority must: >= 0 && <= 7 !");
	Condicio.checkIsNumber(newOption.weight, "The type of playOption-weight must be 'Number'!");
	Condicio.checkArgument(newOption.weight > 0, "playOption-weight must: > 0!");
	
    var commandID = Utility.genGUID();
	var params = {
		commandID: commandID,
		animationName: animationName,
		priority: newOption.priority,
		weight: newOption.weight
	};
    this.session.request(this.objectID, "createPlayAnimationCommand", params);
	
	return new Command(commandID);
};

/**
 * 创建停止动画命令
 *
 * @param {String} animationName  			需要停止的动画名称
 *
 * @return {View3D.Command}
 */
AnimationNode.prototype.createStopAnimationCommand = function(animationName){
	Condicio.checkIsString(animationName, "The type of animationName must be 'String'!");
	
    var commandID = Utility.genGUID();
	var params = {
		commandID: commandID,
		animationName: animationName
	};
    this.session.request(this.objectID, "createStopAnimationCommand", params);
	
	return new Command(commandID);
};

/**
 * 创建设置动画播放模式命令
 *
 * @param {String} animationName  			需要修改播放模式的动画名称
 * @param {String} playMode  				动画播放模式
 * - "once" 	播放一次，完成之后恢复原状
 * - "stay" 	播放一次，完成之后保持最后的状态
 * - "loop"   	循环播放
 * - "ppong"   	正反依次循环播放
 *
 * @return {View3D.Command}
 */
AnimationNode.prototype.createSetPlayModeCommand = function(animationName, playMode){
	Condicio.checkIsString(animationName, "The type of animationName must be 'String'!");
	Condicio.checkIsString(playMode, "The type of playMode must be 'String'!");
	
    var commandID = Utility.genGUID();
	var params = {
		commandID: commandID,
		animationName: animationName,
		playMode: playMode
	};
    this.session.request(this.objectID, "createSetPlayModeCommand", params);
	
	return new Command(commandID);
};

module.exports = AnimationNode;