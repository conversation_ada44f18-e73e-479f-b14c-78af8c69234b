
"use strict";

var _ = require("lodash");

var Interaction = require("./Interaction.js");
	

/**
 * @ignore
 * @class
 * 复合交互
 */
function CompositeInteraction(objectID, interactions) {
	this.objectID = objectID;
	this.subInteractions = interactions;
};

CompositeInteraction.prototype = _.create(Interaction.prototype, {
    constructor: CompositeInteraction
});

CompositeInteraction.prototype.on = function (session) {
    for (var i = 0; i < this.subInteractions.length; ++i) {
        this.subInteractions[i].on(session);
    }
};

CompositeInteraction.prototype.off = function (session) {
    for (var i = 0; i < this.subInteractions.length; ++i) {
        this.subInteractions[i].off(session);
    }
};
	

module.exports = CompositeInteraction;
