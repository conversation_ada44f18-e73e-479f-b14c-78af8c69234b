"use strict";

var _ = require("lodash"),
	Condicio = require("condicio"),
	Effect = require("./Effect.js"),
	Command = require("./Command.js"),
	Utility = require("../Utility.js");

/**
 * @class View3D.TransparencyEffect
 * @extend View3D.Effect
 * 透明效果
 *
 * 创建修改透明度的命令
 */
function TransparencyEffect(session, objectID) {
	this.session = session;
    this.objectID = objectID;
};

TransparencyEffect.prototype = _.create(Effect.prototype, {
	constructor: TransparencyEffect
});

/**
* 创建改变透明度的命令
* 注意：6.0.0起修正了透明度逻辑，修正后透明度参数（取值范围0~1）值越大，越透明
* @param {Number} value  设置透明度参数（取值范围0~1）0为不透明，1为全透明(值：0 <= value <= 1）
 *
 * @return {View3D.Command}
 */
TransparencyEffect.prototype.createChangeTransparencyCommand = function(value){
	Condicio.checkIsNumber(value, "The type of value must be 'number'!");
    Condicio.checkArgument(value >= 0 && value <= 1, "The Value must: >= 0 && <= 1 !");
	
    var commandID = Utility.genGUID();
	var params = {
		commandID: commandID,
		value: value
	};
    this.session.request(this.objectID, "createChangeTransparencyCommand", params);
	
	return new Command(commandID);
	
};

module.exports = TransparencyEffect;