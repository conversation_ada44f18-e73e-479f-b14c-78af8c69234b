"use strict";

var jQuery = require("jquery");
var MouseEvent = require("./MouseEvent.js");

/**
 * @ignore
 * 画布
 */
function ImageDisplay(session, viewID, parentElem, enableSSL, proxyUri) {
    var _this = this;
	
	this.parentElem = parentElem;
	this.displayElem = document.createElement('div');
	this.parentElem.appendChild(this.displayElem);
	jQuery(this.displayElem).attr("id","bcd");
	this.displayElem.style.height = "100%";
	
	_this.continueRequest = true;
	_this.continueRefresh = false;
	
	this.width = 0;
	this.height = 0;
	// 处理大小变化
    var resizeListener = function () {
		if(!_this.continueRequest){
			return;
		}
		
        var w = jQuery(parentElem).width();
        var h = jQuery(parentElem).height();

        if (_this.width !== w || _this.height !== h) {
            _this.width = w;
            _this.height = h;
			_this.continueRefresh = true;
		}
		else{
			if(_this.continueRefresh){
				_this.continueRefresh = false;
				session.request(viewID, "resize", { width: w, height: h });
			}
		}

        setTimeout(resizeListener, 20);
    }
    resizeListener();
	
	MouseEvent(session, viewID, this.displayElem);
	
	var defaultProtocol = "http://";
	if(enableSSL)
		defaultProtocol = "https://";
	
	var hasProxy = function(){
		if(proxyUri.length !== 0){
			return true;
		}
		
		return false;
	}
	
	var uri = "";
	if(hasProxy()){
		uri = "/brs/image/" + proxyUri;
	}
	
    var url = defaultProtocol + session.ip + ":" + session.port + uri + "?rendererID=" + session.rendererID + "&viewID=" + viewID;
	
	// 处理图片不让选中，避免引起闪屏
	this.displayElem.onselectstart = function(){ return false; };
	
	// 创建两张图片，同一时间一个图片请求新图片，另外一个图片显示
	var backStageImage = new Image;
	var forgroundStageImage = new Image;
	session.addNetworkCloseListener(function(){
		_this.continueRequest = false;
	});
	
	this.displayElem.appendChild(backStageImage);
	this.displayElem.appendChild(forgroundStageImage);
	
	//阻止默认事件，保证不影响上层事件，目的是保证最上层能正确获取鼠标消息
	function mouseEvent(e){
		e.preventDefault();	
	};
		
	jQuery(backStageImage).on('mousedown', mouseEvent);
	jQuery(forgroundStageImage).on('mousedown', mouseEvent);
	
	forgroundStageImage.style.width = "100%";
	forgroundStageImage.style.height = "100%";	
	backStageImage.style.width = "100%";
	backStageImage.style.height = "100%";
	
	forgroundStageImage.style.display = "block";
	backStageImage.style.display = "none";
	
	// 处理图片显示
	var swapImageShow = function(){
		var temp = backStageImage;
		backStageImage = forgroundStageImage;
		forgroundStageImage = temp;
		
		backStageImage.style.display = "none";
		forgroundStageImage.style.display = "block";
	};
	var imageLoadEventHandler = function () {
		swapImageShow();
		if(_this.continueRequest)
			loadImage();
	};
	var imageErrorEventHandler = function () {
		if(_this.continueRequest)
			loadImage();
	};
	jQuery(forgroundStageImage)
		.on('abort', loadImage) //因img在响应Esc按键之后，会断开Http连接，所以在abort的时候直接加载img就不会出现卡住img的现象
		.on('load', imageLoadEventHandler)
		.on('error',  imageErrorEventHandler);
	jQuery(backStageImage)
		.on('abort', loadImage)
		.on('load', imageLoadEventHandler)
		.on('error',  imageErrorEventHandler);
	function loadImage() {
        var d = new Date();
        backStageImage.src = url + '&t=' + d.getTime();
    }
	backStageImage.src = "data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQImWNgYGBgAAAABQABh6FO1AAAAABJRU5ErkJggg==";
	
};

ImageDisplay.prototype.release = function(){
	this.continueRequest = false;
	this.parentElem.removeChild(this.displayElem);
};

module.exports = ImageDisplay;