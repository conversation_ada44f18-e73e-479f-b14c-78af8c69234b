"use strict";

var Condicio = require("condicio");

/**
 * @ignore
 * 网络通信协议
 *
 */

var PackageType = {
	Request: 0,
    Response: 1,
    SessionReady: 2,
	Error: 3,
	Ping: 4,
	Pong: 5
};

var VersionNumber = {
	CurrentVIC: "6.0.0",
	SupportBRSMinimum: "5.12.0"
};

function Package(type, body) {
    if (Condicio.isUndefined(type))
        this.type = null;
    else
        this.type = type;

    if (Condicio.isUndefined(body))
        this.body = null;
    else
        this.body = body;
};

Package.prototype.fromString = function (data) {
    var obj = JSON.parse(data);

    this.type = obj.type;
    this.body = obj.body;
};

Package.prototype.toString = function () {
    var obj = new Object;
    obj.type = this.type;

    if (this.body)
        obj.body = this.body;

    return JSON.stringify(obj);
};


exports.PackageType = PackageType;
exports.VersionNumber = VersionNumber;
exports.Package = Package;




